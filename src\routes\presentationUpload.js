import express from "express";
import multer from "multer";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { fileURLToPath } from "url";
import { dirname } from "path";
import auth from "../middleware/auth.js";
import {
  uploadPresentationForAgenda,
  createAgendaItemsFromPresentation
} from "../controllers/agendaController.js";

const router = express.Router({ mergeParams: true });

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configure multer for presentation uploads
const presentationStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, "../../uploads/"));
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// File filter to allow only PDF and images
const presentationFileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/pdf',                                   // .pdf
    'image/jpeg',                                        // For JPEG images
    'image/png',                                         // For PNG images
  ];
  
  // Allowed file extensions
  const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png'];
  // Check if file type is allowed or has an allowed extension
  if (allowedTypes.includes(file.mimetype) || 
      allowedExtensions.some(ext => file.originalname.toLowerCase().endsWith(ext))) {
    console.log("File accepted:", file.originalname, file.mimetype);
    cb(null, true);
  } else {
    console.log("File rejected:", file.originalname, file.mimetype);
    cb(new Error("Only PDF, JPG, or PNG files are allowed"), false);
  }
};

const uploadPresentation = multer({
  storage: presentationStorage,
  limits: {
    fileSize: 20 * 1024 * 1024 // 20MB limit for presentations
  },
  fileFilter: presentationFileFilter
});

// Error handling middleware for multer errors
const handleMulterError = (err, req, res, next) => {
  if (err) {
    console.error("Multer error:", err.message);
    return res.status(400).json({ message: err.message });
  }
  next();
};

// Upload presentation for agenda extraction
router.post("/upload", auth, uploadPresentation.single("file"), handleMulterError, uploadPresentationForAgenda);

// Create agenda items from presentation
router.post("/from-presentation", auth, createAgendaItemsFromPresentation);

export default router;

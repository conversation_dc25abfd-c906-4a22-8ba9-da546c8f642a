import { Committee, Meeting, Task } from "@/types";
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import { useEffect, useState } from "react";

interface TaskPdfDocumentProps {
  meeting: Meeting;
}

interface PdfData {
  committee: Committee | string;
  meetingData: {
    title: string;
    startDate: string;
  };
  tasks: Task[];
}

const TaskPdfDocument = ({ meeting }: TaskPdfDocumentProps) => {
  const [pdfData, setPdfData] = useState<PdfData>({
    committee: '',
    meetingData: { title: '', startDate: '' },
    tasks: [],
  });

  useEffect(() => {
    if (meeting) {
      setPdfData({
        committee: meeting.committee,
        meetingData: {
          title: meeting.title,
          startDate: meeting.startDate,
        },
        tasks: meeting.tasks ?? [],
      });
    }
  }, [meeting]);

  const styles = StyleSheet.create({
    page: { padding: 40, fontSize: 12, fontFamily: 'Helvetica' },
    heading: {
      fontSize: 24,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: 30,
    },
    subHeading: {
      fontSize: 16,
      fontWeight: 'bold',
      marginBottom: 10,
    },
    section: {
      marginBottom: 20,
    },
    fieldGroup: {
      flexDirection: 'row',
      marginBottom: 6,
    },
    label: {
      width: 140,
      fontWeight: 'bold',
      color: '#333',
    },
    value: {
      flex: 1,
      color: '#555',
    },
    taskTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 10,
    },
    taskDescription: {
      fontSize: 12,
      marginBottom: 20,
      lineHeight: 1.5,
    },
  });

  const Field = ({ label, value }: { label: string; value: string }) => (
    <View style={styles.fieldGroup}>
      <Text style={styles.label}>{label}:</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  );

  return (
    <Document>
      <Page style={styles.page}>
        <Text style={styles.heading}>Task Report Summary</Text>

        {typeof pdfData.committee === 'object' && pdfData.committee !== null && (
          <View style={styles.section}>
            <Text style={styles.subHeading}>Committee Details</Text>
            <Field label="Committee" value={pdfData.committee.name ?? 'N/A'} />
            <Field label="Description" value={pdfData.committee.description ?? 'N/A'} />
            <Field label="Members" value={pdfData.committee.members.map((m: any) => m.name).join(', ') || 'N/A'} />
            <Field label="Member Secretary" value={pdfData.committee.assistant?.name ?? 'N/A'} />
            <Field
              label="Department"
              value={
                typeof pdfData.committee.department === 'object' && pdfData.committee.department !== null
                  ? pdfData.committee.department.name ?? 'N/A'
                  : 'N/A'
              }
            />
            <Field label="Meeting Frequency" value={pdfData.committee.meetingFrequency ?? 'N/A'} />
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.subHeading}>Meeting Details</Text>
          <Field label="Title" value={pdfData.meetingData.title ?? 'N/A'} />
          <Field
            label="Start Date"
            value={
              pdfData.meetingData.startDate
                ? new Date(pdfData.meetingData.startDate).toLocaleDateString()
                : 'N/A'
            }
          />
        </View>
      </Page>


      {pdfData.tasks.map((task, index) => (
        <Page key={task._id} style={styles.page}>
          <Text style={styles.subHeading}>Task {index + 1}</Text>
          <Text style={styles.taskTitle}>{task.title}</Text>
          <Text style={styles.taskDescription}>{task.description}</Text>

          <Field label="Priority" value={task.priority ?? 'N/A'} />
          <Field label="Status" value={task.status ?? 'N/A'} />
          <Field
            label="Assigned To"
            value={task.assignedTo?.map((u: any) => u.name).join(', ') ?? 'N/A'}
          />
          <Field
            label="Deadline"
            value={task.deadline ? new Date(task.deadline).toLocaleDateString() : 'N/A'}
          />
        </Page>
      ))}

    </Document>
  );
};

export default TaskPdfDocument;

import mongoose from "mongoose";

const taskSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  priority: {
    type: String,
    enum: ["low", "medium", "high"],
    default: "medium"
  },
  status: {
    type: String,
    enum: ["pending", "completed"],
    default: "pending"
  },
  meeting: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Meeting",
    required: true
  },
  assignedTo: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: "Directory"
  }],
  imageUrl: {
    type: String
  },
  originalText: {
    type: String
  },
  deadline: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

const Task = mongoose.model("Task", taskSchema);
export default Task;

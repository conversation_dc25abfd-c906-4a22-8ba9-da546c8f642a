import express from "express";
import auth from "../middleware/auth.js";
import {
  getCommittees,
  getCommittee,
  createCommittee,
  updateCommittee,
  deleteCommittee,
  getCommitteeMeetings,
  createCommitteeMeeting,
  deleteCommitteeMeeting,
  getCommitteeMembers
} from "../controllers/committeeController.js";

const router = express.Router();

router.get("/", auth, getCommittees);
router.get("/:id/members", auth, getCommitteeMembers);
router.get("/:id/meetings", auth, getCommitteeMeetings);
router.get("/:id", auth, getCommittee);
router.post("/:id/meetings", auth, createCommitteeMeeting);
router.delete("/:id/meetings/:meetingId", auth, deleteCommitteeMeeting);
router.post("/", auth, createCommittee);
router.patch("/:id", auth, updateCommittee);
router.delete("/:id", auth, deleteCommittee);

export default router;

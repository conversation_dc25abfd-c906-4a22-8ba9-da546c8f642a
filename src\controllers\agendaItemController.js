import AgendaItem from "../models/agendaItem.js";
import Meeting from "../models/meeting.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import archiver from "archiver";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define uploads path
const UPLOADS_PATH = path.join(__dirname, "../../uploads");

// Create uploads directory if it doesn't exist
if (!fs.existsSync(UPLOADS_PATH)) {
  fs.mkdirSync(UPLOADS_PATH, { recursive: true });
}

/**
 * Get all agenda items for a meeting
 * @route GET /api/meetings/:meetingId/agenda
 * @access Private
 */
export const getAgendaItems = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { sortBy = "order", sortOrder = "asc" } = req.query;
    
    // Create sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;    const agendaItems = await AgendaItem.find({ meeting: meetingId })
      .sort(sort)
      .populate("createdBy", "email name");
    
    res.json(agendaItems);
  } catch (error) {
    console.error("Error getting agenda items:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Get a single agenda item
 * @route GET /api/meetings/:meetingId/agenda/:id
 * @access Private
 */
export const getAgendaItem = async (req, res) => {
  try {    const agendaItem = await AgendaItem.findById(req.params.id)
      .populate("createdBy", "email name");
    
    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    res.json(agendaItem);
  } catch (error) {
    console.error("Error getting agenda item:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Create a new agenda item
 * @route POST /api/meetings/:meetingId/agenda
 * @access Private
 */
export const createAgendaItem = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { 
      title, 
      description, 
      order 
    } = req.body;
    
    // Check if meeting exists
    const meeting = await Meeting.findById(meetingId);
    if (!meeting) {
      return res.status(404).json({ message: "Meeting not found" });
    }
    
    // Get the last order number if order is not specified
    let newOrder = order;
    if (newOrder === undefined) {
      const lastAgendaItem = await AgendaItem.findOne({ meeting: meetingId })
        .sort({ order: -1 });
      newOrder = lastAgendaItem ? lastAgendaItem.order + 1 : 0;
    }
    
    const newAgendaItem = new AgendaItem({
      title,
      description,
      meeting: meetingId,
      order: newOrder,
      createdBy: req.user.id
    });
    
    const savedAgendaItem = await newAgendaItem.save();
    res.status(201).json(savedAgendaItem);
  } catch (error) {
    console.error("Error creating agenda item:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Update an agenda item
 * @route PUT /api/meetings/:meetingId/agenda/:id
 * @access Private
 */
export const updateAgendaItem = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      description, 
      order
    } = req.body;
    
    const agendaItem = await AgendaItem.findById(id);
    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    // Update fields
    if (title !== undefined) agendaItem.title = title;
    if (description !== undefined) agendaItem.description = description;
    if (order !== undefined) agendaItem.order = order;
    
    const updatedAgendaItem = await agendaItem.save();
    res.json(updatedAgendaItem);
  } catch (error) {
    console.error("Error updating agenda item:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Delete an agenda item
 * @route DELETE /api/meetings/:meetingId/agenda/:id
 * @access Private
 */
export const deleteAgendaItem = async (req, res) => {
  try {
    const { id } = req.params;
    
    const agendaItem = await AgendaItem.findById(id);
    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    await agendaItem.deleteOne();
    res.json({ message: "Agenda item removed" });
  } catch (error) {
    console.error("Error deleting agenda item:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Reorder agenda items
 * @route PUT /api/meetings/:meetingId/agenda/reorder
 * @access Private
 */
export const reorderAgendaItems = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { items } = req.body;
    
    if (!Array.isArray(items)) {
      return res.status(400).json({ message: "Items must be an array" });
    }
    
    // Update each item's order in a batch
    const updates = items.map(item => ({
      updateOne: {
        filter: { _id: item.id, meeting: meetingId },
        update: { $set: { order: item.order } }
      }
    }));
    
    await AgendaItem.bulkWrite(updates);
    
    // Return updated list
    const updatedItems = await AgendaItem.find({ meeting: meetingId })
      .sort({ order: 1 });
      
    res.json(updatedItems);
  } catch (error) {
    console.error("Error reordering agenda items:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Upload files for an agenda item
 * @route POST /api/meetings/:meetingId/agenda/:id/files
 * @access Private
 */
export const uploadFiles = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log("File upload request received for agenda item:", id);
    console.log("Request body:", req.body);
    console.log("Request files:", req.files);
    
    // Check if files are included
    if (!req.files || req.files.length === 0) {
      console.log("No files found in the request");
      return res.status(400).json({ message: "No files were uploaded" });
    }
    
    const agendaItem = await AgendaItem.findById(id);
    if (!agendaItem) {
      console.log("Agenda item not found:", id);
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    console.log("Found agenda item:", agendaItem._id);
      // Format files for storage in the database
    const fileData = req.files.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      path: file.path,
      mimetype: file.mimetype,
      size: file.size,
      uploadedAt: new Date()
    }));
    
    console.log("Files to be added:", fileData);
    
    // Add files to agenda item
    if (!agendaItem.files) {
      agendaItem.files = [];
    }
    agendaItem.files.push(...fileData);
    
    const updatedAgendaItem = await agendaItem.save();
    res.json(updatedAgendaItem);
  } catch (error) {
    console.error("Error uploading files:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Download a single file from an agenda item
 * @route GET /api/meetings/:meetingId/agenda/:id/files/:fileId
 * @access Private
 */
export const downloadFile = async (req, res) => {
  try {
    const { id, fileId } = req.params;
    
    const agendaItem = await AgendaItem.findById(id);
    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    // Find the file in the agenda item
    const file = agendaItem.files.find(f => f._id.toString() === fileId);
    if (!file) {
      return res.status(404).json({ message: "File not found" });
    }
    
    // Check if file exists on disk
    const filePath = file.path;
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: "File not found on server" });
    }
    
    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${file.originalname}"`);
    res.setHeader('Content-Type', file.mimetype);
    
    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error("Error downloading file:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Download all files from an agenda item as a ZIP archive
 * @route GET /api/meetings/:meetingId/agenda/:id/files/download-zip
 * @access Private
 */
export const downloadAllFiles = async (req, res) => {
  try {
    const { id } = req.params;
    
    const agendaItem = await AgendaItem.findById(id);
    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    // Check if agenda item has files
    if (!agendaItem.files || agendaItem.files.length === 0) {
      return res.status(404).json({ message: "No files found for this agenda item" });
    }
    
    // Set headers for ZIP download
    res.setHeader('Content-Disposition', `attachment; filename="agenda-item-${id}-files.zip"`);
    res.setHeader('Content-Type', 'application/zip');
    
    // Create ZIP archive
    const archive = archiver('zip', {
      zlib: { level: 9 } // Compression level
    });
    
    // Pipe archive data to the response
    archive.pipe(res);
    
    // Add files to the archive
    for (const file of agendaItem.files) {
      const filePath = file.path;
      if (fs.existsSync(filePath)) {
        archive.file(filePath, { name: file.originalname });
      }
    }
      // Finalize the archive
    archive.finalize();
  } catch (error) {
    console.error("Error downloading files as ZIP:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Delete a file from an agenda item
 * @route DELETE /api/meetings/:meetingId/agenda/:id/files/:fileId
 * @access Private
 */
export const deleteFile = async (req, res) => {
  try {
    const { id, fileId } = req.params;
    
    const agendaItem = await AgendaItem.findById(id);
    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }
    
    // Find the file in the agenda item
    const file = agendaItem.files.find(f => f._id.toString() === fileId);
    if (!file) {
      return res.status(404).json({ message: "File not found" });
    }
    
    // Check if file exists on disk and delete it
    const filePath = file.path;
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
      // Remove file from agenda item
    agendaItem.files = agendaItem.files.filter(f => f._id.toString() !== fileId);
    
    // Save the updated agenda item
    const updatedAgendaItem = await agendaItem.save();
    res.json(updatedAgendaItem);
  } catch (error) {
    console.error("Error deleting file:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

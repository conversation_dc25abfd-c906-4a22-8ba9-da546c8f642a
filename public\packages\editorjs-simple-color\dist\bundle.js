/**
 * Simple Color Tool for Editor.js
 */
class SimpleColorTool {
  static get isInline() {
    return true;
  }
  
  constructor({api}) {
    this.api = api;
    this.button = null;
    this._state = false;
    this.tag = 'SPAN';
    this.class = 'simple-color';
    
    // Available colors
    this.colors = [
      { name: 'Red', value: '#FF0000' },
      { name: 'Blue', value: '#0000FF' },
      { name: 'Green', value: '#008000' },
      { name: 'Purple', value: '#800080' },
      { name: 'Orange', value: '#FFA500' },
      { name: 'Yellow', value: '#FFFF00' },
      { name: 'Black', value: '#000000' }
    ];
    
    // Current color
    this.currentColor = this.colors[0].value;
  }
  
  render() {
    this.button = document.createElement('button');
    this.button.type = 'button';
    this.button.innerHTML = '<svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.17 12L11.58 2H8.42L2.83 12H5.17L6.5 9.5H13.5L14.83 12H17.17ZM7.5 7.5L10 3.5L12.5 7.5H7.5Z" fill="currentColor"/><path d="M2 15H18V17H2V15Z" fill="currentColor"/></svg>';
    this.button.classList.add(this.api.styles.inlineToolButton);
    
    return this.button;
  }
  
  surround(range) {
    if (this.state) {
      this.unwrap(range);
      return;
    }
    
    this.wrap(range);
  }
  
  wrap(range) {
    const selectedText = range.extractContents();
    const mark = document.createElement(this.tag);
    
    mark.classList.add(this.class);
    mark.style.color = this.currentColor;
    mark.appendChild(selectedText);
    range.insertNode(mark);
    
    this.api.selection.expandToTag(mark);
  }
  
  unwrap(range) {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    const text = range.extractContents();
    
    mark.remove();
    
    range.insertNode(text);
  }
  
  checkState() {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    
    this.state = !!mark;
    
    if (this.state) {
      this.button.classList.add(this.api.styles.inlineToolButtonActive);
      if (mark && mark.style.color) {
        this.currentColor = mark.style.color;
      }
    } else {
      this.button.classList.remove(this.api.styles.inlineToolButtonActive);
    }
  }
  
  static get sanitize() {
    return {
      span: {
        class: 'simple-color',
        style: true
      }
    };
  }
  
  showActions() {
    const colorPicker = document.createElement('div');
    colorPicker.classList.add('simple-color-picker');
    
    this.colors.forEach(color => {
      const colorButton = document.createElement('button');
      colorButton.type = 'button';
      colorButton.classList.add('simple-color-button');
      colorButton.style.backgroundColor = color.value;
      colorButton.style.width = '22px';
      colorButton.style.height = '22px';
      colorButton.style.margin = '2px';
      colorButton.style.border = '1px solid #ccc';
      colorButton.style.borderRadius = '3px';
      colorButton.style.padding = '0';
      colorButton.style.cursor = 'pointer';
      colorButton.title = color.name;
      
      colorButton.addEventListener('click', () => {
        this.currentColor = color.value;
        
        // Apply color to selected text
        if (this.api.selection.getRangeCount() > 0) {
          this.wrap(this.api.selection.getRangeAt(0));
        }
        
        // Close the actions
        this.api.inlineToolbar.close();
      });
      
      colorPicker.appendChild(colorButton);
    });
    
    return colorPicker;
  }
}

export default SimpleColorTool;

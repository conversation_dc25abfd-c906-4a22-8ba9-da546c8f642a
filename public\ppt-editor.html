<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>...</title>

  <!-- Shoelace components -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/themes/light.css" />
  <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.20.1/cdn/shoelace-autoloader.js"></script>

  <!-- Shoelace-based styles -->
  <link rel="stylesheet" href="./styles/shoelace-style.css" />
  <script src="https://cdn.jsdelivr.net/npm/@pdf-lib/fontkit@1.1.1/dist/fontkit.umd.min.js"></script>

  <!-- <PERSON>uri for drag-and-drop slide reordering -->
  <script src="https://cdn.jsdelivr.net/npm/muuri@0.9.5/dist/muuri.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/web-animations-js@2.3.2/web-animations.min.js"></script>

  <!-- <script type="importmap">
    {
      "imports": {
        "jszip": "https://cdn.skypack.dev/jszip@3.10.1",
        "pptxgenjs": "https://cdn.jsdelivr.net/npm/pptxgenjs@3.12.0/dist/pptxgen.es.js"
      }
    }
  </script> -->
  <style>
    sl-button.custom-button:hover::part(base) {
      transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      background-color: #4096ff;
    }
  </style>
</head>
<body>
  <header style="display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; border-bottom: 1px solid #e2e8f0; background-color: #fff;">
    <div style="display: flex; align-items: center; gap: 10px;">
        <div style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; background-color: #1677ff; border-radius: 8px;">
          <sl-icon name="file-earmark" style="font-size: 18px; color: white;"></sl-icon>
        </div>
        <div>
          <h1 style="margin: 0; font-size: 16px; font-weight: 600; color: #1e293b;">Untitled presentation</h1>
          <div style="font-size: 12px; color: #64748b; margin-top: 2px;">Last edited Yesterday</div>
        </div>
    </div>
    <div style="display: flex; gap: 8px; align-items: center;">
      <sl-button id="save-json-btn" variant="primary" size="small" style="--sl-color-primary-600: #1677ff;" class="custom-button">
        <sl-icon slot="prefix" name="save"></sl-icon>
        Save
      </sl-button>
      <sl-button id="export-pptx-btn" variant="primary" size="small" style="--sl-color-primary-600: #1677ff;" class="custom-button">
        <sl-icon slot="prefix" name="file-earmark-slides"></sl-icon>
        Export
      </sl-button>
    </div>
  </header>

  <div class="app-container">
    <aside style="width: 240px; display: flex; flex-direction: column; background-color: #f8fafc;">
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; border-bottom: 1px solid #e2e8f0; background-color: white;">
        <h2 style="margin: 0; font-size: 14px; letter-spacing: 0.5px;">SLIDES</h2>
        <sl-tooltip content="Add new slide">
          <sl-button id="add-page-btn" variant="primary" size="small" circle style="--sl-color-primary-600: #1677ff;" class="custom-button">
            <sl-icon name="plus-lg"></sl-icon>
          </sl-button>
        </sl-tooltip>
      </div>
      <div style="overflow-y: overlay; flex: 1; padding-top: 8px; padding-bottom: 16px;">
        <div id="slides-container" style="min-height: 100%; position: relative;">
          <div class="muuri-grid" id="slides-grid">
            <!-- Slide items will be added here dynamically -->
          </div>
        </div>
      </div>
    </aside>

    <main style="display: flex; flex: 1; padding: var(--sl-spacing-medium); padding-bottom: calc(72px - var(--sl-spacing-medium)); overflow-y: overlay; background-color: #edf2f7;">
      <div id="editor-container" style="width: 100%; max-height: 100%; aspect-ratio: 16/9; margin: auto;">
        <!-- Active editor will be shown here -->
      </div>
    </main>
  </div>
  
  <input type="file" id="file-upload-input" accept=".pdf, .xlsx" style="display: none;" />

  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
  <script>
    // Set PDF.js worker path
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  
  <!-- Main JavaScript -->
  <script type="module" src="./modules/main.js"></script>
</body>
</html>

import dayjs from "dayjs";
import Meeting from "../models/meeting.js";
import Notification from "../models/notification.js";
import { handleNotificationForSingleCommittee } from "./notification.js";


export const getMeetings = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = 'startDate',
      sortOrder = 'desc',
      committee
    } = req.query;

    const filter = {};
    if (committee) {
      filter.committee = committee;
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { agenda: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Get total count for pagination
    const total = await Meeting.countDocuments(filter);

    // Handle populate options
    const populateOptions = req.query.populate ? req.query.populate.split(',') : ['committee'];
    
    let query = Meeting.find(filter);
    
    // Apply populate based on requested fields
    if (populateOptions.includes('committee')) {
      query = query.populate("committee");
    }
    
    if (populateOptions.includes('attendees')) {
      query = query.populate("attendees");
    }
    
    if (populateOptions.includes('invitations.member')) {
      query = query.populate({
        path: "invitations.member",
        model: "Directory"
      });
    }
    
    // Always populate tasks for counting
    query = query.populate({
      path: "tasks",
      select: "_id" // Only get task IDs for counting
    });
    
    const data = await query
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const meetingsWithTaskCount = data.map(meeting => {
      const { tasks, ...meetingData } = meeting.toObject();
      return {
        ...meetingData,
        taskCount: tasks?.length || 0
      };
    });

    res.json({
      data: meetingsWithTaskCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getMeeting = async (req, res) => {
  try {
    const data = await Meeting.findById(req.params.id)
      .populate({
        path: "committee",
        populate: {
          path: "members",
          select: "name designation" // optional: select specific fields
        }
      })
      .populate({
        path: "tasks",
        populate: {
          path: "assignedTo",
          select: "name designation"
        }
      })
      .populate({
        path: "attendees",
        select: "name designation email"
      });
    res.json(data);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const createMeeting = async (req, res) => {
  try {
    const meeting = new Meeting(req.body);
    const saved = await meeting.save();

    // If there's a committee, check if this is the first meeting
    if (meeting.committee) {
      // Count existing meetings for this committee
      const meetingCount = await Meeting.countDocuments({
        committee: meeting.committee,
        _id: { $ne: saved._id } // Exclude the meeting we just created
      });

      // If this is not the first meeting, automatically create an agenda item for MoM discussion
      if (meetingCount > 0) {
        const AgendaItem = (await import('../models/agendaItem.js')).default;

        // Create a MoM discussion agenda item as the first item (order 0)
        const momAgendaItem = new AgendaItem({
          title: "Agenda Item 1",
          description: "Confirmation on Minutes of Meeting (MoM) of previous meeting",
          meeting: saved._id,
          order: 0,
          createdBy: req.user ? req.user.id : null
        });

        await momAgendaItem.save();
        handleNotificationForSingleCommittee(meeting.committee, saved.startDate);
        console.log("Created MoM discussion agenda item for meeting:", saved._id);

        // Reorder existing agenda items if necessary
        await reorderAgendaItemsAfterMoMInsertion(saved._id);
      }
    }

    res.status(201).json(saved);
  } catch (error) {
    console.error("Error creating meeting:", error);
    res.status(400).json({ error: error.message });
  }
};

export const updateMeeting = async (req, res) => {
  try {
    const updated = await Meeting.findByIdAndUpdate(req.params.id, req.body, { new: true });
    handleNotificationForSingleCommittee(updated.committee, dayjs(updated.startDate));
    res.json(updated);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export async function getUpcomingMeetings(req, res) {
  try {
    const upcomingMeetings = await Meeting.find({
      startDate: {
        $gte: new Date(),
      }
    }, {}, {}).populate('committee');  
		const now    = new Date();
		const filter = {
			// showPeriod: { $lte: now },
			periodEnd:  { $gte: now }
		};
		const notifications = await Notification.find(filter)
			.sort({ showPeriod: 1 })
			.populate("committeeId")
			.lean();

    return res.json({
      upcomingMeetings: upcomingMeetings,
      autoGenerated: notifications.map(ele => ({
        ...ele.committeeId,
        startDate: ele.periodEnd,
      })),
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}

export const deleteMeeting = async (req, res) => {
  try {
    const updated = await Meeting.findByIdAndDelete(req.params.id);
    handleNotificationForSingleCommittee(updated.committee, updated.startDate);
    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Update meeting attendees
export const updateMeetingAttendees = async (req, res) => {
  try {
    const { id } = req.params;
    const { attendees } = req.body;
    
    // Validate that attendees is an array
    if (!Array.isArray(attendees)) {
      return res.status(400).json({ error: "Attendees must be an array" });
    }
    
    const updated = await Meeting.findByIdAndUpdate(
      id, 
      { attendees }, 
      { new: true }
    ).populate({
      path: "attendees",
      select: "name designation email"
    });
    
    if (!updated) {
      return res.status(404).json({ error: "Meeting not found" });
    }
    
    res.json(updated);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

/**
 * Helper function to reorder existing agenda items after inserting a MoM item at the beginning
 * @param {string} meetingId - The ID of the meeting
 * @private
 */
async function reorderAgendaItemsAfterMoMInsertion(meetingId) {
  try {
    const AgendaItem = (await import('../models/agendaItem.js')).default;

    // Get all agenda items for this meeting except the MoM item (which is at order 0)
    const existingItems = await AgendaItem.find({
      meeting: meetingId,
      order: { $gt: 0 }
    }).sort({ order: 1 });

    // If there are existing items, make sure they're properly ordered starting from 1
    if (existingItems.length > 0) {
      const bulkOperations = existingItems.map((item, index) => ({
        updateOne: {
          filter: { _id: item._id },
          update: { $set: { order: index + 1 } }
        }
      }));

      if (bulkOperations.length > 0) {
        await AgendaItem.bulkWrite(bulkOperations);
        console.log(`Reordered ${bulkOperations.length} agenda items after MoM insertion`);
      }
    }
  } catch (error) {
    console.error("Error reordering agenda items:", error);
  }
}

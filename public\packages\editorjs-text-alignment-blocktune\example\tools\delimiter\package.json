{"name": "@editorjs/delimiter", "version": "1.2.0", "keywords": ["codex editor", "delimiter", "tool", "editor.js", "editorjs"], "description": "Delimiter Too<PERSON> for Editor.js", "license": "MIT", "repository": "https://github.com/editor-js/delimiter", "main": "./dist/bundle.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development --watch"}, "author": {"name": "CodeX Team", "email": "<EMAIL>"}, "devDependencies": {"@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "babel-core": "^6.26.3", "babel-loader": "^8.0.5", "babel-preset-env": "^1.7.0", "css-loader": "^1.0.0", "style-loader": "^0.21.0", "webpack": "^4.29.5", "webpack-cli": "^3.2.3"}}
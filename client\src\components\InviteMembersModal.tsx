import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Text, 
  Stack, 
  TextInput, 
  Textarea, 
  Paper, 
  Group, 
  Button, 
  Checkbox, 
  ScrollArea, 
  Avatar, 
  Notification 
} from '@mantine/core';
import { IconCheck, IconX, IconMail } from '@tabler/icons-react';
import { format } from 'date-fns';
import { Meeting } from '../types';
import { apiClient } from '../config/axios';

interface Member {
  _id: string;
  name: string;
  email?: string;
  designation?: string;
}

interface InviteMembersModalProps {
  opened: boolean;
  onClose: () => void;
  meeting: Meeting | null;
  onInvitationsSent?: () => void;
}

const InviteMembersModal: React.FC<InviteMembersModalProps> = ({ 
  opened, 
  onClose, 
  meeting,
  onInvitationsSent
}) => {
  const [committeeMembers, setCommitteeMembers] = useState<Member[]>([]);
  const [selectedMemberIds, setSelectedMemberIds] = useState<string[]>([]);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [sendingEmail, setSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);

  useEffect(() => {
    if (opened && meeting) {
      fetchCommitteeMembers();
      resetForm();
    }
  }, [opened, meeting]);

  const resetForm = () => {
    setSelectedMemberIds([]);
    setEmailSent(false);
    setEmailError(null);
    
    // Pre-select members who have been invited before
    if (meeting?.invitations && meeting.invitations.length > 0) {
      const invitedMemberIds = meeting.invitations.map(inv => 
        typeof inv.member === 'object' ? inv.member._id : inv.member
      );
      setSelectedMemberIds(invitedMemberIds);
      
      // Set email error to show invitation status
      const invitationInfo = `${meeting.invitations.length} member(s) have already been invited to this meeting.`;
      setEmailError(invitationInfo);
      setTimeout(() => setEmailError(null), 5000);
    }
  };

  const fetchCommitteeMembers = async () => {
    if (!meeting) return;

    try {
      // Get committee ID from the meeting
      const committeeId = typeof meeting.committee === 'string' 
        ? meeting.committee 
        : meeting.committee._id;
      
      // Fetch committee details with members
      const response = await apiClient.get(`/api/committee/${committeeId}`);
      const committee = response.data;
      
      if (committee && committee.members && committee.members.length > 0) {
        setCommitteeMembers(committee.members);
        
        // Set the email subject with meeting title and date
        const meetingDate = format(new Date(meeting.startDate), 'EEE, MMM d, yyyy');
        setEmailSubject(`Invitation: ${meeting.title} - ${meetingDate}`);
        
        // Set default email content
        setEmailContent(`
You are invited to attend the following meeting:

Meeting: ${meeting.title}
Date: ${format(new Date(meeting.startDate), 'EEEE, MMMM d, yyyy')}
Time: ${format(new Date(meeting.startDate), 'h:mm a')}

${meeting.description ? `\nDetails:\n${meeting.description}` : ''}

${meeting.location ? `\nLocation: ${meeting.location}` : ''}

Please confirm your attendance.
        `.trim());
      }
    } catch (error) {
      console.error('Failed to fetch committee members:', error);
      setEmailError('Failed to fetch committee members. Please try again.');
    }
  };

  // Toggle member selection
  const toggleMemberSelection = (memberId: string) => {
    setSelectedMemberIds(current => 
      current.includes(memberId) 
        ? current.filter(id => id !== memberId)
        : [...current, memberId]
    );
  };

  // Select or deselect all members
  const handleSelectAllMembers = (select: boolean) => {
    if (select) {
      setSelectedMemberIds(committeeMembers.map(member => member._id));
    } else {
      setSelectedMemberIds([]);
    }
  };

  // Send invitations to selected members
  const handleSendInvitations = async () => {
    if (!meeting || selectedMemberIds.length === 0 || !emailSubject || !emailContent) {
      setEmailError('Please select at least one member and provide email subject and content');
      return;
    }

    try {
      setSendingEmail(true);
      setEmailError(null);
      
      await apiClient.post('/api/meeting-invitations', {
        meetingId: meeting._id,
        recipientIds: selectedMemberIds,
        emailSubject,
        emailContent
      });
      
      setEmailSent(true);
      
      // Close modal after 3 seconds on success
      setTimeout(() => {
        onClose();
        if (onInvitationsSent) {
          onInvitationsSent();
        }
      }, 3000);
      
    } catch (error: any) {
      console.error('Failed to send invitations:', error);
      setEmailError(
        error?.response?.data?.error || 
        'Failed to send invitations. Please try again.'
      );
    } finally {
      setSendingEmail(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={<Text fw={600}>Invite Members to Meeting</Text>}
      size="lg"
    >
      {emailSent ? (
        <Notification
          title="Invitations Sent!"
          color="green"
          icon={<IconCheck size={20} />}
          onClose={() => {}}
        >
          Email invitations have been sent successfully.
        </Notification>
      ) : (
        <Stack>
          {emailError && (
            <Notification
              title="Information"
              color={emailError.includes('already been invited') ? 'blue' : 'red'}
              icon={emailError.includes('already been invited') ? <IconMail size={20} /> : <IconX size={20} />}
              onClose={() => setEmailError(null)}
            >
              {emailError}
            </Notification>
          )}
          
          <TextInput
            label="Subject"
            placeholder="Meeting invitation subject"
            value={emailSubject}
            onChange={(e) => setEmailSubject(e.target.value)}
            leftSection={<IconMail size={16} />}
            required
          />
          
          <Textarea
            label="Email Content"
            placeholder="Write the invitation message here..."
            minRows={5}
            value={emailContent}
            onChange={(e) => setEmailContent(e.target.value)}
            required
          />
          
          <Paper withBorder p="md">
            <Group justify="space-between" mb="xs">
              <Text fw={500}>Select Recipients</Text>
              <Group>
                <Button 
                  variant="subtle" 
                  size="xs" 
                  onClick={() => handleSelectAllMembers(true)}
                >
                  Select All
                </Button>
                <Button 
                  variant="subtle" 
                  size="xs" 
                  color="gray" 
                  onClick={() => handleSelectAllMembers(false)}
                >
                  Clear
                </Button>
              </Group>
            </Group>
            
            <ScrollArea h={200}>
              {committeeMembers.length === 0 ? (
                <Text c="dimmed" ta="center" py="md">No members found for this committee</Text>
              ) : (
                committeeMembers.map((member) => (
                  <Group key={member._id} py="xs" justify="space-between">
                    <Group>
                      <Avatar 
                        radius="xl" 
                        size="sm"
                        color={['blue', 'cyan', 'green', 'teal', 'violet'][Math.floor(Math.random() * 5)]}
                      >
                        {member.name?.charAt(0)}
                      </Avatar>
                      <div>
                        <Text size="sm">{member.name}</Text>
                        <Text size="xs" c="dimmed">{member.email || 'No email available'}</Text>
                      </div>
                    </Group>
                    <Checkbox
                      checked={selectedMemberIds.includes(member._id)}
                      onChange={() => toggleMemberSelection(member._id)}
                      disabled={!member.email}
                    />
                  </Group>
                ))
              )}
            </ScrollArea>
          </Paper>
          
          <Group justify="flex-end">
            <Button 
              onClick={handleSendInvitations}
              loading={sendingEmail}
              disabled={selectedMemberIds.length === 0 || !emailSubject || !emailContent}
              leftSection={<IconCheck size={16} />}
            >
              Send Invitations
            </Button>
          </Group>
        </Stack>
      )}
    </Modal>
  );
};

export default InviteMembersModal;

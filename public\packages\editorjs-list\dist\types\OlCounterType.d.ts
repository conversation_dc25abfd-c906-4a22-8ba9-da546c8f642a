export type OlCounterType = 'numeric' | 'upper-roman' | 'lower-roman' | 'upper-alpha' | 'lower-alpha';
/**
 * Map that represents all of the supported styles of the counters for ordered list
 */
export declare const OlCounterTypesMap: Map<string, string>;
/**
 * Map that represents relation between supported counter types and theirs icons to be displayed in toolbox
 */
export declare const OlCounterIconsMap: Map<string, string>;

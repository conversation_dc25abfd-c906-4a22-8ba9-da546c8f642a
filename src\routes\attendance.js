import express from "express";
import auth from "../middleware/auth.js";
import {
  generateAttendanceSheet,
  uploadAttendanceSheet,
  getAttendanceSheet,
  getAttendanceAnalysis,
  upload
} from "../controllers/attendanceController.js";

const router = express.Router();

// Generate attendance sheet for a committee
router.get("/committee/:committeeId/attendance-sheet", auth, generateAttendanceSheet);

// Upload attendance sheet for a meeting
router.post("/meetings/:meetingId/attendance-sheet", auth, upload.single("attendanceSheet"), uploadAttendanceSheet);

// Get attendance sheet for a meeting
router.get("/meetings/:meetingId/attendance-sheet", auth, getAttendanceSheet);

// Get attendance analysis for a meeting
router.get("/meetings/:meetingId/attendance-analysis", auth, getAttendanceAnalysis);

export default router;

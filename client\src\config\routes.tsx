import { Navigate } from "react-router-dom";
import Login from "../pages/Login";
import Register from "../pages/Register";
import Committees from "../pages/Committees";
import Meetings from "../pages/Meetings";
import Directory from "../pages/Directory";
import MeetingDetails from "../pages/MeetingDetails";

export const publicRoutes = [
  { path: "/login", element: <Login /> },
  { path: "/register", element: <Register /> },
];

export const privateRoutes = [
  { path: "/", element: <Navigate to="/committees" replace /> },
  { path: "/committees", element: <Committees /> },
  { path: "/committee/:committeeId/meetings", element: <Meetings /> },
  { path: "/committee/:committeeId/meetings/:meetingId", element: <MeetingDetails /> },
  { path: "/directory", element: <Directory /> },
];

import "dotenv/config";
import express from "express";
import mongoose from "mongoose";
import cors from "cors";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import authRoutes from "./routes/auth.js";
import userRoutes from "./routes/user.js";
import meetingRoutes from "./routes/meeting.js";
import committeeRoutes from "./routes/committee.js";
import directoryRoutes from "./routes/directory.js";
import dashboardRoutes from "./routes/dashboard.js";
import departmentRoutes from "./routes/department.js";
import configRoutes from "./routes/config.js";
import listingsRoutes from "./routes/listings.js";
import attendanceRoutes from "./routes/attendance.js";
import emailRoutes from "./routes/email.js";
import { initEmailService } from "./services/emailService.js";
import schemeRoutes from "./routes/schemes.js";
import axios from "axios";
import "./worker/notification.js";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

// Middleware
app.use(cors({
  origin: 'http://localhost:5173',
  credentials: true
}));
app.use(express.json());

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/meetings", meetingRoutes);
app.use("/api/committee", committeeRoutes);
app.use("/api/directory", directoryRoutes);
app.use("/api/dashboard", dashboardRoutes);
app.use("/api/departments", departmentRoutes);
app.use("/api/config", configRoutes);
app.use("/api/schemes", schemeRoutes);
app.use("/api", listingsRoutes);
app.use("/api", attendanceRoutes);
app.use("/api", emailRoutes);

// Serve uploaded files
app.use("/", express.static(path.join(__dirname, "../public")));
app.use("/uploads", express.static(path.join(__dirname, "../uploads")));

// server /editor/api/{any path here} on analysis server as /api{specified path here}
app.use("/editor/api", async function(req, res) {
  const endPoint = req.originalUrl.replace(/^\/editor\/api/, "/api");
  try {
    const url = new URL(endPoint, process.env.ANALYSIS_SERVER_URL);
    console.log("Forwarding editor request to analysis server:", endPoint);
    
    // Support for binary responses (responseType: 'arraybuffer')
    const responseType = req.headers['accept']?.includes('image/') || endPoint.includes('/slides') ? 'arraybuffer' : 'json';
    
    const response = await axios.request({
      method: req.method,
      url,
      headers: {
        ...req.headers,
        host: process.env.ANALYSIS_SERVER_URL,
        "x-internal-req": "testtokentesttoken"
      },
      data: req.body,
      responseType,
      // Don't treat 304 as an error
      validateStatus: status => (status >= 200 && status < 300) || status === 304
    });
    
    // Handle 304 Not Modified
    if (response.status === 304) {
      console.log("Analysis server returned 304 Not Modified for:", endPoint);
      return res.status(304).end();
    }
    
    // Copy all headers from the proxied response
    Object.entries(response.headers).forEach(([key, value]) => {
      // Skip hop-by-hop headers
      if (!['connection', 'keep-alive', 'transfer-encoding'].includes(key.toLowerCase())) {
        res.setHeader(key, value);
      }
    });
    
    // Send appropriate response based on content type
    res.status(response.status);
    
    if (responseType === 'arraybuffer') {
      return res.send(Buffer.from(response.data));
    } else {
      return res.json(response.data);
    }
  } catch(ex) {
    console.error("Error forwarding request to analysis server:", ex.message);
    return res.status(500).json({ 
      error: "Internal Server Error", 
      message: ex.message,
      endpoint: endPoint
    });
  }
});


app.get("/editor", (req, res) => {
  res.sendFile(path.join(__dirname, "../public/ppt-editor.html"));
});

// Database connection
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log("Connected to MongoDB"))
  .catch(err => console.error("Could not connect to MongoDB:", err));

// Initialize email service
initEmailService()
  .then(() => console.log("Email service initialized"))
  .catch(err => console.error("Failed to initialize email service:", err));

// Health check route
app.get("/health", (req, res) => {
  res.status(200).json({ status: "OK" });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

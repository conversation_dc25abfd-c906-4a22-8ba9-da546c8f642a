import Committee from "../models/committee.js";
import Meeting from "../models/meeting.js";
import Task from "../models/task.js";

/**
 * Get dashboard statistics
 * @route GET /api/dashboard/stats
 * @access Private
 */
export const getDashboardStats = async (req, res) => {
  try {
    // Get all committees
    const committees = await Committee.find({}).populate('members').populate('assistant');
    
    // Initialize arrays to hold aggregated data
    const allMeetings = [];
    const allTasks = [];
    const committeePerformance = [];
    const committeesForUpcomingMeeting = [];
    
    // Process each committee
    for (const committee of committees) {
      try {
        // Get meetings for this committee
        const committeeMeetings = await Meeting.find({ committee: committee._id })
          .populate('committee')
          .populate('attendees')
          .sort({ startDate: -1 });
        
        // Add to all meetings array
        allMeetings.push(...committeeMeetings);
        
        // Find latest meeting
        let latestMeeting = committeeMeetings[0];
        
        // Get tasks for committee meetings
        const committeeTasks = [];
        for (const meeting of committeeMeetings) {
          const tasks = await Task.find({ meetingId: meeting._id });
          
          // Add committee and meeting info to tasks
          const enhancedTasks = tasks.map(task => ({
            ...task.toObject(),
            committeeName: committee.name,
            committeeId: committee._id
          }));
          
          committeeTasks.push(...enhancedTasks);
          allTasks.push(...enhancedTasks);
        }
        
        // Calculate committee performance
        const totalTasks = committeeTasks.length;
        const completedTasks = committeeTasks.filter(t => t.status === 'completed').length;
        const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
        
        committeePerformance.push({
          committee,
          totalTasks,
          completedTasks,
          completionRate,
          upcomingMeetings: 0 // Will be calculated next
        });
        
        // Calculate next meeting based on frequency if there is at least one meeting
        if (latestMeeting && committee.meetingFrequency) {
          const latest = new Date(latestMeeting.startDate);
          const nextMeeting = new Date(latest);
          
          // Calculate next meeting date based on frequency
          switch (committee.meetingFrequency) {
            case "weekly":
              nextMeeting.setDate(latest.getDate() + 7);
              break;
            case "biweekly":
              nextMeeting.setDate(latest.getDate() + 14);
              break;
            case "monthly":
              nextMeeting.setMonth(latest.getMonth() + 1);
              break;
            case "quarterly":
              nextMeeting.setMonth(latest.getMonth() + 3);
              break;
            case "halfyearly":
              nextMeeting.setMonth(latest.getMonth() + 6);
              break;
            case "yearly":
              nextMeeting.setFullYear(latest.getFullYear() + 1);
              break;
            default:
              continue;
          }
          
          // Check if meeting is in next 30 days
          const now = new Date();
          const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
          
          if (nextMeeting >= now && nextMeeting <= in30Days) {
            committeesForUpcomingMeeting.push({
              ...committee.toObject(),
              meetingTime: nextMeeting,
            });
          }
        }
      } catch (error) {
        console.error(`Failed to process committee ${committee._id}:`, error);
      }
    }
    
    // Calculate overall stats
    const total = allTasks.length;
    const completed = allTasks.filter(t => t.status === "completed").length;
    const pending = total - completed;
    const highPriority = allTasks.filter(t => t.priority === "high").length;
    
    // Get upcoming meetings (next 30 days)
    const now = new Date();
    const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    // Create virtual meetings for committees that are due for a meeting
    const meetingsForCommittee = committeesForUpcomingMeeting.map((committee) => ({
      title: `Meeting Due`,
      startDate: committee.meetingTime.toString(),
      attendees: committee.members,
      _id: "",
      endDate: committee.meetingTime.toString(),
      createdAt: new Date().toString(),
      status: 'scheduled',
      committee: committee,
      updatedAt: new Date().toString(),
    }));
    
    // Combine actual and calculated upcoming meetings
    const upcomingMeetings = [...allMeetings, ...meetingsForCommittee]
      .filter(m => {
        if (!m._id) {
          return true; // Include virtual meetings
        }
        const meetingDate = new Date(m.startDate);
        return meetingDate >= now && meetingDate <= nextMonth;
      })
      .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
    
    // Get tasks due this week
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    const tasksDueThisWeek = allTasks
      .filter(t => {
        if (!t.deadline) return false;
        const deadline = new Date(t.deadline);
        return deadline >= now && deadline <= nextWeek && t.status === 'pending';
      });
    
    // Get overdue tasks
    const overdueTasks = allTasks.filter(t => {
      if (!t.deadline) return false;
      const deadline = new Date(t.deadline);
      return deadline < now && t.status === 'pending';
    });
    
    // Find best and worst performing committees
    const committeesWithTasks = committeePerformance.filter(c => c.totalTasks > 0);
    const bestPerforming = committeesWithTasks.length > 0
      ? committeesWithTasks.reduce((best, current) =>
          current.completionRate > best.completionRate ? current : best
        )
      : null;
    
    const worstPerforming = committeesWithTasks.length > 0
      ? committeesWithTasks.reduce((worst, current) =>
          current.completionRate < worst.completionRate ? current : worst
        )
      : null;
    
    // Return compiled dashboard stats
    res.json({
      total,
      completed,
      pending,
      highPriority,
      allMeetings,
      upcomingMeetings,
      activeCommittees: committees.length,
      tasksDueThisWeek,
      overdueTasks,
      bestPerformingCommittee: bestPerforming,
      worstPerformingCommittee: worstPerforming,
      allCommitteePerformance: committeePerformance,
    });
    
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({ message: 'Server error while fetching dashboard statistics' });
  }
};

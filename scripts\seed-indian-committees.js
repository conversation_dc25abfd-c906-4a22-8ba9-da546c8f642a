import "dotenv/config";
import mongoose from "mongoose";
import Department from "../src/models/department.js";
import Directory from "../src/models/directory.js";
import User from "../src/models/user.js";
import Committee from "../src/models/committee.js";
import bcrypt from "bcryptjs";

// Sample Indian committees based on common government/organizational structures
const sampleCommittees = [
  {
    name: "Digital India Implementation Committee",
    description: "Oversees the implementation of digital transformation initiatives and technology adoption across all departments",
    meetingFrequency: "monthly",
    departmentCode: "IT",
    memberRoles: ["CTO", "IT Manager", "Senior Developer", "DevOps Engineer", "Operations Director"],
    startDate: new Date("2025-01-15")
  },
  {
    name: "Staff Welfare and Development Committee",
    description: "Addresses employee welfare, professional development, and workplace satisfaction initiatives",
    meetingFrequency: "monthly",
    departmentCode: "HR",
    memberRoles: ["HR Director", "HR Manager", "Finance Manager", "Operations Director", "Legal Counsel"],
    startDate: new Date("2025-01-20")
  },
  {
    name: "Financial Review and Audit Committee",
    description: "Reviews financial performance, budget allocations, and ensures compliance with financial regulations",
    meetingFrequency: "quarterly",
    departmentCode: "FIN",
    memberRoles: ["CFO", "Finance Manager", "Senior Accountant", "QA Director", "General Counsel"],
    startDate: new Date("2025-02-01")
  },
  {
    name: "Public Relations and Communication Committee",
    description: "Manages external communications, public relations strategies, and stakeholder engagement",
    meetingFrequency: "biweekly",
    departmentCode: "MKT",
    memberRoles: ["Marketing Director", "Digital Marketing Manager", "Content Specialist", "Customer Success Director"],
    startDate: new Date("2025-01-25")
  },
  {
    name: "Quality Assurance and Standards Committee",
    description: "Ensures quality standards, monitors compliance, and implements continuous improvement processes",
    meetingFrequency: "monthly",
    departmentCode: "QA",
    memberRoles: ["QA Director", "QA Manager", "QA Analyst", "Operations Director", "R&D Director"],
    startDate: new Date("2025-02-05")
  },
  {
    name: "Research and Innovation Committee",
    description: "Promotes research initiatives, innovation projects, and knowledge sharing across departments",
    meetingFrequency: "monthly",
    departmentCode: "R&D",
    memberRoles: ["R&D Director", "Research Scientist", "Product Development Manager", "CTO", "Operations Director"],
    startDate: new Date("2025-01-30")
  },
  {
    name: "Strategic Planning and Development Committee",
    description: "Develops long-term strategic plans, monitors organizational goals, and ensures effective implementation",
    meetingFrequency: "quarterly",
    departmentCode: "OPS",
    memberRoles: ["Operations Director", "Project Manager", "CFO", "HR Director", "Marketing Director"],
    startDate: new Date("2025-01-10")
  },
  {
    name: "Customer Service Excellence Committee",
    description: "Focuses on improving customer satisfaction, service quality, and customer experience initiatives",
    meetingFrequency: "biweekly",
    departmentCode: "CS",
    memberRoles: ["Customer Success Director", "Support Manager", "Technical Support Specialist", "Marketing Director", "QA Manager"],
    startDate: new Date("2025-02-10")
  },
  {
    name: "Legal and Compliance Review Committee",
    description: "Reviews legal matters, ensures regulatory compliance, and manages risk assessment across all operations",
    meetingFrequency: "quarterly",
    departmentCode: "LEGAL",
    memberRoles: ["General Counsel", "Legal Counsel", "HR Director", "Finance Manager", "QA Director"],
    startDate: new Date("2025-02-15")
  },
  {
    name: "Sales Growth and Business Development Committee",
    description: "Develops sales strategies, monitors business growth, and identifies new market opportunities",
    meetingFrequency: "monthly",
    departmentCode: "SALES",
    memberRoles: ["Sales Director", "Senior Sales Manager", "Sales Representative", "Marketing Director", "Customer Success Director"],
    startDate: new Date("2025-01-18")
  }
];

// Updated Indian names for directory members
const indianDirectoryMembers = [
  // HR Department
  {
    name: "Priya Sharma",
    designation: "HR Director",
    email: "<EMAIL>",
    phone: "+91-98765-43210",
    department: "HR",
    description: "Experienced HR professional with 15+ years in talent management and organizational development",
    address: "A-123 Corporate Tower, Sector 18, Gurugram, Haryana 122015"
  },
  {
    name: "Rajesh Kumar",
    designation: "HR Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43211",
    department: "HR",
    description: "Specializes in recruitment, employee engagement, and policy implementation"
  },
  {
    name: "Sneha Patel",
    designation: "HR Specialist",
    email: "<EMAIL>",
    phone: "+91-98765-43212",
    department: "HR",
    description: "Handles employee relations, training programs, and performance management"
  },

  // IT Department
  {
    name: "Arjun Reddy",
    designation: "CTO",
    email: "<EMAIL>",
    phone: "+91-98765-43220",
    department: "IT",
    description: "Technology leader with expertise in cloud infrastructure, AI/ML, and digital transformation",
    address: "B-456 Tech Park, Electronic City, Bangalore, Karnataka 560100"
  },
  {
    name: "Kavya Nair",
    designation: "IT Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43221",
    department: "IT",
    description: "Manages IT operations, cybersecurity, and enterprise software solutions"
  },
  {
    name: "Vikram Singh",
    designation: "Senior Developer",
    email: "<EMAIL>",
    phone: "+91-98765-43222",
    department: "IT",
    description: "Full-stack developer with expertise in React, Node.js, and cloud technologies"
  },
  {
    name: "Ananya Gupta",
    designation: "DevOps Engineer",
    email: "<EMAIL>",
    phone: "+91-98765-43223",
    department: "IT",
    description: "Specializes in CI/CD pipelines, containerization, and infrastructure automation"
  },

  // Finance Department
  {
    name: "Suresh Agarwal",
    designation: "CFO",
    email: "<EMAIL>",
    phone: "+91-98765-43230",
    department: "FIN",
    description: "Financial executive with 20+ years experience in corporate finance and strategic planning",
    address: "C-789 Financial District, Bandra Kurla Complex, Mumbai, Maharashtra 400051"
  },
  {
    name: "Meera Joshi",
    designation: "Finance Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43231",
    department: "FIN",
    description: "Oversees budgeting, financial analysis, and treasury management"
  },
  {
    name: "Rahul Verma",
    designation: "Senior Accountant",
    email: "<EMAIL>",
    phone: "+91-98765-43232",
    department: "FIN",
    description: "Handles accounts, taxation, and financial reporting with CA qualification"
  },

  // Marketing Department
  {
    name: "Deepika Malhotra",
    designation: "Marketing Director",
    email: "<EMAIL>",
    phone: "+91-98765-43240",
    department: "MKT",
    description: "Creative marketing leader with expertise in digital marketing and brand management",
    address: "D-321 Media Hub, Connaught Place, New Delhi, Delhi 110001"
  },
  {
    name: "Karthik Iyer",
    designation: "Digital Marketing Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43241",
    department: "MKT",
    description: "Manages social media campaigns, SEO/SEM, and online advertising strategies"
  },
  {
    name: "Ritika Chopra",
    designation: "Content Specialist",
    email: "<EMAIL>",
    phone: "+91-98765-43242",
    department: "MKT",
    description: "Creates engaging content for digital platforms and marketing campaigns"
  },

  // Operations Department
  {
    name: "Amit Bhattacharya",
    designation: "Operations Director",
    email: "<EMAIL>",
    phone: "+91-98765-43250",
    department: "OPS",
    description: "Operations expert focused on process optimization and operational excellence",
    address: "E-654 Business Center, Salt Lake City, Kolkata, West Bengal 700091"
  },
  {
    name: "Pooja Desai",
    designation: "Project Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43251",
    department: "OPS",
    description: "Certified PMP with experience in agile project management and team leadership"
  },
  {
    name: "Siddharth Rao",
    designation: "Operations Analyst",
    email: "<EMAIL>",
    phone: "+91-98765-43252",
    department: "OPS",
    description: "Analyzes operational metrics and implements process improvements"
  },

  // R&D Department
  {
    name: "Dr. Lakshmi Menon",
    designation: "R&D Director",
    email: "<EMAIL>",
    phone: "+91-98765-43260",
    department: "R&D",
    description: "PhD in Engineering with 18 years of research experience in emerging technologies",
    address: "F-987 Innovation Hub, Whitefield, Bangalore, Karnataka 560066"
  },
  {
    name: "Aditya Jain",
    designation: "Research Scientist",
    email: "<EMAIL>",
    phone: "+91-98765-43261",
    department: "R&D",
    description: "Conducts applied research in AI/ML and develops innovative solutions"
  },
  {
    name: "Swati Bansal",
    designation: "Product Development Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43262",
    department: "R&D",
    description: "Manages product lifecycle from ideation to market launch"
  },

  // Quality Assurance Department
  {
    name: "Ravi Krishnan",
    designation: "QA Director",
    email: "<EMAIL>",
    phone: "+91-98765-43270",
    department: "QA",
    description: "Quality management expert with Six Sigma Black Belt and ISO certifications",
    address: "G-147 Quality Plaza, Cyber City, Hyderabad, Telangana 500081"
  },
  {
    name: "Nisha Aggarwal",
    designation: "QA Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43271",
    department: "QA",
    description: "Implements quality control processes and compliance standards"
  },
  {
    name: "Varun Saxena",
    designation: "QA Analyst",
    email: "<EMAIL>",
    phone: "+91-98765-43272",
    department: "QA",
    description: "Performs quality testing, audits, and ensures adherence to standards"
  },

  // Sales Department
  {
    name: "Neha Kapoor",
    designation: "Sales Director",
    email: "<EMAIL>",
    phone: "+91-98765-43280",
    department: "SALES",
    description: "Results-driven sales leader with consistent track record of exceeding targets",
    address: "H-258 Commerce Center, Andheri East, Mumbai, Maharashtra 400069"
  },
  {
    name: "Rohit Pandey",
    designation: "Senior Sales Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43281",
    department: "SALES",
    description: "Manages key client relationships and develops new business opportunities"
  },
  {
    name: "Priyanka Sinha",
    designation: "Sales Representative",
    email: "<EMAIL>",
    phone: "+91-98765-43282",
    department: "SALES",
    description: "Focuses on lead generation, client acquisition, and relationship management"
  },

  // Legal Department
  {
    name: "Ashok Varma",
    designation: "General Counsel",
    email: "<EMAIL>",
    phone: "+91-98765-43290",
    department: "LEGAL",
    description: "Senior advocate with expertise in corporate law, compliance, and dispute resolution",
    address: "I-369 Legal District, Lodhi Road, New Delhi, Delhi 110003"
  },
  {
    name: "Divya Bhardwaj",
    designation: "Legal Counsel",
    email: "<EMAIL>",
    phone: "+91-98765-43291",
    department: "LEGAL",
    description: "Handles contracts, intellectual property, and regulatory compliance matters"
  },

  // Customer Support Department
  {
    name: "Gaurav Mehta",
    designation: "Customer Success Director",
    email: "<EMAIL>",
    phone: "+91-98765-43300",
    department: "CS",
    description: "Customer experience expert focused on satisfaction, retention, and success metrics",
    address: "J-741 Service Center, Sector 62, Noida, Uttar Pradesh 201309"
  },
  {
    name: "Anjali Tiwari",
    designation: "Support Manager",
    email: "<EMAIL>",
    phone: "+91-98765-43301",
    department: "CS",
    description: "Manages customer support operations, team development, and service quality"
  },
  {
    name: "Manish Kumar",
    designation: "Technical Support Specialist",
    email: "<EMAIL>",
    phone: "+91-98765-43302",
    department: "CS",
    description: "Provides technical assistance, troubleshooting, and customer training"
  }
];

async function seedCommittees() {
  try {
    // Connect to MongoDB
    console.log("Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB successfully!");

    // Get or create system user
    console.log("Getting system user...");
    let systemUser = await User.findOne({ email: "<EMAIL>" });
    
    if (!systemUser) {
      const hashedPassword = await bcrypt.hash("systemPassword123", 10);
      systemUser = await User.create({
        email: "<EMAIL>",
        password: hashedPassword,
        name: "System User",
        role: 1
      });
      console.log("Created system user!");
    }

    // Clear existing committees and directory members
    console.log("Clearing existing committees and directory data...");
    await Committee.deleteMany({});
    await Directory.deleteMany({});
    console.log("Existing committees and directory data cleared!");

    // Get existing departments
    const departments = await Department.find({});
    console.log(`Found ${departments.length} departments`);

    if (departments.length === 0) {
      console.log("No departments found. Please run the sample data seeding script first.");
      console.log("Run: npm run seed-sample-data");
      return;
    }

    // Create department map
    const departmentMap = {};
    departments.forEach(dept => {
      departmentMap[dept.code] = dept;
    });

    // Create Indian directory members
    console.log("Creating Indian directory members...");
    const createdMembers = [];

    for (const memberData of indianDirectoryMembers) {
      try {
        const department = departmentMap[memberData.department];
        if (!department) {
          console.warn(`Department not found for code: ${memberData.department}`);
          continue;
        }

        const member = new Directory({
          name: memberData.name,
          designation: memberData.designation,
          email: memberData.email,
          phone: memberData.phone,
          department: department._id,
          description: memberData.description,
          address: memberData.address || `${memberData.name}'s Office Address`,
          createdBy: systemUser._id
        });

        await member.save();
        createdMembers.push(member);
      } catch (error) {
        console.error(`Error creating directory member ${memberData.name}:`, error.message);
      }
    }

    console.log(`✓ Created ${createdMembers.length} Indian directory members!`);

    // Update department heads with Indian members
    console.log("Assigning Indian department heads...");
    let headsAssigned = 0;

    for (const dept of departments) {
      const deptMembers = createdMembers.filter(member => 
        member.department.toString() === dept._id.toString()
      );
      
      if (deptMembers.length > 0) {
        // Find a director/head level person or use the first member
        const head = deptMembers.find(member => 
          member.designation.toLowerCase().includes('director') ||
          member.designation.toLowerCase().includes('cto') ||
          member.designation.toLowerCase().includes('cfo') ||
          member.designation.toLowerCase().includes('counsel')
        ) || deptMembers[0];

        dept.head = head._id;
        await dept.save();
        headsAssigned++;
      }
    }

    console.log(`✓ Updated ${headsAssigned} departments with Indian heads!`);

    // Create committees
    console.log("Creating Indian committees...");
    const createdCommittees = [];

    for (const committeeData of sampleCommittees) {
      try {
        // Get the department for this committee
        const department = departmentMap[committeeData.departmentCode];
        if (!department) {
          console.warn(`Department not found for code: ${committeeData.departmentCode}`);
          continue;
        }

        // Find members based on their roles/designations
        const committeeMembers = [];
        const assistantMember = createdMembers.find(member => 
          member.department && 
          member.department.toString() === department._id.toString() &&
          (member.designation.toLowerCase().includes('director') || 
           member.designation.toLowerCase().includes('cto') || 
           member.designation.toLowerCase().includes('cfo') ||
           member.designation.toLowerCase().includes('counsel'))
        );

        // Add members from various departments based on the member roles
        for (const role of committeeData.memberRoles) {
          const member = createdMembers.find(m => 
            m.designation.toLowerCase().includes(role.toLowerCase()) ||
            m.designation.toLowerCase().replace(/\s+/g, '').includes(role.toLowerCase().replace(/\s+/g, ''))
          );
          
          if (member && !committeeMembers.some(cm => cm.toString() === member._id.toString())) {
            committeeMembers.push(member._id);
          }
        }

        // If we don't have enough members, add some from the primary department
        if (committeeMembers.length < 3) {
          const deptMembers = createdMembers.filter(member => 
            member.department && 
            member.department.toString() === department._id.toString() &&
            !committeeMembers.some(cm => cm.toString() === member._id.toString())
          );
          
          deptMembers.slice(0, 5 - committeeMembers.length).forEach(member => {
            committeeMembers.push(member._id);
          });
        }

        // Create the committee
        const committee = new Committee({
          name: committeeData.name,
          description: committeeData.description,
          members: committeeMembers,
          meetingFrequency: committeeData.meetingFrequency,
          // startDate will be auto-generated based on meeting frequency
          assistant: assistantMember ? assistantMember._id : null,
          department: department._id,
          createdBy: systemUser._id
        });

        await committee.save();
        createdCommittees.push(committee);

        console.log(`✓ Created committee: ${committee.name} (${committeeMembers.length} members)`);
      } catch (error) {
        console.error(`Error creating committee ${committeeData.name}:`, error.message);
      }
    }

    // Display summary
    console.log("\n=== INDIAN COMMITTEE & DIRECTORY SEEDING COMPLETE ===");
    console.log(`Created ${createdMembers.length} Indian directory members`);
    console.log(`Created ${createdCommittees.length} Indian committees:`);
    
    for (const committee of createdCommittees) {
      const populatedCommittee = await Committee.findById(committee._id)
        .populate('members', 'name designation')
        .populate('assistant', 'name designation')
        .populate('department', 'name');
      
      console.log(`\n📋 ${populatedCommittee.name}`);
      console.log(`   Department: ${populatedCommittee.department?.name || 'N/A'}`);
      console.log(`   Members: ${populatedCommittee.members.length}`);
      populatedCommittee.members.slice(0, 3).forEach(member => {
        console.log(`     - ${member.name} (${member.designation})`);
      });
      if (populatedCommittee.members.length > 3) {
        console.log(`     ... and ${populatedCommittee.members.length - 3} more`);
      }
      console.log(`   Assistant: ${populatedCommittee.assistant?.name || 'Not assigned'} (${populatedCommittee.assistant?.designation || 'N/A'})`);
      console.log(`   Frequency: ${populatedCommittee.meetingFrequency}`);
    }

    console.log(`\nTotal directory members created: ${createdMembers.length}`);
    console.log(`Total committees created: ${createdCommittees.length}`);
    console.log("\nIndian committee and directory data has been successfully seeded to your database!");

  } catch (error) {
    console.error("Error seeding committees:", error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log("Database connection closed.");
    process.exit(0);
  }
}

// Run the committee seeding script
seedCommittees();

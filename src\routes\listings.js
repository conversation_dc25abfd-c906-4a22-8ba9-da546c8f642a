import express from 'express';
import { getTasks, getMeetings } from '../controllers/listingController.js';
import auth from '../middleware/auth.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(auth);

// Tasks listing with filters
router.get('/tasks', getTasks);

// Meetings listing with filters
router.get('/meetings', getMeetings);

export default router;

import Task from "../models/task.js";
import Meeting from "../models/meeting.js";
import mongoose from "mongoose";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { extractTasksFromImageWithGemini } from "../services/gemini.js";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, "../../uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Get all tasks for a meeting
export const getTasksForMeeting = async (req, res) => {
  try {
    const { meetingId } = req.params;
    
    // Validate meetingId
    if (!meetingId || meetingId === 'undefined') {
      return res.status(400).json({ message: "Invalid meeting ID" });
    }
    
    // Validate if it's a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(meetingId)) {
      return res.status(400).json({ message: "Invalid meeting ID format" });
    }
    
    const tasks = await Task.find({ meeting: meetingId })
      .populate('assignedTo', 'name designation')
      .sort({ createdAt: -1 });
    res.status(200).json(tasks);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching tasks", error: error.message });
  }
};

// Get a single task
export const getTask = async (req, res) => {
  try {
    const { meetingId, taskId } = req.params;
    const task = await Task.findOne({ _id: taskId, meeting: meetingId })
      .populate('assignedTo', 'name designation');

    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    res.status(200).json(task);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching task", error: error.message });
  }
};

// Create a new task for a meeting
export const createTask = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { title, description, priority, status, assignedTo, deadline } = req.body;

    const task = new Task({
      title,
      description,
      priority,
      status,
      meeting: meetingId,
      assignedTo: assignedTo || [],
      deadline: deadline ? new Date(deadline) : null
    });

    await task.save();
    const meeting = await Meeting.findById(meetingId);
    if (meeting) {
      meeting.tasks.push(task._id);
      await meeting.save();
    }
    await task.populate('assignedTo', 'name designation');
    res.status(201).json(task);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: "Error creating task", error: error.message });
  }
};

// Update a task
export const updateTask = async (req, res) => {
  const updates = Object.keys(req.body);
  const allowedUpdates = ["title", "description", "priority", "status", "assignedTo", "deadline"];
  console.log(updates, allowedUpdates)
  const isValidOperation = updates.every(update => allowedUpdates.includes(update));

  if (!isValidOperation) {
    return res.status(400).json({ message: "Invalid updates" });
  }

  try {
    const { meetingId, taskId } = req.params;
    const task = await Task.findOne({ _id: taskId, meeting: meetingId });

    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    updates.forEach(update => {
      if (update === 'deadline' && req.body[update]) {
        task[update] = new Date(req.body[update]);
      } else {
        task[update] = req.body[update];
      }
    });

    await task.save();
    await task.populate('assignedTo', 'name designation');

    res.status(200).json(task);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: "Error updating task", error: error.message });
  }
};

// Delete a task
export const deleteTask = async (req, res) => {
  try {
    const { meetingId, taskId } = req.params;
    const task = await Task.findOneAndDelete({ _id: taskId, meeting: meetingId });

    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    // If task has an image, delete it
    if (task.imageUrl) {
      const imagePath = path.join(uploadsDir, path.basename(task.imageUrl));
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    res.status(200).json({ message: "Task deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error deleting task", error: error.message });
  }
};

// Upload image and extract tasks for a meeting
export const uploadImageForMeeting = async (req, res) => {
  try {
    const { meetingId } = req.params;

    if (!req.file) {
      return res.status(400).json({ message: "No image file provided" });
    }

    const imageUrl = `/uploads/${req.file.filename}`;
    const imagePath = req.file.path;

    // Use Gemini Vision API for OCR and task extraction
    const { extractedText, suggestedTasks } = await extractTasksFromImageWithGemini(imagePath);

    res.status(200).json({
      imageUrl,
      extractedText,
      suggestedTasks,
      meetingId
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error processing image", error: error.message });
  }
};

// Create tasks from extracted text for a meeting
export const createTasksFromImageForMeeting = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { tasks, imageUrl, originalText } = req.body;

    if (!tasks || !Array.isArray(tasks) || tasks.length === 0) {
      return res.status(400).json({ message: "No tasks provided" });
    }

    const createdTasks = [];

    for (const taskData of tasks) {
      const task = new Task({
        ...taskData,
        meeting: meetingId,
        imageUrl,
        originalText,
        deadline: taskData.deadline ? new Date(taskData.deadline) : null
      });

      await task.save();
      await task.populate('assignedTo', 'name designation');
      createdTasks.push(task);
    }

    res.status(201).json(createdTasks);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: "Error creating tasks", error: error.message });
  }
};

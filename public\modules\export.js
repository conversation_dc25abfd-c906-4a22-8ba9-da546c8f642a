// import PptxGenJS from "../packages/pptxgenjs/dist/pptxgen.es.js";
// import { showNotification } from "./notifications.js";

// /**
//  * Convert a data URL to a Blob
//  * @param {string} dataUrl - The data URL to convert
//  * @returns {Blob} - The resulting Blob
//  */
// function dataURLtoBlob(dataUrl) {
//   const arr = dataUrl.split(',');
//   const mime = arr[0].match(/:(.*?);/)[1];
//   const bstr = atob(arr[1]);
//   let n = bstr.length;
//   const u8arr = new Uint8Array(n);
//   while (n--) {
//     u8arr[n] = bstr.charCodeAt(n);
//   }
//   return new Blob([u8arr], { type: mime });
// }

// /**
//  * Extract the base64 data from a data URL
//  * @param {string} dataUrl - The data URL
//  * @returns {string} - The base64 data
//  */
// function extractBase64FromDataUrl(dataUrl) {
//   return dataUrl.split(',')[1];
// }

// /**
//  * Get the MIME type from a data URL
//  * @param {string} dataUrl - The data URL
//  * @returns {string} - The MIME type
//  */
// function getMimeTypeFromDataUrl(dataUrl) {
//   return dataUrl.split(',')[0].match(/:(.*?);/)[1];
// }

// /** @param {{ id: string; editor: import("../packages/editorjs/types/index.js").default }[]} slides */
// export async function exportPPTX(slides) {
//   // 1. Collect JSON from all slides
//   const allSlidesData = await Promise.all(
//     slides.map(s => s.editor.save().then(data => data.blocks))
//   );

//   // 2. Initialize PPTX
//   const pptx = new PptxGenJS();
//   pptx.layout = 'LAYOUT_WIDE';  // optional

//   // 3. For each slide → one PowerPoint slide
//   allSlidesData.forEach((blocks) => {
//     const slide = pptx.addSlide();
//     let cursorY = 0.5;  // inches from top

//     blocks.forEach(block => {
//       switch (block.type) {
//         case 'header':
//           // Adjust font size based on header level
//           const headerLevel = block.data.level || 2;
//           const fontSize = headerLevel === 1 ? 32 :
//                           headerLevel === 2 ? 28 :
//                           headerLevel === 3 ? 24 :
//                           headerLevel === 4 ? 20 : 18;

//           slide.addText(block.data.text, {
//             x: 0.5, y: cursorY,
//             w: 9,  // width in inches
//             h: 0.8, // Fixed height for headers
//             fontSize: fontSize,
//             bold: true,
//             color: "363636", // Dark gray color
//             valign: "middle" // Center text vertically
//           });
//           cursorY += 0.9;  // move down with a bit more space after headers
//           break;

//         case 'paragraph':
//           // Calculate paragraph height based on text length
//           const textLength = block.data.text.length;
//           const estimatedLines = Math.ceil(textLength / 80); // Rough estimate: ~80 chars per line
//           const paragraphHeight = Math.max(0.4, 0.2 * estimatedLines);

//           slide.addText(block.data.text, {
//             x: 0.5, y: cursorY,
//             w: 9,
//             h: paragraphHeight,
//             fontSize: 14,
//             valign: "top",
//             wrap: true // Enable text wrapping
//           });
//           cursorY += paragraphHeight + 0.1; // Add a small gap after paragraph
//           break;

//         case 'list':
//           // Handle list blocks
//           if (block.data && block.data.items && block.data.items.length > 0) {
//             const isOrdered = block.data.style === "ordered";

//             // Extract list items without adding bullets/numbers in the text
//             const listItems = block.data.items.map(item => {
//               // Handle different list item formats
//               return typeof item === "string"
//                 ? item
//                 : (item.content || item.text || "");
//             });

//             // Calculate appropriate height based on number of items
//             const listHeight = Math.max(0.5, 0.3 * listItems.length);

//             if (isOrdered) {
//               // For ordered lists, we need to add the numbers manually
//               // because PptxGenJS doesn't have native ordered list support
//               const numberedText = listItems
//                 .map((item, idx) => `${idx + 1}. ${item}`)
//                 .join("\n");

//               slide.addText(numberedText, {
//                 x: 0.5, y: cursorY,
//                 w: 9,
//                 h: listHeight,
//                 fontSize: 14,
//                 valign: "top",
//                 bullet: false // No bullet for ordered lists
//               });
//             } else {
//               // For unordered lists, use PptxGenJS's built-in bullet support
//               // Join items with newlines but don't add bullet characters
//               const bulletText = listItems.join("\n");

//               slide.addText(bulletText, {
//                 x: 0.5, y: cursorY,
//                 w: 9,
//                 h: listHeight,
//                 fontSize: 14,
//                 valign: "top",
//                 bullet: true // Use PowerPoint's native bullets
//               });
//             }

//             // Move cursor down based on list height
//             cursorY += 0.4 * listItems.length;
//           } else {
//             // Skip if no list items
//             cursorY += 0.2;
//           }
//           break;

//         case 'image':
//           // Handle image blocks
//           if (block.data && block.data.file && block.data.file.url) {
//             try {
//               // Set reasonable image dimensions
//               const imgWidth = 6; // Width in inches
//               const imgHeight = 4; // Height in inches

//               // If caption exists, add it below the image
//               const hasCaption = block.data.caption && block.data.caption.trim().length > 0;

//               // Get the image URL (which is likely a data URL)
//               const imageUrl = block.data.file.url;

//               console.log('Exporting image:', {
//                 type: typeof imageUrl,
//                 length: imageUrl.length,
//                 preview: imageUrl.substring(0, 50) + '...'
//               });

//               // If it's a data URL, handle it appropriately
//               if (imageUrl.startsWith('data:image/')) {
//                 try {
//                   // First attempt: Use the full data URL
//                   slide.addImage({
//                     data: imageUrl,
//                     x: 0.5, y: cursorY,
//                     w: imgWidth,
//                     h: imgHeight
//                   });
//                 } catch (dataUrlError) {
//                   console.error('Error processing data URL (method 1):', dataUrlError);

//                   // Second attempt: Try with just the base64 part
//                   try {
//                     const base64Data = extractBase64FromDataUrl(imageUrl);
//                     const mimeType = getMimeTypeFromDataUrl(imageUrl);
//                     console.log('Extracted image data:', { mimeType, base64Length: base64Data.length });

//                     slide.addImage({
//                       data: base64Data,
//                       x: 0.5, y: cursorY,
//                       w: imgWidth,
//                       h: imgHeight
//                     });
//                   } catch (base64Error) {
//                     console.error('Error processing base64 data (method 2):', base64Error);

//                     // Third attempt: Try converting to Blob and then to base64
//                     try {
//                       // Convert data URL to Blob
//                       const blob = dataURLtoBlob(imageUrl);

//                       // Create a FileReader to read the Blob as array buffer
//                       const reader = new FileReader();
//                       reader.readAsArrayBuffer(blob);

//                       reader.onload = function() {
//                         try {
//                           // Convert array buffer to base64
//                           const bytes = new Uint8Array(reader.result);
//                           let binary = '';
//                           for (let i = 0; i < bytes.byteLength; i++) {
//                             binary += String.fromCharCode(bytes[i]);
//                           }
//                           const base64 = btoa(binary);

//                           // Add the image using the base64 data
//                           slide.addImage({
//                             data: base64,
//                             x: 0.5, y: cursorY,
//                             w: imgWidth,
//                             h: imgHeight
//                           });
//                         } catch (readerError) {
//                           console.error('Error processing image with FileReader (method 3):', readerError);
//                           throw new Error('Failed to add image to slide');
//                         }
//                       };

//                       reader.onerror = function() {
//                         console.error('FileReader error (method 3)');
//                         throw new Error('Failed to read image data');
//                       };
//                     } catch (blobError) {
//                       console.error('Error converting to Blob (method 3):', blobError);
//                       throw new Error('Failed to add image to slide');
//                     }
//                   }
//                 }
//               } else {
//                 // For regular URLs, use the path property
//                 slide.addImage({
//                   path: imageUrl,
//                   x: 0.5, y: cursorY,
//                   w: imgWidth,
//                   h: imgHeight
//                 });
//               }

//               // Add caption if it exists
//               if (hasCaption) {
//                 slide.addText(block.data.caption, {
//                   x: 0.5, y: cursorY + imgHeight + 0.1,
//                   w: imgWidth,
//                   h: 0.5,
//                   fontSize: 12,
//                   italic: true,
//                   align: 'center',
//                   color: '666666'
//                 });
//                 cursorY += imgHeight + 0.7; // Image height + caption + spacing
//               } else {
//                 cursorY += imgHeight + 0.3; // Just image height + spacing
//               }
//             } catch (error) {
//               console.error('Error adding image to PowerPoint:', error);
//               // Add a placeholder text instead
//               slide.addText('[ Image could not be exported ]', {
//                 x: 0.5, y: cursorY,
//                 w: 9,
//                 h: 0.5,
//                 fontSize: 14,
//                 italic: true,
//                 color: 'FF0000'
//               });
//               cursorY += 0.7; // Add some space after the error message
//             }
//           } else {
//             // Skip if no image URL
//             cursorY += 0.2;
//           }
//           break;

//         case 'table':
//           // Handle table blocks
//           if (block.data && block.data.content) {
//             // Convert EditorJS table format to PptxGenJS format
//             const tableRows = [];

//             // Process each row in the table
//             block.data.content.forEach(row => {
//               const tableRow = [];

//               // Process each cell in the row
//               row.forEach(cell => {
//                 // Add the cell text to the row
//                 tableRow.push(cell || ''); // Use empty string for null/undefined cells
//               });

//               // Add the row to the table
//               tableRows.push(tableRow);
//             });

//             // Calculate table dimensions
//             const numRows = tableRows.length;
//             const tableWidth = 9; // Width in inches (same as other elements)
//             const tableHeight = 0.4 * numRows; // Estimate height based on number of rows

//             // Add the table to the slide
//             slide.addTable(tableRows, {
//               x: 0.5,
//               y: cursorY,
//               w: tableWidth,
//               color: '363636',
//               fontSize: 14,
//               border: { type: 'solid', pt: 1, color: 'CFCFCF' },
//               valign: 'middle',
//               align: 'left',
//               rowH: 0.4 // Height per row in inches
//             });

//             // Move cursor down based on table height plus spacing
//             cursorY += tableHeight + 0.3;
//           }
//           break;

//         // handle other block types as needed…
//       }
//     });
//   });

//   // 4. Download
//   try {
//     // Count the number of slides and blocks
//     const numSlides = allSlidesData.length;
//     const totalBlocks = allSlidesData.reduce((total, slide) => total + slide.length, 0);

//     // Generate the PowerPoint file
//     pptx.writeFile({ fileName: 'presentation.pptx' });

//     // Show success notification with details
//     showNotification(`PowerPoint exported successfully with ${numSlides} slide${numSlides !== 1 ? 's' : ''}`, 'success');
//     console.log(`PowerPoint exported with ${numSlides} slides and ${totalBlocks} total blocks`);
//   } catch (error) {
//     console.error('Error exporting PowerPoint:', error);
//     showNotification('Failed to export PowerPoint file: ' + error.message, 'error');
//   }
// }

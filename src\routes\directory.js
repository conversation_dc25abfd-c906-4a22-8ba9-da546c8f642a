import express from "express";
import auth from "../middleware/auth.js";
import {
  getDirectories,
  getDirectory,
  createDirectory,
  updateDirectory,
  deleteDirectory,
  getDirectoryUsage
} from "../controllers/directoryController.js";

const router = express.Router();

router.get("/", auth, getDirectories);
router.get("/:id", auth, getDirectory);
router.get("/:id/usage", auth, getDirectoryUsage);
router.post("/", auth, createDirectory);
router.patch("/:id", auth, updateDirectory);
router.delete("/:id", auth, deleteDirectory);

export default router;

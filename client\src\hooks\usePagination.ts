import { useState, useEffect, useCallback, useRef } from 'react';

interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function usePagination<T>(
  fetchFunction: (params: PaginationParams) => Promise<PaginationResponse<T>>,
  initialParams: Partial<PaginationParams> = {}
) {
  const [page, setPageState] = useState(initialParams.page || 1);
  const [limit, setLimitState] = useState(initialParams.limit || 10);
  const [search, setSearchState] = useState(initialParams.search || '');
  const [sortBy, setSortByState] = useState(initialParams.sortBy || 'createdAt');
  const [sortOrder, setSortOrderState] = useState<'asc' | 'desc'>(initialParams.sortOrder || 'desc');
  const [loading, setLoading] = useState(true);

  const [data, setData] = useState<T[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,  });

  // Use a ref to prevent multiple simultaneous requests
  const isLoadingRef = useRef(false);

  const fetchData = useCallback(async () => {
    if (isLoadingRef.current) return; // Prevent concurrent requests
    
    isLoadingRef.current = true;
    setLoading(true);
    try {      const result = await fetchFunction({
        page,
        limit,
        search,
        sortBy,
        sortOrder,
      });
      
      // Handle data safely
      setData(result?.data || []);
      
      // Ensure pagination is properly structured
      if (result?.pagination && typeof result.pagination === 'object') {
        const safePageInfo = {
          page: Number(result.pagination.page) || 1,
          limit: Number(result.pagination.limit) || 10,
          total: Number(result.pagination.total) || 0,
          pages: Number(result.pagination.pages) || 0
        };
        setPagination(safePageInfo);
      } else {
        // Default pagination if not provided
        setPagination({
          page: 1,
          limit: 10,
          total: Array.isArray(result?.data) ? result.data.length : 0,
          pages: 1
        });
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
      setData([]);
      setPagination({ page: 1, limit: 10, total: 0, pages: 0 });
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [fetchFunction, page, limit, search, sortBy, sortOrder]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);  const setPage = useCallback((newPage: number) => {
    if (newPage !== page) {
      setPageState(newPage);
    }
  }, [page]);

  const setLimit = useCallback((newLimit: number) => {
    setLimitState(newLimit);
    setPageState(1); // Reset to first page when changing limit
  }, []);

  const setSearch = useCallback((newSearch: string) => {
    setSearchState(newSearch);
    setPageState(1); // Reset to first page when searching
  }, []);

  const setSorting = useCallback((newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortByState(newSortBy);
    setSortOrderState(newSortOrder);
  }, []);

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  };
}

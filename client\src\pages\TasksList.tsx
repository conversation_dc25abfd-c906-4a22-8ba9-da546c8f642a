import { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Title,
  Text,
  Group,
  Paper,
  Card,
  Badge,
  Loader,
  Stack,
  Select,
  SimpleGrid,
  Box,
  UnstyledButton,
  Divider,
  Collapse,
} from '@mantine/core';
import { 
  IconCalendarStats, 
  IconClock, 
  IconFilter, 
  IconUser, 
  IconBuilding,
  IconExclamationCircle,
  IconCircleCheck,
  IconAlertTriangle,
  IconCircle,
  IconChevronDown, 
  IconChevronUp} from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Task } from '../types';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';

type PopulatedTask = Omit<Task, "meeting"> & { meeting: { _id: string; title: string; committee: { _id: string; name: string } | null} };

export default function TasksList() {
  const navigate = useNavigate();
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [priorityFilter, setPriorityFilter] = useState<string | null>(null);
  const [committeeFilter, setCommitteeFilter] = useState<string | null>(null);
  const [committeeOptions, setCommitteeOptions] = useState<{value: string, label: string}[]>([]);
  const [dateFilter, _setDateFilter] = useState<string | null>(null);
  const [filtersCollapsed, setFiltersCollapsed] = useState(false);

  // Fetch function for pagination with filters
  const fetchTasks = useCallback(async (params: any) => {
    // Adding filters to the request
    const filters: Record<string, string> = {};
    if (statusFilter) filters.status = statusFilter;
    if (priorityFilter) filters.priority = priorityFilter;
    if (committeeFilter) filters.committeeId = committeeFilter;
    if (dateFilter) filters.dateRange = dateFilter;

    const response = await apiClient.get('/api/tasks', { 
      params: { 
        ...params,
        ...filters, 
        populate: 'assignedTo'
      }
    });
    return response.data;
  }, [statusFilter, priorityFilter, committeeFilter, dateFilter]);

  const {
    data: tasks,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
  } = usePagination<PopulatedTask>(fetchTasks, {
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  const sortOptions = [
    { value: 'createdAt', label: 'Date Created' },
    { value: 'deadline', label: 'Deadline' },
    { value: 'priority', label: 'Priority' },
    { value: 'status', label: 'Status' },
    { value: 'title', label: 'Title' },
  ];

  // Fetch committee options for filter
  useEffect(() => {
    const fetchCommittees = async () => {
      try {
        const response = await apiClient.get('/api/committee', { params: { limit: 100 } });
        const options = response.data.data.map((committee: any) => ({
          value: committee._id,
          label: committee.name,
        }));
        setCommitteeOptions(options);
      } catch (error) {
        console.error('Failed to fetch committees:', error);
      }
    };

    fetchCommittees();
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'blue';
      default:
        return 'gray';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'in-progress':
        return 'blue';
      case 'pending':
        return 'yellow';
      default:
        return 'gray';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <IconExclamationCircle size="1.25em" />;
      case 'medium':
        return <IconAlertTriangle size="1.25em" />;
      case 'low':
        return <IconCircle size="1.25em" />;
      default:
        return <IconCircle size="1.25em" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <IconCircleCheck size="1.2em" />;
      case 'pending':
        return <IconClock size="1.2em" />;
      default:
        return <IconClock size="1.2em" />;
    }
  };

  const handleTaskClick = (task: PopulatedTask) => {
    navigate(`/committee/${task.committeeId}/meetings/${task.meeting._id}?task=${task._id}`);
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) return 'Not set';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>Tasks</Title>
          <Text c="dimmed">Manage and track all tasks across committees</Text>
        </div>
      </Group>

      {/* Filters Section */}
      <Paper withBorder mb="lg" >
        <Group>
          <UnstyledButton 
            onClick={() => setFiltersCollapsed(!filtersCollapsed)} 
            style={{ width: '100%' }}
            px="md"
          >
            <Group justify="space-between">
              <Group gap="xs">
                <IconFilter size={16} />
                <Text fw={500} size="sm">Filters</Text>
              </Group>
              {filtersCollapsed ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
            </Group>
          </UnstyledButton>
        </Group>
            
        <Collapse in={!filtersCollapsed}>
          <Divider mt="md" />
          <Box p="md">
            <Group wrap="wrap">
              <Select
                label="Status"
                placeholder="Filter by status"
                value={statusFilter}
                onChange={setStatusFilter}
                data={[
                  { value: '', label: 'All Statuses' },
                  { value: 'pending', label: 'Pending' },
                  { value: 'completed', label: 'Completed' },
                ]}
                style={{ width: 150 }}
                clearable
              />
              
              <Select
                label="Priority"
                placeholder="Filter by priority"
                value={priorityFilter}
                onChange={setPriorityFilter}
                data={[
                  { value: '', label: 'All Priorities' },
                  { value: 'low', label: 'Low' },
                  { value: 'medium', label: 'Medium' },
                  { value: 'high', label: 'High' },
                ]}
                style={{ width: 150 }}
                clearable
              />
              
              <Select
                label="Committee"
                placeholder="Filter by committee"
                value={committeeFilter}
                onChange={setCommitteeFilter}
                data={[{ value: '', label: 'All Committees' }, ...committeeOptions]}
                style={{ width: 200 }}
                searchable
                clearable
              />
            </Group>
          </Box>
        </Collapse>
      </Paper>

      {/* Search and Pagination Controls */}
      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>

      {/* Task Cards */}
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '3rem 0' }}>
          <Loader />
        </div>
      ) : tasks.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No tasks found matching your filters</Text>
          </Stack>
        </Paper>
      ) : (
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="md">
          {tasks.map((task) => {
            return (
              <Card 
                key={task._id} 
                withBorder 
                shadow="sm" 
                padding="lg" 
                radius="md"
                style={{ cursor: 'pointer' }}
                onClick={() => handleTaskClick(task)}
              >
                <Card.Section withBorder inheritPadding py="xs">
                  <Group justify="space-between">
                    <Text fw={500} truncate>{task.title}</Text>
                    <Badge
                      variant="filled"
                      size="sm"
                      radius="sm"
                      leftSection={getPriorityIcon(task.priority)}
                      color={getPriorityColor(task.priority)}
                    >
                      {task.priority}
                    </Badge>
                  </Group>
                </Card.Section>

                <Stack gap="md" mt="md">
                  <Text lineClamp={2} size="sm" c="dimmed">
                    {task.description || "No description provided"}
                  </Text>
                  
                  <Group gap="xs" align="center">
                    <Badge
                      size="md"
                      radius="sm"
                      leftSection={getStatusIcon(task.status)}
                      color={getStatusColor(task.status)}
                    >
                      {task.status}
                    </Badge>
                  </Group>

                  {task.deadline && (
                    <Group gap="xs">
                      <IconCalendarStats size={16} />
                      <Text size="sm">Due: {formatDate(task.deadline)}</Text>
                    </Group>
                  )}

                  <Group justify="space-between">
                    <Group gap="xs">
                      <IconBuilding size={16} stroke={1.5} />
                      <Text size="sm" lineClamp={1}>
                        {task.meeting.committee?.name || "<Committee unavaiable>"}
                      </Text>
                    </Group>
                    
                    {/* {task.assignedTo && task.assignedTo.length > 0 && (
                      <Tooltip.Group openDelay={300} closeDelay={100}>
                        <Avatar.Group spacing="sm">
                          {task.assignedTo.slice(0, 3).map((user, index) => (
                            <Tooltip key={user._id} label={user.name}>
                              <Avatar 
                                radius="xl" 
                                size="sm"
                                color={['blue', 'cyan', 'green', 'teal', 'violet'][index % 5]}
                              >
                                {user.name.charAt(0)}
                              </Avatar>
                            </Tooltip>
                          ))}
                          
                          {task.assignedTo.length > 3 && (
                            <Avatar radius="xl" size="sm">
                              +{task.assignedTo.length - 3}
                            </Avatar>
                          )}
                        </Avatar.Group>
                      </Tooltip.Group>
                    )} */}
                  </Group>

                  {task.assignedTo && task.assignedTo.length > 0 && 
                  <Group>
                    <Group gap="xs">
                      <IconUser size={16} stroke={.5} />
                      <Text size="sm" lineClamp={1}>{task.assignedTo.map(u => u.name).join(", ")}</Text>
                    </Group>
                  </Group>}
                  
                  <Text size="xs" c="dimmed">
                    Meeting: {task.meeting.title || "<Meeting unavailable>"}
                  </Text>
                </Stack>
              </Card>
          )
        })}
        </SimpleGrid>
      )}
    </Container>
  );
}

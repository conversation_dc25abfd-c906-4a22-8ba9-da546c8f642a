import EditorJS from "../packages/editorjs/dist/editorjs.js";
import Header from "../packages/editorjs-header/dist/header.js";
import List from "../packages/editorjs-list/dist/editorjs-list.js";
import Marker from "../packages/editorjs-marker/dist/marker.js";
import Bold from "../packages/editorjs-bold/dist/bundle.js";
import Italic from "../packages/editorjs-italic/dist/bundle.js";
import Image from "../packages/editorjs-image/dist/bundle.js";
import Paragraph from "../packages/editorjs-paragraph/dist/paragraph.js";
import Table from "../packages/editorjs-table/dist/table.js";
// import { exportPPTX } from "./export.js";
import ElementsBuilder from "./elements-builder.js";
import { showNotification } from "./notifications.js";
import { saveToJson } from "./request.js";

Image.isReadOnlySupported = true;

const {
  div: Div,
  "sl-icon-button": SLIconButton,
  "sl-icon": SLIcon
} = ElementsBuilder.multiple("div", "sl-icon-button", "sl-icon");

export class EditorManager {

  #readonly;

  /**
   * @param {string} requestId
   * @param {{ readonly?: boolean }} options
   */
  constructor(requestId, options = {}) {
    /** @type {{ id: string; editor: EditorJS; title: string }[]} */
    this.slides = [];
    this.slideCounter = 0;
    this.activeSlideIndex = -1;
    this.grid = null;
    this.#readonly = Boolean(options?.readonly ?? false);

    // Get DOM elements
    this.editorContainer = document.getElementById('editor-container');
    this.slidesGrid = document.getElementById('slides-grid');
    this.addSlideBtn = document.getElementById('add-page-btn');

    // Get the main element (parent of editor container)
    this.mainElement = this.editorContainer.parentElement;

    // Set up event listeners
    this.setupEventListeners();

    // Initialize Muuri grid
    this.initMuuriGrid();

    this.requestId = requestId;
    
    // Apply initial readonly state to UI
    if (this.#readonly) {
      // Defer UI updates until DOM is ready
      setTimeout(() => this.setReadonly(this.#readonly), 100);
    }
  }

  /**
   * Initialize Muuri grid for drag-and-drop slide management
   */
  initMuuriGrid() {
    // Initialize Muuri grid if it doesn't exist
    if (!this.grid && this.slidesGrid) {
      this.grid = new Muuri(this.slidesGrid, {
        items: '.muuri-item',
        dragEnabled: !this.#readonly,
        dragSort: !this.#readonly,
        dragStartPredicate: {
          distance: 10,
          delay: 100
        },
        dragSortHeuristics: {
          sortInterval: 50
        },
        layout: {
          fillGaps: false,
          horizontal: false,
          alignRight: false,
          alignBottom: false,
          rounding: false,
          spacing: 4 // Reduced spacing between items
        },
        dragAutoScroll: {
          targets: [window],
          sortDuringScroll: true
        },
        itemDraggingClass: 'muuri-item-dragging',
        itemReleasingClass: 'muuri-item-releasing',
        layoutOnInit: true,
        layoutOnResize: true,
        layoutDuration: 200,
        layoutEasing: 'ease-out'
      });

      // Force layout after initialization
      setTimeout(() => {
        this.grid.refreshItems().layout();

        // Make sure the grid is properly sized
        const gridElement = document.getElementById('slides-grid');
        if (gridElement) {
          gridElement.style.minHeight = '100%';
        }
      }, 100);

      // Listen for order change
      this.grid.on('dragReleaseEnd', () => {
        this.updateSlidesOrder();
      });

      // Add click event listeners to each item
      const items = this.grid.getItems();
      items.forEach(item => {
        const element = item.getElement();
        element.addEventListener('click', () => {
          const index = parseInt(element.dataset.index, 10);
          if (!isNaN(index)) {
            this.setActiveSlide(index);
          }
        });
      });

      // Listen for new items being added
      this.grid.on('add', (items) => {
        items.forEach(item => {
          const element = item.getElement();
          element.addEventListener('click', () => {
            const index = parseInt(element.dataset.index, 10);
            if (!isNaN(index)) {
              this.setActiveSlide(index);
            }
          });
        });
      });
    }
  }

  /**
   * Update slides order after drag and drop
   */
  updateSlidesOrder() {
    if (!this.grid) return;
    
    // Update the indices in the DOM
    const items = this.grid.getItems();
    items.forEach((item, newIndex) => {
      const element = item.getElement();
      element.dataset.index = newIndex;
      element.dataset.slideId = `slide-${newIndex}`;
      const slideNumber = element.querySelector(".slide-number")
      slideNumber.innerHTML = newIndex + 1;
    });

    // Update the slides array
    this.slides.map((slide, newIndex) => {
      slide.id = `slide-${newIndex + 1}`;
      slide.title = `Slide ${newIndex + 1}`;
    })
    

    // Update the active slide
    const slideContainer = document.getElementById("editor-container");
    const children = Array.from(slideContainer.children);
    children.map((child, newIndex) => {
      child.id = `slide-${newIndex + 1}`;
    })
  }

  /**
   * Initialize the editor
   */
  init() {
    // Clear any existing content
    this.editorContainer.innerHTML = '';

    // Clear Muuri grid
    if (this.grid) {
      this.grid.remove(this.grid.getItems(), { removeElements: true });
    }

    // Remove slide heading container if it exists
    const headingContainer = document.getElementById('slide-heading-container');
    if (headingContainer) {
      headingContainer.remove();
    }

    // Remove footer if it exists
    const slideFooter = document.getElementById('slide-footer');
    if (slideFooter) {
      slideFooter.remove();
    }

    this.slides = [];
    this.slideCounter = 0;
    this.activeSlideIndex = -1;

    // Add first slide automatically
    this.addSlide();
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Add slide button - only if not readonly
    if (this.addSlideBtn && !this.#readonly) {
      this.addSlideBtn.addEventListener('click', () => this.addSlide());
    }

    // Export buttons
    const saveJsonBtn = document.getElementById('save-json-btn');
    if (saveJsonBtn) {
      saveJsonBtn.addEventListener('click', () => this.saveJSON());
    }

    const exportPptxBtn = document.getElementById('export-pptx-btn');
    if (exportPptxBtn) {
      exportPptxBtn.addEventListener('click', () => this.exportToPDF());
    }

    // File upload - only if not readonly
    const newButton = document.getElementById('upload-file-button');
    const fileUploadInput = document.getElementById('file-upload-input');
    if (newButton && fileUploadInput && !this.#readonly) {
      newButton.addEventListener('click', () => fileUploadInput.click());
      fileUploadInput.addEventListener('change', (event) => {
        if (event.target.files && event.target.files.length > 0) {
          const file = event.target.files[0];
          this.handleFileUpload(file);
        }
      });
    }


    // Add window resize handler to refresh Muuri grid and update footer position
    window.addEventListener('resize', () => {
      if (this.grid) {
        // Debounce the resize event
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
          this.grid.refreshItems().layout();

          // Update footer position
          const slideFooter = document.getElementById('slide-footer');
          if (slideFooter) {
            const sidebar = document.querySelector('aside');
            if (sidebar) {
              slideFooter.style.left = `${sidebar.offsetWidth}px`;
            }
          }
        }, 100);
      }
    });

    // No need for tab selection event listener with Muuri grid
    // The click event is handled in the initMuuriGrid method
  }

  /**
   * Add a new slide with optional blocks data
   */
  addSlide(blocks = []) {
    // Prevent adding slides in readonly mode
    if (this.#readonly) {
      showNotification('Cannot add slides in readonly mode', 'warning');
      return null;
    }

    this.slideCounter++;
    const slideId = `slide-${this.slideCounter}`;
    const slideTitle = `Slide ${this.slideCounter}`;

    // Create sidebar tab item (this also adds it to the DOM)
    this.createSlideTreeItem(slideId, slideTitle, this.slideCounter - 1);

    // Create slide element (hidden initially) with 16:9 aspect ratio
    const slideDiv = new Div({
      id: slideId,
      className: "page",
      $styles: {
        display: "none",
        aspectRatio: "16/9",
        maxWidth: "100%",
        maxHeight: "100%",
        margin: "0 auto",
        boxSizing: "border-box",
        overflow: "overlay",
        backgroundColor: "#ffffff",
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)"
      }
    });
    this.editorContainer.appendChild(slideDiv);

    // Initialize EditorJS
    const editor = new EditorJS({
      holder: slideId,
      readOnly: this.#readonly,
      tools: this.#readonly ? {} : {
        header: {
          class: Header,
          config: {
            levels: [1, 2, 3, 4],
            defaultLevel: 2
          }
        },
        paragraph: {
          class: Paragraph,
          inlineToolbar: true
        },
        list: {
          class: List,
          inlineToolbar: true
        },
        table: {
          class: Table,
          inlineToolbar: true,
          config: {
            rows: 2,
            cols: 2
          }
        },
        bold: Bold,
        italic: Italic,
        marker: Marker,
        image: {
          class: Image,
          config: {
            uploader: {
              uploadByFile(file) {
                return new Promise((resolve) => {
                  try {
                    // Convert the file to a data URL
                    const reader = new FileReader();
                    reader.onload = function(e) {
                      resolve({
                        success: 1,
                        file: {
                          url: e.target.result,
                          // Add these properties to prevent errors during save
                          name: file.name || 'image',
                          size: file.size || 0,
                          extension: file.name ? file.name.split('.').pop() : 'jpg',
                          // Add src property to prevent "Cannot read properties of null (reading 'src')" error
                          src: e.target.result
                        }
                      });
                    };
                    reader.onerror = function() {
                      // Handle read errors
                      console.error('Error reading file');
                      resolve({
                        success: 0,
                        file: {
                          url: '',
                          name: '',
                          size: 0,
                          extension: '',
                          src: ''
                        }
                      });
                    };
                    reader.readAsDataURL(file);
                  } catch (error) {
                    console.error('Error in uploadByFile:', error);
                    resolve({
                      success: 0,
                      file: {
                        url: '',
                        name: '',
                        size: 0,
                        extension: '',
                        src: ''
                      }
                    });
                  }
                });
              },
              uploadByUrl(url) {
                return Promise.resolve({
                  success: 1,
                  file: {
                    url: url,
                    // Add these properties to prevent errors during save
                    name: 'image-from-url',
                    size: 0,
                    extension: url.split('.').pop() || 'jpg',
                    // Add src property to prevent "Cannot read properties of null (reading 'src')" error
                    src: url
                  }
                });
              }
            }
          }
        }
      },
      placeholder: this.#readonly ? "" : "Start writing...",
      data: blocks.length ? { blocks } : undefined,
      minHeight: 200,
      onChange: this.#readonly ? undefined : () => {
        this.updateSlideTitleFromContent(this.slides.length - 1);
      }
    });

    // Track the slide
    const slide = { id: slideId, editor, title: slideTitle };
    this.slides.push(slide);

    // Initialize the preview with the initial blocks
    if (this.grid) {
      const items = this.grid.getItems();
      const item = items.find(item => parseInt(item.getElement().dataset.index, 10) === this.slides.length - 1);
      if (item) {
        const element = item.getElement();
        this.updateSlidePreview(element, blocks);
      }
    }

    // Set as active if it's the first slide or explicitly requested
    if (this.slides.length === 1 || this.activeSlideIndex === -1) {
      this.setActiveSlide(this.slides.length - 1);
    }

    return slide;
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use addSlide instead
   */
  addPage(blocks = []) {
    return this.addSlide(blocks);
  }

  /**
   * Create a Muuri item for the sidebar
   */
  createSlideTreeItem(slideId, _slideTitle, index) {
    // Create the Muuri item container
    const item = new Div({
      className: 'muuri-item',
      $attributes: {
        'data-slide-id': slideId,
        'data-index': index.toString()
      }
    });

    // Create the item content with modern design
    const itemContent = new Div({
      className: 'muuri-item-content'
    });

    // Add slide number indicator
    const slideNumber = new Div({
      className: 'slide-number',
      textContent: `${index + 1}`
    });
    itemContent.appendChild(slideNumber);

    // Create a preview container to show slide content with 16:9 aspect ratio
    const previewContainer = new Div({
      className: 'preview-container'
    });

    // Add a placeholder initially (will be replaced with actual content)
    const placeholder = new Div({
      $styles: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        opacity: '0.3'
      }
    });

    const placeholderIcon = new SLIcon({
      $attributes: {
        name: 'card-text'
      },
      $styles: {
        fontSize: '1.5rem',
        color: 'var(--sl-color-neutral-300)'
      }
    });

    placeholder.appendChild(placeholderIcon);
    previewContainer.appendChild(placeholder);

    // Add preview container to item content
    itemContent.appendChild(previewContainer);

    // Add slide actions (delete button) - only if not readonly
    if (!this.#readonly) {
      const slideActions = new Div({
        className: 'slide-actions'
      });

      // Add delete button
      const deleteBtn = new SLIconButton({
        $attributes: {
          name: "trash",
          label: "Delete slide",
          size: "small"
        },
        $styles: {
          color: 'var(--sl-color-neutral-500)'
        },
        $listeners: {
          click: (event) => {
            event.stopPropagation();
            const currIndex = parseInt(event.target.closest('.muuri-item').dataset.index, 10);
            this.deleteSlide(currIndex);
          }
        }
      });
      slideActions.appendChild(deleteBtn);
      itemContent.appendChild(slideActions);
    }

    // Add item content to itemx
    item.appendChild(itemContent);

    // Add item to grid
    this.slidesGrid.appendChild(item);

    // Add the item to the Muuri grid
    if (this.grid) {
      this.grid.add(item);
    }

    return item;
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use createSlideTreeItem instead
   */
  createPageListItem(pageId, pageTitle, index) {
    return this.createSlideTreeItem(pageId, pageTitle, index);
  }

  /**
   * Set the active slide
   */
  setActiveSlide(index) {
    if (index < 0 || index >= this.slides.length) return;

    // Hide all slides
    for (const slide of this.slides) {
      const slideElement = document.getElementById(slide.id);
      if (slideElement) {
        slideElement.style.display = 'none';
      }
    }

    // Show the selected slide
    const selectedSlide = this.slides[index];
    const slideElement = document.getElementById(selectedSlide.id);
    if (slideElement) {
      // Remove slide number heading if it exists
      let slideNumberHeading = document.getElementById('slide-number-heading');
      if (slideNumberHeading) {
        const headingContainer = document.getElementById('slide-heading-container');
        if (headingContainer) {
          headingContainer.remove();
        }
      }

      // Create or update slide footer with slide number
      let slideFooter = document.getElementById('slide-footer');
      if (!slideFooter) {
        // Create a footer container
        slideFooter = new Div({
          id: 'slide-footer',
          $styles: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '0 20px',
            borderTop: '1px solid #e2e8f0',
            backgroundColor: '#fff',
            position: 'fixed',
            bottom: '0',
            left: '240px', // Width of sidebar
            right: '0',
            zIndex: '10',
            height: '36px',
            boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.05)'
          }
        });

        // Create slide number indicator
        const slideNumberIndicator = new Div({
          id: 'slide-number-indicator',
          textContent: `Slide ${index + 1}`,
          $styles: {
            fontSize: '14px',
            fontWeight: '600',
            color: '#1e293b'
          }
        });

        slideFooter.appendChild(slideNumberIndicator);

        // Add some placeholder actions on the right side
        const footerActions = new Div({
          $styles: {
            display: 'flex',
            gap: '8px'
          }
        });

        // Add zoom control
        const zoomControl = new Div({
          textContent: '100%',
          $styles: {
            fontSize: '13px',
            color: '#64748b',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            fontWeight: '500'
          }
        });

        const zoomIcon = new SLIcon({
          $attributes: {
            name: 'zoom-in'
          },
          $styles: {
            fontSize: '14px'
          }
        });

        zoomControl.prepend(zoomIcon);
        footerActions.appendChild(zoomControl);

        slideFooter.appendChild(footerActions);

        // Add to DOM
        document.body.appendChild(slideFooter);
      } else {
        // Just update the slide number
        const slideNumberIndicator = document.getElementById('slide-number-indicator');
        if (slideNumberIndicator) {
          slideNumberIndicator.textContent = `Slide ${index + 1}`;
        }
      }

      // Show the slide
      slideElement.style.display = 'block';
    }

    // Select the item in the Muuri grid
    if (this.grid) {
      const items = this.grid.getItems();
      items.forEach((item, i) => {
        const element = item.getElement();
        if (i === index) {
          element.classList.add('muuri-item-selected');
        } else {
          element.classList.remove('muuri-item-selected');
        }
      });
    }

    this.activeSlideIndex = index;
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use setActiveSlide instead
   */
  setActivePage(index) {
    return this.setActiveSlide(index);
  }

  /**
   * Delete a slide
   */
  deleteSlide(index) {
    // Prevent deleting slides in readonly mode
    if (this.#readonly) {
      showNotification('Cannot delete slides in readonly mode', 'warning');
      return;
    }

    if (index < 0 || index >= this.slides.length || this.slides.length <= 1) {
      // Don't allow deleting the last slide
      if (this.slides.length <= 1) {
        showNotification('Cannot delete the only slide', 'error');
      }
      return;
    }

    // Get the slide to delete
    const slideToDelete = this.slides[index];

    // Remove from DOM
    const slideElement = document.getElementById(slideToDelete.id);
    if (slideElement) {
      slideElement.remove();
    }

    // Update footer if it exists
    const slideNumberIndicator = document.getElementById('slide-number-indicator');
    if (slideNumberIndicator && this.activeSlideIndex >= 0) {
      slideNumberIndicator.textContent = `Slide ${this.activeSlideIndex + 1}`;
    }

    // Remove from Muuri grid
    if (this.grid) {
      const items = this.grid.getItems();
      const itemToRemove = items.find(item => parseInt(item.getElement().dataset.index, 10) === index);

      if (itemToRemove) {
        this.grid.remove([itemToRemove], { removeElements: true });
      }
    }

    // Remove from slides array
    this.slides.splice(index, 1);

    // Update indices in sidebar
    this.updateSlidesOrder()


    // Set active slide
    if (this.activeSlideIndex === index) {
      // If we deleted the active slide, activate the previous slide or the first slide
      const newIndex = Math.max(0, index - 1);
      this.setActiveSlide(newIndex);
    } else if (this.activeSlideIndex > index) {
      // If we deleted a slide before the active slide, adjust the active index
      this.activeSlideIndex--;
    }
  }

  clearAllSlides() {
    // Remove all slides from the DOM
    this.slides.forEach(slide => {
      const slideElement = document.getElementById(slide.id);
      if (slideElement) {
        slideElement.remove();
      }
    });

    // Clear Muuri grid
    if (this.grid) {
      this.grid.remove(this.grid.getItems(), { removeElements: true });
    }

    this.slides = [];
    this.slideCounter = 0;
    this.activeSlideIndex = -1;

  }


  /**
   * Legacy method for backward compatibility
   * @deprecated Use deleteSlide instead
   */
  deletePage(index) {
    return this.deleteSlide(index);
  }

  /**
   * Update slide preview from content
   */
  async updateSlideTitleFromContent(index) {
    if (index < 0 || index >= this.slides.length) return;

    try {
      const slide = this.slides[index];

      // Check if editor exists
      if (!slide || !slide.editor) {
        console.warn('Slide or editor not found for index:', index);
        return;
      }

      // Save editor content with error handling
      let data;
      try {
        data = await slide.editor.save();
      } catch (saveError) {
        console.error('Error saving editor content:', saveError);
        // Create a minimal valid data structure to prevent further errors
        data = { blocks: [] };
      }

      // Ensure data.blocks exists
      if (!data || !data.blocks) {
        console.warn('Invalid data structure returned from editor.save()');
        data = { blocks: [] };
      }

      // Update preview in Muuri grid
      if (this.grid) {
        const items = this.grid.getItems();
        const item = items.find(item => parseInt(item.getElement().dataset.index, 10) === index);

        if (item) {
          const element = item.getElement();

          // Update the preview content
          this.updateSlidePreview(element, data.blocks);
        }
      }
    } catch (error) {
      console.error('Error updating slide preview:', error);
      // Don't throw the error further, just log it
    }
  }

  /**
   * Update the preview content in a slide item
   */
  updateSlidePreview(element, blocks) {
    // Find the preview container
    const previewContainer = element.querySelector('.muuri-item-content .preview-container');
    if (!previewContainer) return;

    // Clear existing preview content
    previewContainer.innerHTML = '';

    // Create a simplified preview of the slide content
    if (blocks && blocks.length) {
      // Get up to 3 blocks to show in the preview
      const previewBlocks = blocks.slice(0, 3);

      // Create a content wrapper with padding
      const contentWrapper = new Div({
        $styles: {
          padding: '6px',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          boxSizing: 'border-box',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }
      });

      previewBlocks.forEach(block => {
        try {
          if (block.type === 'header') {
            // Create a header preview
            const headerPreview = new Div({
              className: 'preview-header',
              textContent: block.data?.text || '',
              $styles: {
                fontSize: '9px',
                fontWeight: 'bold',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                marginBottom: '4px',
                color: '#1e293b'
              }
            });
            contentWrapper.appendChild(headerPreview);
          } else if (block.type === 'paragraph') {
            // Create a paragraph preview
            const paragraphPreview = new Div({
              className: 'preview-paragraph',
              textContent: block.data?.text || '',
              $styles: {
                fontSize: '8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                marginBottom: '3px',
                color: '#64748b'
              }
            });
            contentWrapper.appendChild(paragraphPreview);
          } else if (block.type === 'list') {
            // Create a list preview
            const listPreview = new Div({
              className: 'preview-list',
              textContent: '• List item...',
              $styles: {
                fontSize: '8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                marginBottom: '3px',
                color: '#64748b'
              }
            });
            contentWrapper.appendChild(listPreview);
          } else if (block.type === 'image') {
            // Create an image preview
            const imagePreview = new Div({
              className: 'preview-image',
              textContent: '📷 Image',
              $styles: {
                fontSize: '8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                marginBottom: '3px',
                color: '#64748b'
              }
            });
            contentWrapper.appendChild(imagePreview);
          } else if (block.type === 'table') {
            // Create a table preview
            const tablePreview = new Div({
              className: 'preview-table',
              textContent: '📊 Table',
              $styles: {
                fontSize: '8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                marginBottom: '3px',
                color: '#64748b'
              }
            });
            contentWrapper.appendChild(tablePreview);
          }
        } catch (error) {
          console.error('Error creating preview for block:', error, block);
        }
      });

      previewContainer.appendChild(contentWrapper);

      // If no blocks were rendered, show a placeholder
      if (contentWrapper.children.length === 0) {
        this.addPreviewPlaceholder(previewContainer);
      }
    } else {
      // No blocks, show a placeholder
      this.addPreviewPlaceholder(previewContainer);
    }
  }

  /**
   * Add a placeholder to the preview container
   */
  addPreviewPlaceholder(container) {
    const placeholder = new Div({
      $styles: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: '0.3',
        backgroundColor: '#f1f5f9'
      }
    });

    const placeholderIcon = new SLIcon({
      $attributes: {
        name: 'card-text'
      },
      $styles: {
        fontSize: '1.2rem',
        color: '#94a3b8'
      }
    });

    placeholder.appendChild(placeholderIcon);
    container.appendChild(placeholder);
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use updateSlideTitleFromContent instead
   */
  async updatePageTitleFromContent(index) {
    return this.updateSlideTitleFromContent(index);
  }

  /**
   * Clean up resources when the component is destroyed
   */
  destroy() {
    // Remove footer if it exists
    const slideFooter = document.getElementById('slide-footer');
    if (slideFooter) {
      slideFooter.remove();
    }

    // Remove any other DOM elements or event listeners
    const headingContainer = document.getElementById('slide-heading-container');
    if (headingContainer) {
      headingContainer.remove();
    }
  }

  /**
   * Load sample data into the editor
   */
  loadSampleData(sampleData) {
    if (!Array.isArray(sampleData) || sampleData.length === 0) {
      return;
    }

    // In readonly mode, we can still load data for viewing
    // Clear existing content
    this.editorContainer.innerHTML = '';

    // Clear Muuri grid
    if (this.grid) {
      this.grid.remove(this.grid.getItems(), { removeElements: true });
    }

    // Remove slide heading container if it exists
    const headingContainer = document.getElementById('slide-heading-container');
    if (headingContainer) {
      headingContainer.remove();
    }

    this.slides = [];
    this.slideCounter = 0;
    this.activeSlideIndex = -1;

    // Add slides for each sample data item
    for (const data of sampleData) {
      if (data.blocks && data.blocks.length) {
        this.addSlide(data.blocks);
      }
    }
  }
  

  /**
   * Save editor content as JSON
   */
  async saveJSON() {
    try {
      // Show loading notification
      showNotification('Preparing JSON data...', 'info');

      // Collect data from all slides
      const slidesData = await Promise.all(
        this.slides.map(slide => slide.editor.save())
      );

      // Create a JSON blob
      const objToSave = (slidesData ?? []).map(ele => JSON.stringify(ele));
      await saveToJson(this.requestId, objToSave);
      showNotification('JSON data saved successfully!', 'success');
      console.log('JSON data saved successfully');
    } catch (error) {
      console.error('Error saving JSON data:', error);

      // Show error notification
      showNotification('Error saving JSON data: ' + error.message, 'error');
    }
  }

  /**
   * Export editor content as PowerPoint
   */
  async exportPPTX() {
    try {
      // Show loading notification
      showNotification('Generating PowerPoint presentation...', 'info');

      // Use the exportPPTX function from export.js
      await exportPPTX(this.slides);

      // Show success notification
      showNotification('PowerPoint exported successfully!', 'success');

      console.log('PowerPoint exported successfully');
    } catch (error) {
      console.error('Error exporting PowerPoint:', error);

      // Show error notification
      showNotification('Error exporting PowerPoint: ' + error.message, 'error');
    }
  }

  async handleFileUpload(file) {
    // Prevent file uploads in readonly mode
    if (this.#readonly) {
      showNotification('Cannot upload files in readonly mode', 'warning');
      return;
    }

    const fileExtension = file.name.split('.').pop().toLowerCase();
    console.log("file extension", fileExtension);
    if(fileExtension === "xlsx" || fileExtension === "xls") { 
      console.log("xlsx file uploaded", file.name, file.type);
      this.processExcel(file);
    }
    else if(fileExtension === "pdf") {
      console.log("pdf file uploaded", file.name, file.type);
      this.processPDF(file);
    }
  }

  /**
   * @param {File} file - The PDF file to process
   */
  async processPDF(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfData = await pdfjsLib.getDocument(arrayBuffer).promise;
      const numPages = pdfData.numPages;
  
      showNotification(`Processing PDF with ${numPages} pages...`, 'info');
      
      this.clearAllSlides();

      this.addSlide([
        {
          type: "header", 
          data: {
            text: file.name, 
            level: 1
          }
        }, 
        {
          type: "paragraph",
          data: {
            text: `${numPages} number of pages`
          }
        }
      ]);

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {

        const page = await pdfData.getPage(pageNum);
        
        const viewport = page.getViewport({ scale: 1.5 });
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        
        await page.render({
          canvasContext: context,
          viewport: viewport
        }).promise;
        
        const imageDataUrl = canvas.toDataURL('image/jpeg', 0.75);
        
        const blocks = [
          {
            type: "header",
            data: {
              text: `Page ${pageNum}`,
              level: 2
            }
          }
        ];
        blocks.push({
          type: "image",
          data: {
            url: imageDataUrl,
            withBorder: false,
            stretched: false,
            withBackground: false
          }
        });
        
        this.addSlide(blocks);
      }
      
    } catch (error) {
      console.error('Error processing PDF:', error);
      showNotification('Error processing PDF: ' + error.message, 'error');
    }
  }

  /**
   * @param {File} file - The PDF file to process
   */
  async processExcel(file) {
    console.log("sdfmj");
    const data = await file.arrayBuffer();
    console.log("sdfmj");
    const workbook = XLSX.read(data, { type: 'array' });

    this.clearAllSlides();

    this.addSlide([
      {
        type: "header", 
        data: {
          text: file.name, 
          level: 1
        }
      }, 
      {
        type: "paragraph",
        data: {
          text: `${workbook.SheetNames.length} sheets found`
        }
      }
    ]);
    
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length === 0) continue;
      const headers = jsonData[0];
    
      if (!headers || headers.length === 0) continue;
      
      this.addSlide([
        {
          type: "header",
          data: {
            text: `Sheet: ${sheetName}`,
            level: 2
          }
        },
        {
          type: "paragraph",
          data: {
            text: `Total rows: ${jsonData.length - 1}, Total columns: ${headers.length}`
          }
        }
      ]);
      
      const rowsPerSlide = 8;
      const dataRows = jsonData.slice(1);
      
      for (let i = 0; i < dataRows.length; i += rowsPerSlide) {
        const chunk = dataRows.slice(i, i + rowsPerSlide);
        const tableContent = [headers, ...chunk];

        const cleanedTableContent = tableContent.map(row => 
          row.map(cell => 
            cell === undefined || cell === null ? '' : String(cell)
          )
        );

        this.addSlide([
          {
            type: "header",
            data: {
              text: `${sheetName} (Rows ${i + 1} to ${Math.min(i + rowsPerSlide, dataRows.length)})`,
              level: 3
            }
          },
          {
            type: "table",
            data: {
              withHeadings: true,
              content: cleanedTableContent
            }
          }
        ]);
      }
    }
  }

  /**
   * Clear all slides
   */
  clearAllSlides() {
    // Prevent clearing slides in readonly mode
    if (this.#readonly) {
      showNotification('Cannot clear slides in readonly mode', 'warning');
      return;
    }

    // Clear existing content
    this.editorContainer.innerHTML = '';

    // Clear Muuri grid
    if (this.grid) {
      this.grid.remove(this.grid.getItems(), { removeElements: true });
    }

    // Remove slide heading container if it exists
    const headingContainer = document.getElementById('slide-heading-container');
    if (headingContainer) {
      headingContainer.remove();
    }

    this.slides = [];
    this.slideCounter = 0;
    this.activeSlideIndex = -1;
  }

  /**
   * Get all slides
   */
  getSlides() {
    return this.slides;
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use getSlides instead
   */
  getPages() {
    return this.slides;
  }

  async exportToPDF() {
    try {
      const { PDFDocument, rgb, StandardFonts } = PDFLib;
      
      showNotification("Generating PDF...", "info");
      
      if (window.fontkit && !PDFDocument.prototype._fontkit) {
        PDFDocument.prototype._fontkit = window.fontkit;
      }

      const pdfDoc = await PDFDocument.create();
      
      let font;
      
      try {
        // Try to load a Unicode-compatible font
        const fontResponse = await fetch('https://cdn.jsdelivr.net/npm/noto-sans@0.1.1/NotoSans-Regular.ttf');
        if (fontResponse.ok) {
          const fontBytes = await fontResponse.arrayBuffer();
          font = await pdfDoc.embedFont(fontBytes);
        } else {
          font = await pdfDoc.embedFont(StandardFonts.Helvetica);
        }
      } catch (fontError) {
        font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      }

      if (this.slides.length === 0) {
        showNotification("No slides to export!", "error");
        return;
      }
      
      const pageWidth = 612;
      const pageHeight = 792;
      const margin = 50;
      const contentWidth = pageWidth - (margin * 2);

      // Helper function to wrap text
      function wrapText(text, maxWidth, fontSize) {
        const charsPerLine = Math.floor(maxWidth / (fontSize * 0.5));
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';
        
        words.forEach(word => {
          const testLine = currentLine ? `${currentLine} ${word}` : word;
          if (testLine.length <= charsPerLine) {
            currentLine = testLine;
          } else {
            lines.push(currentLine);
            currentLine = word;
          }
        });
        
        if (currentLine) {
          lines.push(currentLine);
        }
        
        return lines.length > 0 ? lines : [''];
      }

      for (const slide of this.slides) {
        const slideData = await slide.editor.save();
        
        let page = pdfDoc.addPage([pageWidth, pageHeight]);
        let yPosition = pageHeight - margin;
        
        const safeDrawText = (text, options) => {
          try {
            page.drawText(text, { ...options, font });
          } catch (textError) {
            console.warn("Error drawing text:", textError.message);
            // Replace unsupported characters with '?'
            const safeText = text.replace(/[^\x00-\x7F]/g, '?');
            page.drawText(safeText, { ...options, font });
          }
        };
        
        for (const block of slideData.blocks) {
          if (yPosition < margin + 100) {
            page = pdfDoc.addPage([pageWidth, pageHeight]);
            yPosition = pageHeight - margin;
          }
          
          if (block.type === "header") {
            const level = block.data.level || 1;
            const fontSize = level === 1 ? 24 : level === 2 ? 20 : 16;
            const lineHeight = fontSize * 1.2;
            
            const text = block.data.text || "";
            const lines = wrapText(text, contentWidth, fontSize);
            
            lines.forEach((line, index) => {
              safeDrawText(line, {
                x: margin,
                y: yPosition - (index * lineHeight),
                size: fontSize,
                color: rgb(0, 0, 0),
              });
            });
            
            yPosition -= (lines.length * lineHeight) + 10;
          } 
          else if (block.type === "paragraph") {
            const fontSize = 12;
            const lineHeight = fontSize * 1.5;
            
            const text = block.data.text || "";
            const lines = wrapText(text, contentWidth, fontSize);
            
            lines.forEach((line, index) => {
              safeDrawText(line, {
                x: margin,
                y: yPosition - (index * lineHeight),
                size: fontSize,
                color: rgb(0, 0, 0),
              });
            });
            
            yPosition -= (lines.length * lineHeight) + 15;
          } 
          else if (block.type === "list") {
            const fontSize = 12;
            const lineHeight = fontSize * 1.5;
            const textIndent = 25;
            
            let listItems = [];
            if (block.data && block.data.items) {
              listItems = block.data.items.map(item => {
                return typeof item === "string" ? item : (item.content || item.text || "");
              });
            }
            
            const isOrdered = block.data && block.data.style === "ordered";
            
            listItems.forEach((item, index) => {
              let bulletText = isOrdered ? `${index + 1}.` : '.';
              
              safeDrawText(bulletText, {
                x: margin,
                y: yPosition,
                size: fontSize,
                color: rgb(0, 0, 0),
              });
              
              const lines = wrapText(item, contentWidth - textIndent, fontSize);
              
              lines.forEach((line, lineIndex) => {
                safeDrawText(line, {
                  x: margin + textIndent,
                  y: yPosition - (lineIndex * lineHeight),
                  size: fontSize,
                  color: rgb(0, 0, 0),
                });
              });
              
              yPosition -= (lines.length * lineHeight) + 5;
            });
            
            yPosition -= 10;
          }
          else if (block.type === "table") {
            const fontSize = 10;
            const lineHeight = fontSize * 1.5;
            const cellPadding = 5;
            const tableContent = block.data.content || [];
            
            if (tableContent.length > 0) {
              const numColumns = tableContent[0].length;
              const colWidth = contentWidth / numColumns;
              
              tableContent.forEach((row, rowIndex) => {
                let maxLinesInRow = 1;
                
                // First pass: calculate max lines in this row
                row.forEach((cell, colIndex) => {
                  const cellText = String(cell || "");
                  const cellLines = wrapText(cellText, colWidth - (cellPadding * 2), fontSize);
                  maxLinesInRow = Math.max(maxLinesInRow, cellLines.length);
                });
                
                // Check if we need a new page for this row
                if (yPosition - (maxLinesInRow * lineHeight) - 10 < margin) {
                  page = pdfDoc.addPage([pageWidth, pageHeight]);
                  yPosition = pageHeight - margin;
                }
                
                // Draw row background for header row
                if (rowIndex === 0 && block.data.withHeadings) {
                  page.drawRectangle({
                    x: margin,
                    y: yPosition - (maxLinesInRow * lineHeight) - 5,
                    width: contentWidth,
                    height: (maxLinesInRow * lineHeight) + 10,
                    color: rgb(0.9, 0.9, 0.9),
                    borderColor: rgb(0.7, 0.7, 0.7),
                    borderWidth: 1,
                  });
                }
                
                // Second pass: draw cells
                row.forEach((cell, colIndex) => {
                  const cellText = String(cell || "");
                  const cellLines = wrapText(cellText, colWidth - (cellPadding * 2), fontSize);
                  
                  // Draw cell border
                  page.drawRectangle({
                    x: margin + (colIndex * colWidth),
                    y: yPosition - (maxLinesInRow * lineHeight) - 5,
                    width: colWidth,
                    height: (maxLinesInRow * lineHeight) + 10,
                    borderColor: rgb(0.7, 0.7, 0.7),
                    borderWidth: 1,
                    color: rowIndex === 0 && block.data.withHeadings ? 
                      rgb(0.9, 0.9, 0.9) : rgb(1, 1, 1),
                  });
                  
                  // Draw cell text
                  cellLines.forEach((line, lineIndex) => {
                    safeDrawText(line, {
                      x: margin + (colIndex * colWidth) + cellPadding,
                      y: yPosition - (lineIndex * lineHeight),
                      size: fontSize,
                      color: rgb(0, 0, 0),
                      font: font,
                    });
                  });
                });
                
                // Move cursor down after row
                yPosition -= (maxLinesInRow * lineHeight) + 10;
              });
              
              // Add some space after the table
              yPosition -= 10;
            }
          } 
          else if (block.type === "image") {
            try {
              // Get image URL from various possible locations in the block data
              const imageUrl = block.data.url || 
                             (block.data.file && block.data.file.url) || 
                             block.data.src;
              
              if (!imageUrl) {
                console.warn("No image URL found in block data:", block.data);
                safeDrawText("[Image not found]", {
                  x: margin,
                  y: yPosition,
                  size: 10,
                  color: rgb(1, 0, 0),
                });
                yPosition -= 20;
                continue;
              }
              
              // Extract image data
              let imageBytes;
              
              if (imageUrl.startsWith('data:image/')) {
                // Handle data URLs
                const base64Data = imageUrl.split(',')[1];
                imageBytes = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
              } else {
                // Handle regular URLs
                const response = await fetch(imageUrl);
                if (!response.ok) {
                  throw new Error(`Failed to fetch image: ${response.status}`);
                }
                imageBytes = new Uint8Array(await response.arrayBuffer());
              }
              
              // Embed the image
              let image;
              try {
                // Try to embed as PNG first
                image = await pdfDoc.embedPng(imageBytes);
              } catch (pngError) {
                try {
                  // If PNG fails, try JPG
                  image = await pdfDoc.embedJpg(imageBytes);
                } catch (jpgError) {
                  throw new Error("Failed to embed image: Unsupported format");
                }
              }
              
              if (image) {
                // Calculate image dimensions to fit within content width
                const maxImgWidth = contentWidth;
                const maxImgHeight = 300;
                
                let imgWidth, imgHeight;
                
                if (image.width > maxImgWidth || image.height > maxImgHeight) {
                  const widthRatio = maxImgWidth / image.width;
                  const heightRatio = maxImgHeight / image.height;
                  const ratio = Math.min(widthRatio, heightRatio);
                  
                  imgWidth = image.width * ratio;
                  imgHeight = image.height * ratio;
                } else {
                  imgWidth = image.width;
                  imgHeight = image.height;
                }
                
                // Check if we need a new page for the image
                if (yPosition - imgHeight < margin) {
                  page = pdfDoc.addPage([pageWidth, pageHeight]);
                  yPosition = pageHeight - margin;
                }
                
                // Center the image horizontally
                const xPosition = margin + (contentWidth - imgWidth) / 2;
                
                // Draw the image
                page.drawImage(image, {
                  x: xPosition,
                  y: yPosition - imgHeight,
                  width: imgWidth,
                  height: imgHeight,
                });
                
                // Add caption if available
                if (block.data.caption) {
                  const captionY = yPosition - imgHeight - 15;
                  safeDrawText(block.data.caption, {
                    x: margin,
                    y: captionY,
                    size: 10,
                    color: rgb(0.3, 0.3, 0.3),
                    font: font,
                  });
                  yPosition = captionY - 20;
                } else {
                  // Move position down
                  yPosition -= (imgHeight + 20);
                }
              }
            } catch (imgError) {
              console.error("Error embedding image in PDF:", imgError);
              safeDrawText(`[Image error: ${imgError.message}]`, {
                x: margin,
                y: yPosition,
                size: 10,
                color: rgb(1, 0, 0),
              });
              yPosition -= 20;
            }
          }
        }
      }

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: "application/pdf" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = "slides.pdf";
      link.click();

      showNotification("PDF exported successfully!", "success");
    } catch (error) {
      console.error("Error exporting to PDF:", error);
      showNotification("Error exporting to PDF: " + error.message, "error");
    }
  }

  /**
   * Check if the editor is in readonly mode
   */
  get isReadonly() {
    return this.#readonly;
  }

  /**
   * Set readonly mode
   */
  setReadonly(readonly) {
    this.#readonly = Boolean(readonly);
    
    // Update existing editors
    for(const slide of this.slides) {
      void async function(editor) {
        await editor.isReady;
        editor.readOnly.toggle(true);
      }(slide.editor);
    }
    
    // Update grid drag functionality
    console.log(this.grid)
    if (this.grid) {
      this.grid.destroy();
    }
    
    // Show/hide action buttons based on readonly state
    const buttonsToHide = [
      'add-page-btn',
      'upload-file-button',
      'import-pptx-btn'
    ];
    
    buttonsToHide.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.style.display = this.#readonly ? 'none' : '';
      }
    });
    
    // Show/hide delete buttons on slides
    const deleteButtons = document.querySelectorAll('.slide-actions');
    deleteButtons.forEach(btn => {
      btn.style.display = this.#readonly ? 'none' : '';
    });
    
    // Add visual indicator for readonly mode
    if (this.#readonly) {
      const header = document.querySelector('header');
      if (header && !header.querySelector('.readonly-indicator')) {
        const readonlyIndicator = document.createElement('div');
        readonlyIndicator.className = 'readonly-indicator';
        readonlyIndicator.innerHTML = '<sl-badge variant="neutral" pill>Read Only</sl-badge>';
        readonlyIndicator.style.marginLeft = '8px';
        
        const titleDiv = header.querySelector('div:first-child');
        if (titleDiv) {
          titleDiv.appendChild(readonlyIndicator);
        }
      }
    } else {
      // Remove readonly indicator
      const readonlyIndicator = document.querySelector('.readonly-indicator');
      if (readonlyIndicator) {
        readonlyIndicator.remove();
      }
    }
  }

  // ...existing code...
}

import Task from "../models/task.js";
import Meeting from "../models/meeting.js";
import Committee from "../models/committee.js";
import { isValidObjectId } from "mongoose";
import { subDays, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

// Get all tasks with advanced filtering
export const getTasks = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = "",
      sortBy = "createdAt",
      sortOrder = "desc",
      status,
      priority,
      committeeId,
      meetingId,
      dateRange,
      assignedTo,
    } = req.query;

    // Build filter object
    const filter = { meeting: { $ne: null, $exists: true } };
    
    // Add search functionality across multiple fields
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    // Status filter
    if (status) {
      filter.status = status;
    }

    // Priority filter
    if (priority) {
      filter.priority = priority;
    }

    // Committee filter
    if (committeeId && isValidObjectId(committeeId)) {
      // Find meetings associated with this committee
      const meetings = await Meeting.find({ committee: committeeId }, '_id');
      const meetingIds = meetings.map(m => m._id);
      filter.meeting.$in = meetingIds;
    }

    // Meeting filter
    if (meetingId && isValidObjectId(meetingId)) {
      filter.meeting = meetingId;
    }

    // Assigned To filter
    if (assignedTo && isValidObjectId(assignedTo)) {
      filter.assignedTo = assignedTo;
    }

    // Date range filter
    if (dateRange) {
      const today = new Date();
      
      switch (dateRange) {
        case 'today':
          filter.deadline = { 
            $gte: startOfDay(today),
            $lte: endOfDay(today)
          };
          break;
        case 'week':
          filter.deadline = { 
            $gte: startOfDay(today),
            $lte: endOfWeek(today)
          };
          break;
        case 'overdue':
          filter.deadline = { $lt: startOfDay(today) };
          filter.status = { $ne: 'completed' }; // Only include non-completed tasks
          break;
        case 'no_deadline':
          filter.deadline = { $exists: false };
          break;
      }
    }

    // Create sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (Number(page) - 1) * Number(limit);
    console.log(filter);
    
    const tasks = await Task.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(Number(limit))
      .populate('assignedTo', 'name')
      .populate({
        path: 'meeting',
        select: 'title committee',
        populate: {
          path: 'committee',
          select: 'name'
        }
      });
      console.log(tasks)
    // Get total count for pagination
    const total = await Task.countDocuments(filter);

    // For each task, fetch additional data to enrich the response
    const enhancedTasks = await Promise.all(tasks.map(async (task) => {
      const meeting = await Meeting.findById(task.meeting._id, 'title committee');
      let committee = null;
      
      if (meeting && meeting.committee) {
        committee = await Committee.findById(meeting.committee, 'name');
      }
      
      return {
        ...task.toObject(),
        meetingTitle: meeting ? meeting.title : 'Unknown Meeting',
        committeeId: meeting && meeting.committee ? meeting.committee : null,
        committeeName: committee ? committee.name : 'Unknown Committee'
      };
    }));

    res.status(200).json({
      data: enhancedTasks,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Endpoint for getting all meetings with advanced filtering
export const getMeetings = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = "",
      sortBy = "startDate",
      sortOrder = "desc",
      status,
      committeeId,
      dateRange,
    } = req.query;

    // Build filter object
    const filter = {};
    
    // Add search functionality across multiple fields
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    // Status filter
    if (status) {
      filter.status = status;
    }

    // Committee filter
    if (committeeId && isValidObjectId(committeeId)) {
      filter.committee = committeeId;
    }

    // Date range filter
    if (dateRange) {
      const today = new Date();
      
      switch (dateRange) {
        case 'today':
          filter.startDate = { 
            $gte: startOfDay(today),
            $lte: endOfDay(today)
          };
          break;
        case 'tomorrow':
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          filter.startDate = { 
            $gte: startOfDay(tomorrow),
            $lte: endOfDay(tomorrow)
          };
          break;
        case 'week':
          filter.startDate = { 
            $gte: startOfDay(today),
            $lte: endOfWeek(today)
          };
          break;
        case 'month':
          filter.startDate = { 
            $gte: startOfDay(today),
            $lte: endOfMonth(today)
          };
          break;
        case 'past':
          filter.endDate = { $lt: new Date() };
          break;
        case 'upcoming':
          filter.startDate = { $gte: new Date() };
          break;
      }
    }

    // Create sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (Number(page) - 1) * Number(limit);

    const meetings = await Meeting.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(Number(limit))
      .populate('committee', 'name')
      .populate('attendees', 'name');

    // Get total count for pagination
    const total = await Meeting.countDocuments(filter);

    // Enhance meetings with task count
    const enhancedMeetings = await Promise.all(meetings.map(async (meeting) => {
      const taskCount = await Task.countDocuments({ meetingId: meeting._id });
      return {
        ...meeting.toObject(),
        taskCount
      };
    }));

    res.status(200).json({
      data: enhancedMeetings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

import { SearchAndPagination } from "@/components/SearchAndPagination";
import apiClient from "@/config/axios"
import { usePagination } from "@/hooks/usePagination";
import { Button, Container, Group, Paper, Title, Text, Stack, Loader, Grid, Card, Modal, TextInput, Textarea, Select, ActionIcon, Alert, List } from "@mantine/core";
import { useForm } from '@mantine/form';
import { DateInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconCheck, IconEdit, IconTrash } from "@tabler/icons-react";
import { useCallback, useState } from "react"

interface Scheme {
  _id:string;
  name: string;
  description: string;
  startDate: string;
  aimAndObjective: string;
  eligibilityCriteria: string;
  documentLink: string;
  benefits: string;
  sourceOfFund: "Central" | "State" | "Sponsored" | "50:50";
}

export default function Schemes() {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [currentScheme, setCurrentScheme] = useState<Scheme | null>(null);
    const [deletingScheme, setDeletingScheme] = useState<Scheme | null>(null);
    const [isEditMode, setIsEditMode] = useState(false);
    
    const form = useForm({
        initialValues: {
            name: '',
            description: '',
            startDate: new Date(),
            aimAndObjective: '',
            eligibilityCriteria: '',
            documentLink: '',
            benefits: '',
            sourceOfFund: 'Central' as "Central" | "State" | "Sponsored" | "50:50",
        },
        validate: {
            name: (value) => value.trim().length === 0 ? 'Name is required' : null,
            description: (value) => value.trim().length === 0 ? 'Description is required' : null,
            startDate: (value) => !value ? 'Start date is required' : null,
        }
    });
    
    const handleEditScheme = (scheme: Scheme) => {
        setCurrentScheme(scheme);
        form.setValues({
            name: scheme.name,
            description: scheme.description,
            startDate: new Date(scheme.startDate),
            aimAndObjective: scheme.aimAndObjective,
            eligibilityCriteria: scheme.eligibilityCriteria,
            documentLink: scheme.documentLink,
            benefits: scheme.benefits,
            sourceOfFund: scheme.sourceOfFund,
        });
        setIsEditMode(true);
        setIsModalOpen(true);
    };
    
    const handleDeleteScheme = (scheme: Scheme) => {
        setDeletingScheme(scheme);
        setIsDeleteModalOpen(true);
    };
    
    const confirmDeleteScheme = async () => {
        if (!deletingScheme) return;
        
        try {
            // Uncomment when API is ready
            // await apiClient.delete(`/api/schemes/${deletingScheme._id}`);
            console.log('Deleted scheme:', deletingScheme._id);
            
            notifications.show({
                title: 'Success',
                message: 'Scheme deleted successfully',
                color: 'green',
                icon: <IconCheck size="1rem" />
            });
            
            setIsDeleteModalOpen(false);
            setDeletingScheme(null);
            refresh();
        } catch (error) {
            console.error('Error deleting scheme:', error);
            notifications.show({
                title: 'Error',
                message: 'Failed to delete scheme',
                color: 'red'
            });
        }
    };
    
    const handleSubmit = async (values: typeof form.values) => {
        try {
            if (isEditMode && currentScheme) {
                // Uncomment when API is ready
                // await apiClient.put(`/api/schemes/${currentScheme._id}`, values);
                console.log('Updated values:', values);
                
                notifications.show({
                    title: 'Success',
                    message: 'Scheme updated successfully',
                    color: 'green',
                    icon: <IconCheck size="1rem" />
                });
            } else {
                // Uncomment when API is ready
                await apiClient.post('/api/schemes', values);
                console.log('Submitted values:', values);
                
                notifications.show({
                    title: 'Success',
                    message: 'Scheme created successfully',
                    color: 'green',
                    icon: <IconCheck size="1rem" />
                });
            }
            
            setIsModalOpen(false);
            setIsEditMode(false);
            setCurrentScheme(null);
            form.reset();
            refresh();
        } catch (error) {
            console.error('Error saving scheme:', error);
            notifications.show({
                title: 'Error',
                message: 'Failed to save scheme',
                color: 'red'
            });
        }
    };
    
    const fetchSchemes = useCallback(async ():Promise<any> => {
        const response = await apiClient.get("/api/schemes");
        console.log(response.data);
        return response.data;
        // Return in the format expected by usePagination
        // return {
        //     data: [
        //         {
        //             _id:"1234",
        //             name: "Swasthya Suraksha Yojana",
        //             description: "A health scheme aimed at providing free primary healthcare in rural areas.",
        //             startDate: "2023-01-15",
        //             aimAndObjective: "To ensure accessible and affordable healthcare for all citizens in underserved regions.",
        //             eligibilityCriteria: "All residents in rural areas with an annual income below ₹2.5 lakh.",
        //             documentLink: "https://example.com/swasthya-doc.pdf",
        //             benefits: "Free doctor consultations, medicines, and annual health checkups.",
        //             sourceOfFund: "Central"
        //         },
        //         {
        //             _id:"12ds34",
        //             name: "SkillBoost Initiative",
        //             description: "A nationwide skill development program to improve employability among youth.",
        //             startDate: "2022-06-01",
        //             aimAndObjective: "To train 1 million youth in technical and digital skills over 5 years.",
        //             eligibilityCriteria: "Indian citizens aged 18–35 with minimum Class 10 education.",
        //             documentLink: "https://example.com/skillboost-details.pdf",
        //             benefits: "Free certified training, stipend during training, placement assistance.",
        //             sourceOfFund: "50:50"
        //         },
        //         {
        //             _id:"123ads4",
        //             name: "Green Village Mission",
        //             description: "An eco-development scheme promoting renewable energy in villages.",
        //             startDate: "2024-03-10",
        //             aimAndObjective: "To transition 5,000 villages to sustainable energy sources.",
        //             eligibilityCriteria: "Gram Panchayats located in Tier-2 and Tier-3 districts.",
        //             documentLink: "https://example.com/green-village.pdf",
        //             benefits: "Subsidy on solar panels, compost units, and green tech infrastructure.",
        //             sourceOfFund: "Sponsored"
        //         },
        //         {
        //             _id:"12zxcfv4",
        //             name: "Nari Udyam Yojana",
        //             description: "A women empowerment scheme for supporting micro-enterprises.",
        //             startDate: "2021-09-25",
        //             aimAndObjective: "To increase the participation of women in entrepreneurship.",
        //             eligibilityCriteria: "Women aged 21–50 from economically weaker sections.",
        //             documentLink: "https://example.com/nari-udyam.pdf",
        //             benefits: "Startup grant up to ₹1 lakh, free mentorship, and training.",
        //             sourceOfFund: "State"
        //         },
        //         {
        //             _id:"123xdf4",
        //             name: "Smart School Upgrade Scheme",
        //             description: "An infrastructure and technology upgrade program for public schools.",
        //             startDate: "2025-01-01",
        //             aimAndObjective: "To convert 10,000 government schools into smart schools.",
        //             eligibilityCriteria: "Government schools with over 200 students and active PTA.",
        //             documentLink: "https://example.com/smart-school.pdf",
        //             benefits: "Smartboards, computer labs, digital curriculum access.",
        //             sourceOfFund: "Central"
        //         }
        //     ],
        //     pagination: {
        //         page: 1,
        //         limit: 10,
        //         total: 5,
        //         pages: 1
        //     }
        // };
    }, [])

    const sortOptions = [
        { value: 'name', label: 'Name' },
        { value: 'designation', label: 'Designation' },
        { value: 'email', label: 'Email' },
        { value: 'createdAt', label: 'Created Date' },
    ];

    const {
        data: schemes,
        pagination,
        loading,
        search,
        sortBy,
        sortOrder,
        setPage,
        setLimit,
        setSearch,
        setSorting,
        refresh,
    } = usePagination<Scheme>(fetchSchemes, {
        sortBy: 'name',
        sortOrder: 'asc'
    });
    console.log("schemes", schemes)

    return <Container>
        <Group justify="space-between" mb={20}>
            <div>
                <Title order={2}>Schemes</Title>
                <Text c="dimmed">Manage committee members and contacts</Text>
            </div>
            <Button
            leftSection={<IconPlus size="1rem" />}
            onClick={() => setIsModalOpen(true)}
            >
                Add Scheme
            </Button>
      </Group>
        <Paper p="md" withBorder mb="lg">
          <SearchAndPagination
            search={search}
            onSearchChange={setSearch}
            pagination={pagination}
            onPageChange={setPage}
            onLimitChange={setLimit}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSortChange={setSorting}
            sortOptions={sortOptions}
            loading={loading}
           />
        </Paper>
        {loading ? (
            <Paper p="xl" withBorder>
                <Stack align="center">
                <Loader />
                <Text>Loading schemes...</Text>
                </Stack>
            </Paper>
            ) : schemes.length === 0 ? (
            <Paper p="xl" withBorder>
            <Stack align="center">
                <Text c="dimmed">No schemes found</Text>
            </Stack>
            </Paper>) : (
            <Grid>
                {schemes?.map((scheme) => (
                    <Grid.Col key={scheme._id} span={{ base: 12, md: 6, lg: 4 }}>
                        <Card withBorder shadow="sm">
                            <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                                <Stack gap="1">
                                    <Text fw={500} size="lg">
                                    {scheme.name}
                                    </Text>
                                    <Text size="sm" c="dimmed">
                                    {scheme.description}
                                    </Text>
                                </Stack>
                                <Group gap="xs" wrap="nowrap">
                                    <ActionIcon
                                        variant="light"
                                        color="blue"
                                        onClick={() => handleEditScheme(scheme)}
                                    >
                                        <IconEdit size={16} />
                                    </ActionIcon>
                                    <ActionIcon
                                        variant="light"
                                        color="red"
                                        onClick={() => handleDeleteScheme(scheme)}
                                    >
                                        <IconTrash size={16} />
                                    </ActionIcon>
                                </Group>
                            </Group>
                            
                            <Stack gap="xs" mt="md">
                                <Group gap="xs">
                                    <Text size="sm" fw={500}>Source:</Text>
                                    <Text size="sm">{scheme.sourceOfFund}</Text>
                                </Group>
                                <Group gap="xs">
                                    <Text size="sm" fw={500}>Started:</Text>
                                    <Text size="sm">{new Date(scheme.startDate).toLocaleDateString()}</Text>
                                </Group>
                                <Text size="sm" lineClamp={2} title={scheme.benefits}>
                                    <Text span fw={500}>Benefits:</Text> {scheme.benefits}
                                </Text>
                            </Stack>
                            
                            <Group mt="md" gap="xs" justify="space-between">
                                <Button variant="light" size="xs" component="a" href={scheme.documentLink} target="_blank">
                                    View Document
                                </Button>
                                <Button variant="outline" size="xs">
                                    Details
                                </Button>
                            </Group>
                        </Card>
                    </Grid.Col>
                ))}
            </Grid>
            )
        }
        <Modal
            opened={isModalOpen}
            onClose={() => {
                setIsModalOpen(false);
                setIsEditMode(false);
                setCurrentScheme(null);
                form.reset();
            }}
            title={isEditMode ? "Edit Scheme" : "Add New Scheme"}
            size="lg"
        >
            <form onSubmit={form.onSubmit(handleSubmit)}>
                <Stack>
                    <TextInput
                        label="Scheme Name"
                        placeholder="Enter scheme name"
                        required
                        {...form.getInputProps('name')}
                    />
                    
                    <Textarea
                        label="Description"
                        placeholder="Brief description of the scheme"
                        required
                        {...form.getInputProps('description')}
                    />
                    
                    <DateInput
                        label="Start Date"
                        placeholder="Select start date"
                        required
                        {...form.getInputProps('startDate')}
                    />
                    
                    <Textarea
                        label="Aim and Objective"
                        placeholder="Main goals of the scheme"
                        {...form.getInputProps('aimAndObjective')}
                    />
                    
                    <Textarea
                        label="Eligibility Criteria"
                        placeholder="Who can apply for this scheme"
                        {...form.getInputProps('eligibilityCriteria')}
                    />
                    
                    <TextInput
                        label="Document Link"
                        placeholder="URL to scheme documentation"
                        {...form.getInputProps('documentLink')}
                    />
                    
                    <Textarea
                        label="Benefits"
                        placeholder="Benefits provided by the scheme"
                        {...form.getInputProps('benefits')}
                    />
                    
                    <Select
                        label="Source of Fund"
                        placeholder="Select funding source"
                        required
                        data={[
                            { value: 'Central', label: 'Central Government' },
                            { value: 'State', label: 'State Government' },
                            { value: 'Sponsored', label: 'Sponsored' },
                            { value: '50:50', label: '50:50 Central-State' }
                        ]}
                        {...form.getInputProps('sourceOfFund')}
                    />
                    
                    <Group justify="flex-end" mt="md">
                        <Button variant="light" onClick={() => {
                            setIsModalOpen(false);
                            setIsEditMode(false);
                            setCurrentScheme(null);
                            form.reset();
                        }}>Cancel</Button>
                        <Button type="submit">{isEditMode ? "Update" : "Create"} Scheme</Button>
                    </Group>
                </Stack>
            </form>
        </Modal>
        {/* Delete Confirmation Modal */}
        <Modal
            opened={isDeleteModalOpen}
            onClose={() => {
                setIsDeleteModalOpen(false);
                setDeletingScheme(null);
            }}
            title="Delete Scheme"
            size="md"
        >
            <Stack>
                <Alert color="red" title="Warning: This action cannot be undone">
                    You are about to permanently delete the scheme <strong>"{deletingScheme?.name}"</strong>.
                </Alert>

                <Text size="sm" fw={500} mb="xs">This will permanently delete:</Text>
                <List size="sm" spacing="xs">
                    <List.Item>The scheme and all its information</List.Item>
                    <List.Item>Any associated beneficiaries or records</List.Item>
                    <List.Item>Historical data related to this scheme</List.Item>
                </List>

                <Group justify="flex-end" mt="lg">
                    <Button
                        variant="light"
                        onClick={() => {
                            setIsDeleteModalOpen(false);
                            setDeletingScheme(null);
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        color="red"
                        onClick={confirmDeleteScheme}
                    >
                        Delete Scheme
                    </Button>
                </Group>
            </Stack>
        </Modal>
    </Container>
}

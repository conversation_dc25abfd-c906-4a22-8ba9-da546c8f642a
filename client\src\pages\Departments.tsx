import { useState, useCallback } from 'react';
import {
  Container,
  Title,
  Button,
  Group,
  Paper,
  TextInput,
  Textarea,
  Modal,
  Stack,
  Card,
  ActionIcon,
  Grid,
  Loader,
  Alert,
  Text
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconEdit, IconTrash, IconFileCode } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Department } from '../types';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';
import ErrorBoundary from '../components/ErrorBoundary';
import { useNavigate } from 'react-router-dom';

// Department Form Component
const DepartmentForm = ({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  title
}: {
  formData: any;
  setFormData: (data: any) => void;
  onSubmit: () => void;
  onCancel: () => void;
  title: string;
}) => (
  <Stack>
    <TextInput
      label="Name"
      placeholder="Enter department name"
      value={formData.name}
      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      required
    />

    <TextInput
      label="Code"
      placeholder="Enter department code"
      value={formData.code}
      onChange={(e) => setFormData({ ...formData, code: e.target.value })}
    />

    <Textarea
      label="Description"
      placeholder="Enter department description"
      value={formData.description}
      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
      minRows={2}
      resize="vertical"
    />

    <Group justify="flex-end" mt="md">
      <Button variant="light" onClick={onCancel}>
        Cancel
      </Button>
      <Button onClick={onSubmit}>{title}</Button>
    </Group>
  </Stack>
);

function DepartmentsPage() {
  const navigate = useNavigate();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<Department | null>(null);
  const [deletingDepartment, setDeletingDepartment] = useState<Department | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
  });
    // Fetch function for pagination
  const fetchDepartments = useCallback(async (params: any) => {
    try {
      const response = await apiClient.get('/api/departments', { 
        params: { ...params, populate: 'createdBy' }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching departments:', error);
      return { data: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } };
    }
  }, []);

  const {
    data: departments,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Department>(fetchDepartments, {
    sortBy: 'name',
    sortOrder: 'asc'
  });

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'code', label: 'Code' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  const validateDepartment = () => {
    if (!formData.name) {
      notifications.show({
        title: 'Validation Error',
        message: 'Department name is required',
        color: 'red',
      });
      return false;
    }
    return true;
  };

  const handleCreateDepartment = async () => {
    if (!validateDepartment()) return;

    try {
      await apiClient.post('/api/departments', formData);
      notifications.show({
        title: 'Success',
        message: 'Department created successfully',
        color: 'green',
      });
      setIsCreateModalOpen(false);
      refresh();
      resetForm();
    } catch (error: any) {
      console.error('Failed to create department:', error);
      notifications.show({
        title: 'Error',
        message: error.response?.data?.message || 'Failed to create department',
        color: 'red',
      });
    }
  };

  const handleEditDepartment = async () => {
    if (!validateDepartment() || !currentDepartment) return;

    try {
      await apiClient.put(`/api/departments/${currentDepartment._id}`, formData);
      notifications.show({
        title: 'Success',
        message: 'Department updated successfully',
        color: 'green',
      });
      setIsEditModalOpen(false);
      refresh();
      resetForm();
    } catch (error: any) {
      console.error('Failed to update department:', error);
      notifications.show({
        title: 'Error',
        message: error.response?.data?.message || 'Failed to update department',
        color: 'red',
      });
    }
  };

  const handleDeleteDepartment = async () => {
    if (!deletingDepartment) return;
    setDeleteError(null);

    try {
      await apiClient.delete(`/api/departments/${deletingDepartment._id}`);
      notifications.show({
        title: 'Success',
        message: 'Department deleted successfully',
        color: 'green',
      });
      setIsDeleteModalOpen(false);
      refresh();
    } catch (error: any) {
      console.error('Failed to delete department:', error);
      if (error.response?.data?.message) {
        setDeleteError(error.response.data.message);
      } else {
        notifications.show({
          title: 'Error',
          message: 'Failed to delete department',
          color: 'red',
        });
        setIsDeleteModalOpen(false);
      }
    }
  };

  const openEditModal = (department: Department) => {
    setCurrentDepartment(department);
    setFormData({
      name: department.name,
      code: department.code || '',
      description: department.description || '',
    });
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (department: Department) => {
    setDeletingDepartment(department);
    setDeleteError(null);
    setIsDeleteModalOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
    });
    setCurrentDepartment(null);
    setDeletingDepartment(null);
  };

  return (
    <Container size="lg" py="xl">
      <Group justify="space-between" mb={20}>
        <Title order={2}>Departments</Title>
        <Button
          leftSection={<IconPlus size="1rem" />}
          onClick={() => {
            resetForm();
            setIsCreateModalOpen(true);
          }}
        >
          Add Department
        </Button>
      </Group>

      <Paper p="md" withBorder mb="md">        <SearchAndPagination
          search={search}
          pagination={pagination}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSearchChange={setSearch}
          onPageChange={setPage}
          onLimitChange={setLimit}
          onSortChange={setSorting}
          sortOptions={sortOptions}
        />
      </Paper>

      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
          <Loader />
        </div>
      ) : departments && departments.length > 0 ? (
        <Grid>
          {departments.map((department) => (
            <Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={department._id}>
              <Card 
                shadow="sm"
                padding="lg" 
                withBorder
                onClick={() => navigate(`${department._id}`)}
                style={{ cursor: 'pointer' }}
              >
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Text fw={500} size="lg">
                    {department.name}
                  </Text>
                  <Group gap="xs" wrap="nowrap">
                    <ActionIcon 
                      variant="light" 
                      color="blue"
                      onClick={(e) => { e.stopPropagation(); openEditModal(department) }}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon 
                      variant="light" 
                      color="red"
                      onClick={(e) => { e.stopPropagation(); openDeleteModal(department) }}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>

                {department.description && (
                  <Text size="sm" c="dimmed" mb="md">
                    {department.description}
                  </Text>
                )}

                <Stack gap="xs">
                  {department.code && (
                    <Group gap="xs" align="flex-start" wrap="nowrap">
                      <IconFileCode size={16} style={{ flexShrink: 0, marginTop: 2 }} />
                      <Text size="sm">
                        <Text component="span" fw={500} c="dimmed">Code: </Text>
                        <Text component="span">{department.code}</Text>
                      </Text>
                    </Group>
                  )}
                  
                  <Text size="xs" c="dimmed">
                    Created by {typeof department.createdBy === 'object' && department.createdBy?.name ? department.createdBy.name : 'Unknown'} on {new Date(department.createdAt || '').toLocaleDateString()}
                  </Text>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      ) : (
        <Alert title="No departments found" color="gray">
          No departments are available. Start by adding a new department.
        </Alert>
      )}

      {/* Create Modal */}
      <Modal
        opened={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Department"
        size="md"
      >
        <DepartmentForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleCreateDepartment}
          onCancel={() => setIsCreateModalOpen(false)}
          title="Create"
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Department"
        size="md"
      >
        <DepartmentForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleEditDepartment}
          onCancel={() => setIsEditModalOpen(false)}
          title="Update"
        />
      </Modal>

      {/* Delete Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Department"
        size="md"
      >
        <Stack>
          {deleteError ? (
            <Alert color="red" title="Cannot Delete Department">
              {deleteError}
            </Alert>
          ) : (
            <Text>
              Are you sure you want to delete the department: <strong>{deletingDepartment?.name}</strong>?
              This action cannot be undone.
            </Text>
          )}
          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button color="red" onClick={handleDeleteDepartment} disabled={!!deleteError}>
              Delete
            </Button>
          </Group>
        </Stack>
      </Modal>    </Container>
  );
}

// Wrap the component with ErrorBoundary
export default function DepartmentsPageWithErrorBoundary() {
  return (
    <ErrorBoundary>
      <DepartmentsPage />
    </ErrorBoundary>
  );
}

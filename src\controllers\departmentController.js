import Department from "../models/department.js";
import mongoose from "mongoose";

/**
 * Get all departments with pagination
 * @route GET /api/departments
 * @access Private
 */
export const getAllDepartments = async (req, res) => {
  try {    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const searchQuery = req.query.search || "";
    const sortBy = req.query.sortBy || "name";
    const sortOrder = req.query.sortOrder || "asc";

    let query = {};
    if (searchQuery) {
      query = {
        $or: [
          { name: { $regex: searchQuery, $options: "i" } },
          { description: { $regex: searchQuery, $options: "i" } },
          { code: { $regex: searchQuery, $options: "i" } },
        ],
      };
    }

    // Create sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    const departments = await Department.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await Department.countDocuments(query);    res.json({
      data: departments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      }
    });
  } catch (error) {
    console.error("Error getting departments:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Get a department by ID
 * @route GET /api/departments/:id
 * @access Private
 */
export const getDepartmentById = async (req, res) => {
  try {
    const department = await Department.findById(req.params.id);
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }
    res.json(department);
  } catch (error) {
    console.error("Error getting department:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Create a new department
 * @route POST /api/departments
 * @access Private
 */
export const createDepartment = async (req, res) => {
  try {
    const { name, description, code } = req.body;

    // Check if department with same name or code already exists
    const existingDepartment = await Department.findOne({
      $or: [{ name }, { code }],
    });
    
    if (existingDepartment) {
      if (existingDepartment.name === name) {
        return res.status(400).json({ message: "Department with this name already exists" });
      }
      if (code && existingDepartment.code === code) {
        return res.status(400).json({ message: "Department with this code already exists" });
      }
    }

    const newDepartment = new Department({
      name,
      description,
      code,
      createdBy: req.user.id,
      updatedBy: req.user.id,
    });

    const savedDepartment = await newDepartment.save();
    res.status(201).json(savedDepartment);
  } catch (error) {
    console.error("Error creating department:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Update a department
 * @route PUT /api/departments/:id
 * @access Private
 */
export const updateDepartment = async (req, res) => {
  try {
    const { name, description, code } = req.body;
    
    // Check if department exists
    const department = await Department.findById(req.params.id);
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }

    // Check for duplicate name or code (excluding current department)
    if (name !== department.name || code !== department.code) {
      const existingDepartment = await Department.findOne({
        _id: { $ne: req.params.id },
        $or: [
          { name },
          { code }
        ]
      });

      if (existingDepartment) {
        if (existingDepartment.name === name) {
          return res.status(400).json({ message: "Department with this name already exists" });
        }
        if (code && existingDepartment.code === code) {
          return res.status(400).json({ message: "Department with this code already exists" });
        }
      }
    }

    // Update department
    department.name = name;
    department.description = description;
    department.code = code;
    department.updatedBy = req.user.id;

    const updatedDepartment = await department.save();
    res.json(updatedDepartment);
  } catch (error) {
    console.error("Error updating department:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Delete a department
 * @route DELETE /api/departments/:id
 * @access Private
 */
export const deleteDepartment = async (req, res) => {
  try {
    const department = await Department.findById(req.params.id);
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }

    // Check if department is assigned to any directory entries
    const Directory = mongoose.model('Directory');
    const directoryWithDepartment = await Directory.findOne({ department: req.params.id });
    
    if (directoryWithDepartment) {
      return res.status(400).json({ 
        message: "Cannot delete this department because it is assigned to one or more directory entries",
        directoryEntry: directoryWithDepartment.name
      });
    }

    await department.deleteOne();
    res.json({ message: "Department removed" });
  } catch (error) {
    console.error("Error deleting department:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

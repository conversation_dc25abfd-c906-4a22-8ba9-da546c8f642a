import dayjs from "dayjs";
import dayjsQuarter from "dayjs/plugin/quarterOfYear.js";
import dayjsIsBetween from "dayjs/plugin/isBetween.js";

dayjs.extend(dayjsQuarter);
dayjs.extend(dayjsIsBetween);

import Committee from "../models/committee.js";
import Notification from "../models/notification.js";
import Meeting from "../models/meeting.js";

export const handleNotificationForSingleCommittee = async (committeeId, now = dayjs()) => {
    try {
        const committee = await Committee.findOne({ _id: committeeId });
        if (!committee) {
            console.log(`Skipping committee with id ${committeeId} not found`);
            return;
        }

        let startPeriod = now;
        let endPeriod   = now;
        let showPeriod  = now;

        switch (committee.meetingFrequency) {
            case "weekly": {
                startPeriod = now.startOf("week");
                endPeriod   = now.endOf("week");
                showPeriod  = endPeriod.subtract(2, "day").startOf("day");
                break;
            }

            case "biweekly": {
                const currentWeek = Math.min(4, now.diff(now.startOf("month"), "week") + 1);
                if (currentWeek % 2 === 0) {
                    startPeriod = now.subtract(1, "week").startOf("week");
                    endPeriod   = now.endOf("week");
                } else {
                    startPeriod = now.startOf("week");
                    endPeriod   = now.add(1, "week").endOf("week");
                }
                showPeriod = endPeriod.subtract(5, "day").startOf("day");
                break;
            }

            case "monthly": {
                startPeriod = now.startOf("month");
                endPeriod   = now.endOf("month");
                showPeriod  = endPeriod.subtract(10, "day").startOf("day");
                break;
            }

            case "quarterly": {
                startPeriod = now.startOf("quarter");
                endPeriod   = now.endOf("quarter");
                showPeriod  = endPeriod.subtract(15, "day").startOf("day");
                break;
            }

            case "halfyearly": {
                const halfStartMonth = now.month() < 6 ? 0 : 6;
                startPeriod = dayjs(`${now.year()}-${halfStartMonth + 1}-01`);
                endPeriod   = startPeriod.add(5, "month").endOf("month");
                showPeriod  = endPeriod.subtract(90, "day").startOf("day");
                break;
            }

            case "yearly": {
                startPeriod = now.startOf("year");
                endPeriod   = now.endOf("year");
                showPeriod  = endPeriod.subtract(180, "day").startOf("day");
                break;
            }

            default:
                return;
        }

        const committeeCreation = dayjs(committee.createdAt);

        if (startPeriod.isBefore(committeeCreation)) {
            startPeriod = committeeCreation;
        }

        const notification = await Notification.findOne({
            committeeId,
            periodStart: { $gte: startPeriod.toDate() },
            periodEnd:   { $lte: endPeriod.toDate() },
        });

        const meetings = await Meeting.find({
            committee:  committeeId,
            startDate:  { $gte: startPeriod.toDate(), $lte: endPeriod.toDate() }
        });

        if (meetings.length && notification) {
            await Notification.deleteMany({
                committeeId,
                periodStart: { $gte: startPeriod.toDate() },
                periodEnd:   { $lte: endPeriod.toDate() },
            }, {
                periodEnd: { $ltr: dayjs().toDate(), }
            });
            return;
        }

        if (!meetings.length && !notification) {
            await new Notification({
                committeeId,
                periodStart: startPeriod.toDate(),
                periodEnd:   endPeriod.toDate(),
                showPeriod:  showPeriod.toDate()
            }).save();
        }
    } catch (error) {
        console.error(error);
    }
};

export const generateNotificationData = async (date) => {
    try {
        const activeCommittees = await Committee.find({}, { _id: 1 }).lean();
        for (const { _id } of activeCommittees) {
            await handleNotificationForSingleCommittee(_id, date);
        }
    } catch (err) {
        console.error(err);
    }
};

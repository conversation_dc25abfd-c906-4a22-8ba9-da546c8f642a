import mongoose from "mongoose";

// File schema for meeting documents including attendance sheets
const fileSchema = new mongoose.Schema({
  filename: { type: String, required: true },
  originalname: { type: String, required: true },
  path: { type: String, required: true },
  mimetype: { type: String, required: true },
  size: { type: Number, required: true },
  uploadedAt: { type: Date, default: Date.now },
  isAttendanceSheet: { type: Boolean, default: false }
});

// Schema for tracking member invitations
const invitationSchema = new mongoose.Schema({
  member: { type: mongoose.Schema.Types.ObjectId, ref: "Directory", required: true },
  status: { type: String, enum: ['invited', 'confirmed', 'declined'], default: 'invited' },
  invitedAt: { type: Date, default: Date.now },
  responseAt: { type: Date }
});

const meetingSchema = new mongoose.Schema({
  title: { type: String, required: true },
  committee: { type: mongoose.Schema.Types.ObjectId, ref: "Committee", required: true },
  agenda: { type: String },
  startDate: { type: Date, required: true },
  tasks: [{ type: mongoose.Schema.Types.ObjectId, ref: "Task" }],
  attendees: [{ type: mongoose.Schema.Types.ObjectId, ref: "Directory" }],
  invitations: [invitationSchema], // Added for tracking invitations
  documents: [fileSchema], // Added for storing attendance sheets and other docs
}, { timestamps: true });

let MeetingModel = mongoose.models.Meeting;
if (MeetingModel === undefined) {
  MeetingModel = mongoose.model("Meeting", meetingSchema);
}

export default MeetingModel;

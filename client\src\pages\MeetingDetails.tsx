import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { apiClient } from '../config/axios';
import { Meeting, Task, AgendaItem } from '../types';
import {
  Container,
  Title,
  Text,
  Group,
  Button,
  Modal,
  TextInput,
  Textarea,
  Select,
  MultiSelect,
  ActionIcon,
  Menu,
  Paper,
  Stack,
  Badge,
  Progress,
  Tabs,
  SegmentedControl,
  SimpleGrid,
  Box,
  ScrollArea,
  Loader,
  Divider,
  Collapse,
  UnstyledButton,
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { showNotification } from '@mantine/notifications';
import { 
  IconPlus, IconEdit, IconTrash, IconDotsVertical, IconCheck, IconUsers, 
  IconUpload, IconPaperclip, IconFileZip, IconPresentation, IconX, 
  IconCalendarStats, IconChevronDown, IconChevronUp, IconFilter, 
  IconDownload, IconFileCheck, IconMail
} from '@tabler/icons-react';
import { pdf   } from '@react-pdf/renderer';
import InviteMembersModal from '../components/InviteMembersModal';
import TaskImageUploader from '../components/TaskImageUploader';
import AgendaImageUploader from '../components/AgendaImageUploader';
import TaskPdfDocument from '@/components/TaskPdfDocument';

interface TaskFormData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  assignedTo: string[];
  status: 'pending' | 'completed';
  deadline: Date | null;
}

export default function MeetingDetails() {
  const { committeeId, meetingId } = useParams<{ committeeId: string; meetingId: string }>();
  const navigate = useNavigate();
  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [agendaItems, setAgendaItems] = useState<AgendaItem[]>([]);
  const [loadingAgenda, setLoadingAgenda] = useState(true);  
  const [isCreateTaskModalOpen, setIsCreateTaskModalOpen] = useState(false);
  const [isEditTaskModalOpen, setIsEditTaskModalOpen] = useState(false);
  const [isAgendaItemModalOpen, setIsAgendaItemModalOpen] = useState(false);
  const [isUploaderOpen, setIsUploaderOpen] = useState(false);
  const [isAgendaUploaderOpen, setIsAgendaUploaderOpen] = useState(false);
  const [isAnalysisModalOpen, setIsAnalysisModalOpen] = useState(false);
  const [isAttendanceSheetUploaderOpen, setIsAttendanceSheetUploaderOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [attendanceProcessing, setAttendanceProcessing] = useState(false);
  const [isAnalysisLoading, setIsAnalysisLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [isFiltersCollapsed, setIsFiltersCollapsed] = useState(false);
  const pollingStatusRef = useRef<Record<string, boolean>>({});
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [editingAgendaItem, setEditingAgendaItem] = useState<AgendaItem | null>(null);
  const [analyzingAgendaItem, setAnalyzingAgendaItem] = useState<AgendaItem | null>(null);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [loadingPdf, setLoadingPdf] = useState(false);

  // Attendance sheet related state is handled in the attendance sheet controller
  const [taskForm, setTaskForm] = useState<TaskFormData>({
    title: '',
    description: '',
    priority: 'medium',
    assignedTo: [],
    status: 'pending',
    deadline: null
  });
  const [agendaItemForm, setAgendaItemForm] = useState({
    title: '',
    description: '',
    order: 0
  });
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);


  useEffect(() => {
    if (committeeId && meetingId && meetingId !== 'undefined') {
      fetchMeetingDetails();
    } else if (meetingId === 'undefined') {
      console.error('Invalid meetingId in URL params');
      showNotification({
        title: 'Error',
        message: 'Invalid meeting ID in URL',
        color: 'red',
      });
    }

    // Cleanup function to stop all polling when component unmounts
    return () => {
      // Reset polling status on unmount
      pollingStatusRef.current = {};
    };
  }, [committeeId, meetingId]);

  // No need to initialize attendees selection, we're using attendance sheets now
  
  // Pre-fetch available users when meeting data loads
  useEffect(() => {
    if (meeting && typeof meeting.committee === 'object' && meeting.committee?._id) {
      fetchAvailableUsers();
    }
  }, [meeting?.committee]);
  
  const fetchMeetingDetails = async () => {
    try {
      if (!meetingId || meetingId === 'undefined') {
        console.error('Invalid meetingId:', meetingId);
        showNotification({
          title: 'Error',
          message: 'Invalid meeting ID',
          color: 'red',
        });
        return;
      }
      
      setLoading(true);
      setLoadingAgenda(true);
      
      // Fetch meeting details, tasks and agenda items separately for better reliability
      const [meetingRes, tasksRes, agendaRes] = await Promise.all([
        apiClient.get(`/api/meetings/${meetingId}`, {
          params: { populate: 'committee,attendees,invitations.member' }
        }),
        apiClient.get(`/api/meetings/${meetingId}/tasks`, {
          params: { limit: 1000 } // Get all tasks for this meeting
        }),
        apiClient.get(`/api/meetings/${meetingId}/agenda`)
      ]);

      setMeeting(meetingRes.data);
      
      // Handle both paginated and non-paginated response formats
      const tasksData = tasksRes.data.data || tasksRes.data;
      setTasks(Array.isArray(tasksData) ? tasksData : []);
      
      // Set agenda items (sorted by order)
      const agendaItems = agendaRes.data;
      setAgendaItems(agendaItems);
      
      // Check for any agenda items in processing state and start polling
      checkProcessingAgendaItems(agendaItems);
    } catch (error) {
      console.error('Failed to fetch meeting details:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to load meeting details',
        color: 'red',
      });
    } finally {
      setLoading(false);
      setLoadingAgenda(false);
    }
  };

  // Fetch available users for attendance display
  const fetchAvailableUsers = async (forceRefresh = false) => {
    // If we already have users and don't need a force refresh, skip the API call
    if (!forceRefresh && availableUsers.length > 0) {
      return;
    }

    try {
      // Fetch committee members
      if (typeof meeting?.committee === 'object' && meeting?.committee?._id) {
        const response = await apiClient.get(`/api/committee/${meeting.committee._id}/members`);
        setAvailableUsers(response.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch available users:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to load available users',
        color: 'red',
      });
    }
  };

  // Removed handleUpdateAttendees as it's replaced with the attendance sheet workflow

  const handleCreateTask = async () => {
    try {
      if (!taskForm.title || !taskForm.priority || !taskForm.status) {
        showNotification({
          title: 'Error',
          message: 'Please fill in all required fields (Title, Priority, and Status)',
          color: 'red',
        });
        return;
      }

      await apiClient.post(`/api/meetings/${meetingId}/tasks`, {
        ...taskForm,
        deadline: taskForm.deadline?.toISOString() || null,
      });

      showNotification({
        title: 'Success',
        message: 'Task created successfully',
        color: 'green',
      });

      setIsCreateTaskModalOpen(false);
      fetchMeetingDetails();
      resetTaskForm();
    } catch (error) {
      console.error('Failed to create task:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to create task',
        color: 'red',
      });
    }
  };

  const handleUpdateTaskStatus = async (taskId: string, newStatus: 'pending' | 'completed') => {
    try {
      await apiClient.patch(`/api/meetings/${meetingId}/tasks/${taskId}`, {
        status: newStatus
      });

      setTasks(tasks.map(task =>
        task._id === taskId ? { ...task, status: newStatus } : task
      ));

      showNotification({
        title: 'Success',
        message: 'Task status updated',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to update task status:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to update task status',
        color: 'red',
      });
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setTaskForm({
      title: task.title,
      description: task.description || '',
      priority: task.priority,
      assignedTo: task.assignedTo?.map(user => (typeof user === 'string' ? user : user._id)) || [],
      status: task.status,
      deadline: task.deadline ? new Date(task.deadline) : null
    });
    setIsEditTaskModalOpen(true);
  };
  const handleUpdateTask = async () => {
    try {
      if (!editingTask?._id) return;
      
      if (!taskForm.title || !taskForm.priority || !taskForm.status) {
        showNotification({
          title: 'Error',
          message: 'Please fill in all required fields (Title, Priority, and Status)',
          color: 'red',
        });
        return;
      }

      await apiClient.patch(`/api/meetings/${meetingId}/tasks/${editingTask._id}`, {
        ...taskForm,
        deadline: taskForm.deadline?.toISOString() || null,
      });

      showNotification({
        title: 'Success',
        message: 'Task updated successfully',
        color: 'green',
      });

      setIsEditTaskModalOpen(false);
      fetchMeetingDetails();
      resetTaskForm();
    } catch (error) {
      console.error('Failed to update task:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to update task',
        color: 'red',
      });
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await apiClient.delete(`/api/meetings/${meetingId}/tasks/${taskId}`);
      setTasks(tasks.filter(task => task._id !== taskId));
      showNotification({
        title: 'Success',
        message: 'Task deleted successfully',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to delete task:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to delete task',
        color: 'red',
      });
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
    return matchesStatus && matchesPriority;
  });



  const taskStats = {
    total: tasks.length,
    completed: tasks.filter(t => t.status === 'completed').length,
    pending: tasks.filter(t => t.status === 'pending').length,
    high: tasks.filter(t => t.priority === 'high').length,
  };

  const resetTaskForm = () => {
    setTaskForm({
      title: '',
      description: '',
      priority: 'medium',
      assignedTo: [],
      status: 'pending',
      deadline: new Date()
    });
    setEditingTask(null);
  };  // Reset agenda item form
  const resetAgendaItemForm = () => {
    setAgendaItemForm({
      title: '',
      description: '',
      order: agendaItems.length
    });
    setSelectedFiles([]);
    setEditingAgendaItem(null);
  };// Handle create agenda item
  const handleCreateAgendaItem = async () => {
    try {
      if (!agendaItemForm.title) {
        showNotification({
          title: 'Error',
          message: 'Please enter a title for the agenda item',
          color: 'red',
        });
        return;
      }

      // Create the agenda item
      const response = await apiClient.post(`/api/meetings/${meetingId}/agenda`, agendaItemForm);
      const newAgendaItemId = response.data._id;
      
      // If there are files to upload, upload them to the newly created agenda item
      if (selectedFiles.length > 0 && newAgendaItemId) {
        try {
          // Use the handleUploadFiles function directly with the current selectedFiles
          await handleUploadFiles(newAgendaItemId, selectedFiles);
          
          // Note: handleUploadFiles already shows a success notification, so we don't need another one here
        } catch (fileError: any) {
          console.error('Files upload failed:', fileError);
          
          // Extract more specific error message if available
          let errorMessage = 'Agenda item created but file upload failed';
          
          if (fileError.response?.data?.message) {
            errorMessage = `Agenda item created but ${fileError.response.data.message}`;
          } else if (fileError.message && fileError.message.includes("Unsupported file type")) {
            errorMessage = 'Agenda item created but file upload failed: Unsupported file type';
          } else if (fileError.message) {
            errorMessage = `Agenda item created but file upload failed: ${fileError.message}`;
          }
          
          showNotification({
            title: 'Warning',
            message: errorMessage,
            color: 'yellow',
          });
        }
      } else {
        showNotification({
          title: 'Success',
          message: 'Agenda item created successfully',
          color: 'green',
        });
      }

      setIsAgendaItemModalOpen(false);
      fetchMeetingDetails();
      resetAgendaItemForm();
      setSelectedFiles([]);
    } catch (error) {
      console.error('Failed to create agenda item:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to create agenda item',
        color: 'red',
      });
    }
  };  // Handle edit agenda item
  const handleEditAgendaItem = (agendaItem: AgendaItem) => {
    setEditingAgendaItem(agendaItem);
    setAgendaItemForm({
      title: agendaItem.title,
      description: agendaItem.description || '',
      order: agendaItem.order
    });
    // Clear selected files since we'll be showing existing files separately
    // and any new files will be added to the existing ones
    setSelectedFiles([]);
    setIsAgendaItemModalOpen(true);
  };

  // Handle update agenda item
  const handleUpdateAgendaItem = async () => {
    try {
      if (!editingAgendaItem?._id) return;
      
      if (!agendaItemForm.title) {
        showNotification({
          title: 'Error',
          message: 'Please enter a title for the agenda item',
          color: 'red',
        });
        return;
      }

      await apiClient.put(`/api/meetings/${meetingId}/agenda/${editingAgendaItem._id}`, agendaItemForm);

      showNotification({
        title: 'Success',
        message: 'Agenda item updated successfully',
        color: 'green',
      });

      setIsAgendaItemModalOpen(false);
      fetchMeetingDetails();
      resetAgendaItemForm();
    } catch (error) {
      console.error('Failed to update agenda item:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to update agenda item',
        color: 'red',
      });
    }
  };
  // Handle delete agenda item
  const handleDeleteAgendaItem = async (agendaItemId: string) => {
    try {
      await apiClient.delete(`/api/meetings/${meetingId}/agenda/${agendaItemId}`);
      
      // Update local state
      const updatedItems = agendaItems.filter(item => item._id !== agendaItemId);
      setAgendaItems(updatedItems);
      
      showNotification({
        title: 'Success',
        message: 'Agenda item deleted successfully',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to delete agenda item:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to delete agenda item',
        color: 'red',
      });
    }
  };  // Removed handleToggleAgendaItemCompleted as it's no longer needed
  // Handle reordering agenda items - temporarily disabled
  /*
  const handleMoveAgendaItem = async (agendaItemId: string, direction: 'up' | 'down') => {
    // Find current item index
    const currentIndex = agendaItems.findIndex(item => item._id === agendaItemId);
    if (currentIndex === -1) return;
    
    // Calculate new index
    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // Check if move is valid
    if (newIndex < 0 || newIndex >= agendaItems.length) return;
    
    // Create new ordered array
    const newOrderedItems = [...agendaItems];
    
    // Swap items
    [newOrderedItems[currentIndex], newOrderedItems[newIndex]] = 
      [newOrderedItems[newIndex], newOrderedItems[currentIndex]];
    
    // Update order numbers
    const updatedItems = newOrderedItems.map((item, index) => ({
      ...item,
      order: index
    }));
    
    // Optimistic update UI
    setAgendaItems(updatedItems);
    
    try {
      // Send reorder request to API
      await apiClient.put(`/api/meetings/${meetingId}/agenda/reorder`, {
        items: updatedItems.map(item => ({ id: item._id, order: item.order }))
      });
    } catch (error) {
      console.error('Failed to reorder agenda items:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to reorder agenda items',
        color: 'red',
      });
      // Revert on error
      fetchMeetingDetails();
    }
  };
  */
  
  // Handle file selection  
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>, agendaItemId?: string) => {    
    console.log("File select triggered for agenda item:", agendaItemId, e.target.files);
    if (e.target.files && e.target.files.length > 0) {
      // Check file types
      const filesArray = Array.from<File>(e.target.files);
      const allowedTypes = [
        'application/pdf', 
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv',
        'image/jpeg',
        'image/jpg',
        'image/png'
      ];
      
      // Check for unsupported file types
      const invalidFiles = filesArray.filter(file => !allowedTypes.includes(file.type));
      if (invalidFiles.length > 0) {
        const invalidFileNames = invalidFiles.map(f => f.name).join(', ');
        showNotification({
          title: 'Error',
          message: `Unsupported file type(s): ${invalidFileNames}. Only PDF, Word, Excel, text, CSV, and image files are supported.`,
          color: 'red',
        });
        // Reset the file input
        e.target.value = '';
        return;
      }
      
      console.log("Files selected for agenda item:", agendaItemId, filesArray);
      
      // Update state for component rendering
      setSelectedFiles(filesArray);
      
      // Only auto-upload if we're dealing with an existing agenda item
      if (agendaItemId && agendaItemId !== 'new') {
        // Immediate upload for existing agenda items - pass the filesArray directly
        handleUploadFiles(agendaItemId, filesArray);
      }
      // For new items, files will be uploaded along with the item creation
    } else {
      console.log("No files selected or files array is empty");
    }
  };  // Handle file upload
  const handleUploadFiles = async (agendaItemId: string, files?: File[]) => {
    const filesToUpload = files || selectedFiles;
    
    if (!filesToUpload.length) {
      console.error("No files selected for upload");
      showNotification({
        title: 'Error',
        message: 'No files selected for upload',
        color: 'red',
      });
      return;
    }

    console.log(`Preparing to upload ${filesToUpload.length} files:`, filesToUpload);
    
    const formData = new FormData();
    filesToUpload.forEach(file => {
      console.log(`Adding file to form: ${file.name} (${file.type}, ${file.size} bytes)`);
      formData.append('files', file);
    });

    try {
      console.log(`Uploading files to agenda item ${agendaItemId}...`);
      
      // Log the actual FormData content
      for (let pair of formData.entries()) {
        console.log(`Form data entry: ${pair[0]} = ${pair[1] instanceof File ? pair[1].name : pair[1]}`);
      }
      // For multipart/form-data, the browser will automatically set the correct Content-Type with boundary
      // We should NOT manually set 'Content-Type': 'multipart/form-data'
      
      // Use meetingId from component's state
      if (!meetingId || meetingId === 'undefined') {
        console.error('Invalid meetingId:', meetingId);
        showNotification({
          title: 'Error',
          message: 'Invalid meeting ID, cannot upload files',
          color: 'red',
        });
        return;
      }
      
      const response = await apiClient.post(
        `/api/meetings/${meetingId}/agenda/${agendaItemId}/files`, 
        formData
      );
      
      console.log("Upload response:", response.data);

      showNotification({
        title: 'Success',
        message: 'Files uploaded successfully',
        color: 'green',
      });

      // Clear selected files - only clear if these are the same files we just uploaded
      // This ensures we don't clear files that might have been selected during the async operation
      if (files === selectedFiles) {
        setSelectedFiles([]);
      } else {
        // If we were passed specific files, just clear the input field
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
          (input as HTMLInputElement).value = '';
        });
      }
      
      // Refresh meeting details
      fetchMeetingDetails();
    } catch (error: any) {
      console.error('Failed to upload files:', error);
      
      // Extract more specific error message if available
      let errorMessage = 'Failed to upload files';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message && error.message.includes("Unsupported file type")) {
        errorMessage = 'Unsupported file type. Only .pdf, .doc, .docx, .xls, .xlsx, .txt, .csv, .jpg, .jpeg, and .png files are allowed.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      showNotification({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    }
  };  // Handle file download
  const handleDownloadFile = (agendaItemId: string, fileId: string, filename: string) => {
    // Use meetingId from component's props
    if (!meetingId || meetingId === 'undefined') {
      console.error('Invalid meetingId:', meetingId);
      showNotification({
        title: 'Error',
        message: 'Invalid meeting ID, cannot download file',
        color: 'red',
      });
      return;
    }
    
    apiClient.get(`/api/meetings/${meetingId}/agenda/${agendaItemId}/files/${fileId}`, {
      responseType: 'blob'
    })
      .then(response => {
        // Create a URL for the blob
        const url = window.URL.createObjectURL(new Blob([response.data]));
        // Create a temporary link element
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        // Append the link to the body
        document.body.appendChild(link);
        // Trigger the download
        link.click();
        // Clean up
        link.parentNode?.removeChild(link);
        window.URL.revokeObjectURL(url);
      })
      .catch(() => {
        showNotification({
          title: 'Error',
          message: 'Failed to download file',
          color: 'red',
        });
      });
  };  // Handle download all files as zip
  const handleDownloadAllFiles = (agendaItemId: string) => {
    // Use meetingId from component's state
    if (!meetingId || meetingId === 'undefined') {
      console.error('Invalid meetingId:', meetingId);
      showNotification({
        title: 'Error',
        message: 'Invalid meeting ID, cannot download files',
        color: 'red',
      });
      return;
    }
    
    apiClient.get(`/api/meetings/${meetingId}/agenda/${agendaItemId}/files/download-zip`, {
      responseType: 'blob'
    })
      .then(response => {
        // Create a URL for the blob
        const url = window.URL.createObjectURL(new Blob([response.data]));
        // Create a temporary link element
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `agenda-item-${agendaItemId}-files.zip`);
        // Append the link to the body
        document.body.appendChild(link);
        // Trigger the download
        link.click();
        // Clean up
        link.parentNode?.removeChild(link);
        window.URL.revokeObjectURL(url);
      })
      .catch(error => {
        console.error('Failed to download files as ZIP:', error);
        showNotification({
          title: 'Error',
          message: 'Failed to download files as ZIP',
          color: 'red',
        });
      });
  };  // Handle delete file
  const handleDeleteFile = async (agendaItemId: string, fileId: string) => {
    try {
      // Use meetingId from component state
      if (!meetingId || meetingId === 'undefined') {
        console.error('Invalid meetingId:', meetingId);
        showNotification({
          title: 'Error',
          message: 'Invalid meeting ID, cannot delete file',
          color: 'red',
        });
        return;
      }
      
      await apiClient.delete(`/api/meetings/${meetingId}/agenda/${agendaItemId}/files/${fileId}`);
      
      showNotification({
        title: 'Success',
        message: 'File deleted successfully',
        color: 'green',
      });
      
      // Refresh agenda items
      fetchMeetingDetails();
    } catch (error) {
      console.error('Failed to delete file:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to delete file',
        color: 'red',
      });
    }
  };

  // Handle analyzing or checking status of an agenda item
  const handleAnalyzeAgendaItem = async (item: AgendaItem) => {
    // If already analyzed and status is "complete" (32), open the analysis directly
    if (item.analysis && item.analysis.status === 32 && item.analysis.token) {
      window.open(`${import.meta.env.VITE_API_URL}/editor?id=${item.analysis.token}`, '_blank');
      return;
    }
    
    // If analysis is in progress, check status instead of requesting new analysis
    if (item.analysis && (item.analysis.status === 1 || item.analysis.status === 16)) {
      try {
        setIsAnalysisLoading(true);
        
        // Check current status from server
        const statusResponse = await apiClient.get(`/api/meetings/${meetingId}/agenda/${item._id}/analysis/status`);
        const { status, message, analysisUrl } = statusResponse.data;
        
        // Update local status based on server response
        const updatedItems = agendaItems.map<AgendaItem>(agendaItem => {
          return {
            _id: agendaItem._id,
            title: agendaItem.title,
            description: agendaItem.description,
            order: agendaItem.order,
            analysis: agendaItem.analysis ?? {
              status: 0,
              token: null,
              time: null
            },
            createdAt: agendaItem.createdAt,
            meeting: agendaItem.meeting,
            updatedAt: agendaItem.updatedAt,
            createdBy: agendaItem.createdBy,
            files: agendaItem.files ?? []
          };
        });
        
        setAgendaItems(updatedItems);
        
        // Show appropriate notification based on status
        showNotification({
          title: 'Analysis Status',
          message: message || getStatusMessage(status),
          color: status === 32 ? 'green' : status === -1 ? 'red' : 'blue'
        });
        
        // If analysis is complete, open it
        if (status === 32 && analysisUrl) {
          
          window.open(`${import.meta.env.VITE_API_URL}/editor?id=${item.analysis?.token}`, '_blank');
        }
        
        setIsAnalysisLoading(false);
        return;
      } catch (error) {
        console.error('Error checking analysis status:', error);
        setIsAnalysisLoading(false);
        
        showNotification({
          title: 'Error',
          message: 'Failed to check analysis status',
          color: 'red'
        });
        return;
      }
    }
    
    // If no analysis yet or it failed, request a new analysis
    setAnalyzingAgendaItem(item);
    setIsAnalysisLoading(true);
    setIsAnalysisModalOpen(true);
    
    try {
      // Request analysis from the server
      const response = await apiClient.post(`/api/meetings/${meetingId}/agenda/${item._id}/analyze`);

      // Update the agenda item with analysis token and status

      setAgendaItems((agendaItems) => agendaItems.map(agendaItem => 
        agendaItem._id === item._id 
          ? { ...agendaItem, analysis: response.data.analysis } 
          : agendaItem
      ));
      setAnalyzingAgendaItem(prev => prev ? { ...prev, analysis: response.data.analysis } : null);
      
      // Start polling for analysis status
      startAnalysisStatusPolling(item._id, item.analysis?.token);
      
      showNotification({
        title: 'Analysis Requested',
        message: 'Analysis has been requested and will be available soon',
        color: 'green'
      });
    } catch (error) {
      console.error('Error analyzing agenda item:', error);
      
      showNotification({
        title: 'Analysis Failed',
        message: 'There was a problem analyzing this agenda item',
        color: 'red'
      });
    } finally {
      setIsAnalysisLoading(false);
    }
  };

  // Helper function to get status message - aligned with backend AnalysisStatus enum
  const getStatusMessage = (status: number) => {
    switch (status) {
      case 0:
        return "Not analyzed";
      case 1:
        return "Analysis queued";
      case 16:
        return "Analysis in progress";
      case 32:
        return "Analysis complete";
      case -1:
        return "Analysis failed";
      default:
        return "Unknown status";
    }
  };

  // Function to check for agenda items in processing state and start polling
  const checkProcessingAgendaItems = (items: AgendaItem[]) => {
    items.forEach(item => {
      // If item has analysis in progress (status 1 or 16) and not already polling
      if (
        item.analysis && 
        (item.analysis.status === 1 || item.analysis.status === 16) &&
        !pollingStatusRef.current[item._id]
      ) {
        // Start polling for this item
        startAnalysisStatusPolling(item._id, item.analysis?.token);
      }
    });
  };

  // Function to poll for analysis status
  const startAnalysisStatusPolling = async (itemId: string, _token?: string | null) => {
    // Initialize polling interval variables
    let pollingCount = 0;
    const maxPolls = 20; // Maximum number of polling attempts
    const pollingDelay = 3000; // 3 seconds between polls
    
    // Mark this item as being polled
    pollingStatusRef.current = { ...pollingStatusRef.current, [itemId]: true };
    
    const pollStatus = async () => {
      try {
        if (pollingCount >= maxPolls) {
          // Stop polling after max attempts
          pollingStatusRef.current = { ...pollingStatusRef.current, [itemId]: false };
          // showNotification({
          //   title: 'Analysis Taking Too Long',
          //   message: 'The analysis is still processing. Check back later.',
          //   color: 'yellow',
          // });
          return;
        }
        
        pollingCount++;
        
        // Check analysis status
        const statusResponse = await apiClient.get(`/api/meetings/${meetingId}/agenda/${itemId}/analysis/status`);
        const { status } = statusResponse.data;
        
        // If analysis is complete (status 3)
        if (status === 32) {
          // Stop polling
          pollingStatusRef.current = { ...pollingStatusRef.current, [itemId]: false };

          setAgendaItems((agendaItems) => agendaItems.map<AgendaItem>(agendaItem => 
            agendaItem._id === itemId 
              ? { 
                  ...agendaItem, 
                  analysis: { 
                    token: agendaItem.analysis?.token || null,
                    status: 32, // Updated to match backend constant AnalysisStatus.ANALYSED
                    time: agendaItem.analysis?.time || null
                  } 
                } 
              : agendaItem
          ));
          
          // If this is the currently analyzing item, update it
          setAnalyzingAgendaItem(prev => 
            prev && prev._id === itemId 
              ? { 
                  ...prev, 
                  analysis: { 
                    token: prev.analysis?.token || null,
                    status: 32, // Updated to match backend constant AnalysisStatus.ANALYSED
                    time: prev.analysis?.time || null
                  } 
                } 
              : prev
          );

        } 
        // If still in progress, continue polling
        else if (status === 1 || status === 16) {
          setTimeout(pollStatus, pollingDelay);
        } 
        // If failed or unknown status
        else {
          // Stop polling
          pollingStatusRef.current = { ...pollingStatusRef.current, [itemId]: false };
        }
      } catch (error) {
        console.error('Error polling analysis status:', error);
        // Stop polling on error
        pollingStatusRef.current = { ...pollingStatusRef.current, [itemId]: false };
      }
    };
    
    // Start polling
    setTimeout(pollStatus, pollingDelay);
  };


  // Early return if required params are missing
  if (!committeeId || !meetingId) {
    return (
      <Container size="xl" py="md">
        <Stack align="center" justify="center" h={400} gap="md">
          <Text c="red" size="lg">Error: Committee ID and Meeting ID are required</Text>
          <Button 
            variant="light"
            onClick={() => navigate(-1)}
          >
            Go Back
          </Button>
        </Stack>
      </Container>
    );
  }

  const downloadPdf = async () => {
    console.log('downloadPdf called, meeting:', meeting);
    console.log('meeting tasks:', meeting?.tasks);

    if(!meeting) {
      console.log('No meeting data available');
      showNotification({ title: 'Error', message: 'Meeting data not available', color: 'red' });
      return;
    }

    if(!meeting.tasks?.length) {
      console.log('No tasks found in meeting');
      showNotification({ title: 'Error', message: 'No tasks to download', color: 'red' });
      return;
    }

    try {
      setLoadingPdf(true);
      console.log('Starting PDF generation...');

      const blob = await pdf(<TaskPdfDocument meeting={meeting} />).toBlob();
      console.log('PDF blob generated:', blob);

      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = 'tasks-report.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showNotification({ title: 'Success', message: 'Tasks report downloaded successfully', color: 'green' });
    } catch (error) {
      console.error('PDF generation error:', error);
      showNotification({
        title: 'Error',
        message: `Failed to download tasks report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        color: 'red'
      });
    } finally {
      setLoadingPdf(false);
    }
  }

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>{meeting?.title}</Title>
          <Group gap="xs">
            <Text c="dimmed">Meeting Tasks and Agenda</Text>
            {meeting?.frequency && (
              <>
                <Text c="dimmed">•</Text>
                <Text c="dimmed" tt="capitalize">
                  {meeting.frequency 
                    ? (meeting.frequency.includes('(') 
                        ? meeting.frequency.split('(')[0].trim() 
                        : meeting.frequency)
                    : ''}
                </Text>
              </>
            )}
          </Group>
          {/* Display current attendees */}
          {meeting?.attendees && meeting.attendees.length > 0 && (
            <Group gap="xs" mt="xs">
              <Text c="dimmed" size="sm">Attendees:</Text>
              <Group gap="xs">
                {meeting.attendees.slice(0, 3).map((attendee, index) => (
                  <Badge key={index} variant="light" size="sm">
                    {typeof attendee === 'string' ? attendee : attendee.name}
                  </Badge>
                ))}
                {meeting.attendees.length > 3 && (
                  <Badge variant="light" size="sm" c="dimmed">
                    +{meeting.attendees.length - 3} more
                  </Badge>
                )}
              </Group>
            </Group>
          )}
        </div>
        <Group>
          <Button
            variant="outline"
            leftSection={<IconUpload size={16} />}
            onClick={() => setIsUploaderOpen(true)}
          >
            Upload MOM
          </Button>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => setIsCreateTaskModalOpen(true)}
          >
            Create Task
          </Button>
          <Button
            leftSection={<IconDownload size={16} />}
            onClick={downloadPdf}
            w={"fit-content"}
            loading={loadingPdf}
            loaderProps={{type : 'dots'}}
            style={{ alignSelf: 'flex-end' }}
          >
            Download Tasks
          </Button>
        </Group>
      </Group>

      <Modal
        opened={isUploaderOpen}
        onClose={() => setIsUploaderOpen(false)}
        title="Upload MOM"
        size="xl"
        styles={{
          header: {
            backgroundColor: 'var(--mantine-color-body)',
            borderBottom: '1px solid var(--mantine-color-gray-3)',
            position: 'sticky',
            top: 0,
            zIndex: 1000
          },
          body: {
            padding: 0
          },
          content: {
            overflowY: 'hidden'
          }
        }}
      >
        <ScrollArea.Autosize mah="calc(90vh - 80px)" p="md">
          <TaskImageUploader 
            meetingId={meetingId!} 
            onTasksCreated={() => {
              setIsUploaderOpen(false);
              fetchMeetingDetails();
            }} 
          />
        </ScrollArea.Autosize>
      </Modal>
      <Tabs defaultValue="tasks" style={{ position: "sticky", top: "100px" }}>
        <Tabs.List>
          <Tabs.Tab value="tasks">Tasks</Tabs.Tab>
          <Tabs.Tab value="agenda">Agenda</Tabs.Tab>
          <Tabs.Tab value="attendees">Attendees</Tabs.Tab>
        </Tabs.List>        <Tabs.Panel value="tasks" pt="md">
          {loading ? (
            <Stack align="center" justify="center" h="60vh">
              <Text>Loading tasks...</Text>
            </Stack>
          ) : tasks.length === 0 ? (
            <Stack align="center" justify="center" h="60vh" gap="md">
              <Text size="xl" fw={500} c="dimmed" ta="center">No tasks have been created yet</Text>
              <Text size="sm" c="dimmed" ta="center" maw={400}>
                Click 'Create Task' button to add a new task or use 'Upload MOM' to extract tasks from your meeting minutes.
              </Text>
            </Stack>
          ) : (
            <Stack>
              {/* Task Stats */}
              <SimpleGrid cols={4}>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">Total Tasks</Text>
                    <Text fw={700} size="xl">{taskStats.total}</Text>
                  </Stack>
                </Paper>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">Completed</Text>
                    <Text fw={700} size="xl" c="green">{taskStats.completed}</Text>
                  </Stack>
                </Paper>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">Pending</Text>
                    <Text fw={700} size="xl" c="blue">{taskStats.pending}</Text>
                  </Stack>
                </Paper>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">High Priority</Text>
                    <Text fw={700} size="xl" c="red">{taskStats.high}</Text>
                  </Stack>
                </Paper>
              </SimpleGrid>

              {/* Progress Bar */}
              <Paper withBorder p="md">
                <Stack>
                  <Text size="sm" fw={500}>Progress</Text>
                  <Progress 
                    value={tasks.length ? (taskStats.completed / taskStats.total) * 100 : 0}
                    color="green"
                    size="lg"
                  />
                  <Text size="xs" c="dimmed">
                    {taskStats.completed} of {taskStats.total} tasks completed
                  </Text>
                </Stack>
              </Paper>

              {/* Filters */}
              <Paper withBorder mb="lg">
                <UnstyledButton 
                  onClick={() => setIsFiltersCollapsed(!isFiltersCollapsed)} 
                  style={{ width: '100%' }}
                  py="xs" px="md"
                >
                  <Group justify="space-between">
                    <Group gap="xs">
                      <IconFilter size={16} />
                      <Text fw={500} size="sm">Filters</Text>
                    </Group>
                    {isFiltersCollapsed ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
                  </Group>
                </UnstyledButton>
                
                
                <Collapse in={!isFiltersCollapsed}>
                  <Divider />
                  <Box p="md">
                    <Group>
                      <SegmentedControl
                        data={[
                          { label: 'All', value: 'all' },
                          { label: 'Pending', value: 'pending' },
                          { label: 'Completed', value: 'completed' }
                        ]}
                        value={statusFilter}
                        onChange={(value) => setStatusFilter(value as 'all' | 'pending' | 'completed')}
                      />
                      <SegmentedControl
                        data={[
                          { label: 'All', value: 'all' },
                          { label: 'High', value: 'high' },
                          { label: 'Medium', value: 'medium' },
                          { label: 'Low', value: 'low' }
                        ]}
                        value={priorityFilter}
                        onChange={(value) => setPriorityFilter(value as 'all' | 'low' | 'medium' | 'high')}
                      />
                    </Group>
                  </Box>
                </Collapse>
              </Paper>

              {/* Task List */}
              {filteredTasks.length === 0 ? (
                <Stack align="center" justify="center" h="20vh">
                  <Text c="dimmed" ta="center">No tasks match the selected filters.</Text>
                </Stack>
              ) : (
                <Stack>
                  {filteredTasks.map((task) => (
                    <Paper key={task._id} p="md" withBorder>
                      <Group justify="space-between" mb="xs" wrap="nowrap">
                        <Group wrap="nowrap" style={{ overflow: 'hidden', flex: 1 }}>
                          <ActionIcon 
                            variant={task.status === 'completed' ? 'filled' : 'light'}
                            color={task.status === 'completed' ? 'green' : 'gray'}
                            title={task.status === 'completed' ? 'Mark as Pending' : 'Mark as Completed'}
                            onClick={() => handleUpdateTaskStatus(task._id, task.status === 'completed' ? 'pending' : 'completed')}
                            style={{ flexShrink: 0 }}
                          >
                            <IconCheck size={16} />
                          </ActionIcon>
                          <Text fw={500} truncate style={{ flex: 1 }}>
                            {task.title}
                          </Text>
                        </Group>
                        <Menu position="bottom-end">
                          <Menu.Target>
                            <ActionIcon style={{ flexShrink: 0 }}>
                              <IconDotsVertical size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item 
                              leftSection={<IconEdit size={14} />}
                              onClick={() => handleEditTask(task)}
                            >
                              Edit
                            </Menu.Item>
                            <Menu.Item 
                              color="red"
                              leftSection={<IconTrash size={14} />}
                              onClick={() => handleDeleteTask(task._id)}
                            >
                              Delete
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                      
                      {task.description && (
                        <Text size="sm" c="dimmed" mb="xs">{task.description}</Text>
                      )}
                      
                      {/* Status and Priority Badges */}
                      <Group mb="sm">
                        <Badge color={task.status === 'completed' ? 'green' : 'blue'}>
                          {task.status}
                        </Badge>
                        <Badge color={task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'blue'}>
                          {task.priority}
                        </Badge>
                      </Group>
                      
                      <Stack gap="xs">
                        {/* Assigned users */}
                        {task.assignedTo && task.assignedTo.length > 0 && (
                          <Group gap="xs" align="flex-start" wrap="nowrap">
                            <IconUsers size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
                            <Text size="sm">
                              Assigned to: {task.assignedTo.map(person => person.name).join(', ')}
                            </Text>
                          </Group>
                        )}
                        
                        {/* Status information */}
                        {task.status === 'completed' && (
                          <Group gap="xs" align="flex-start" wrap="nowrap">
                            <IconCheck size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
                            <Text size="sm" fw={500}>
                              Completed
                            </Text>
                          </Group>
                        )}
                        
                        {/* Deadline information */}
                        {task.deadline && (
                          <Group gap="xs" align="flex-start" wrap="nowrap">
                            <IconCalendarStats size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
                            <Text size="sm">
                              Due: {new Date(task.deadline).toLocaleDateString()}
                            </Text>
                          </Group>
                        )}
                        
                        {/* Meeting link */}
                        {/* <Group gap="xs" align="flex-start" wrap="nowrap">
                          <IconLink size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
                          <Text size="sm" component={Link} to={`/committee/${committeeId}/meetings/${task.meeting}`} 
                            style={{ textDecoration: 'none' }} c="blue" onClick={(e) => e.stopPropagation()}>
                            {meeting?.title || "Meeting"}
                          </Text>
                        </Group> */}
                        
                        {/* Committee link */}
                        {/* <Group gap="xs" align="flex-start" wrap="nowrap">
                          <IconBuilding size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
                          <Text size="sm" component={Link} to={`/committee/${committeeId}`} 
                            style={{ textDecoration: 'none' }} c="blue" onClick={(e) => e.stopPropagation()}>
                            {typeof meeting?.committee === 'object' ? meeting.committee.name : "Committee"}
                          </Text>
                        </Group> */}
                      </Stack>
                    </Paper>
                  ))}
                </Stack>
              )}
            </Stack>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="agenda" pt="md">
          <Group justify="space-between" mb="md">
            <Text size="lg" fw={500}>Meeting Agenda</Text>
            <Group>
              <Button
                variant="outline"
                leftSection={<IconPresentation size={16} />}
                onClick={() => setIsAgendaUploaderOpen(true)}
              >
                Upload Agenda PDF
              </Button>
              <Button 
                leftSection={<IconPlus size={16} />}
                onClick={() => {
                  resetAgendaItemForm();
                  setIsAgendaItemModalOpen(true);
                }}
              >
                Add Agenda Item
              </Button>
            </Group>
          </Group>

          {loadingAgenda ? (
            <Stack align="center" justify="center" h="60vh">
              <Text>Loading agenda items...</Text>
            </Stack>
          ) : agendaItems.length === 0 ? (
            <Stack align="center" justify="center" h="60vh" gap="md">
              <Text size="xl" fw={500} c="dimmed" ta="center">No agenda items have been added yet</Text>
              <Text size="sm" c="dimmed" ta="center" maw={400}>
                Click 'Add Agenda Item' button to start building your meeting agenda.
              </Text>
            </Stack>
          ) : (
            <Stack>
              {agendaItems.map((item) => (                <Paper key={item._id} p="md" withBorder>                  <Group justify="space-between" mb="xs">                    <Group>
                      <Text fw={600}>
                        {item.title}
                      </Text>
                      {item.analysis && item.analysis.status !== 0 && (
                        <Badge 
                          color={
                            item.analysis.status === 32 ? "green" : 
                            item.analysis.status === 16 ? "yellow" : 
                            item.analysis.status === 1 ? "blue" : 
                            item.analysis.status === -1 ? "red" : "gray"
                          }
                          variant="light" 
                          size="sm"
                        >
                          {item.analysis.status === 32 ? "Analyzed" : 
                           item.analysis.status === 16 ? "Processing" :
                           item.analysis.status === 1 ? "Queued" :
                           item.analysis.status === -1 ? "Failed" : "Unknown"
                          }
                        </Badge>
                      )}
                    </Group>
                    <Group>
                      {/* Reordering buttons - hidden for now */}
                      {/* 
                      <Group>
                        <ActionIcon 
                          size="sm" 
                          disabled={index === 0}
                          onClick={() => handleMoveAgendaItem(item._id, 'up')}
                          title="Move Up"
                        >
                          <IconArrowUp size={16} />
                        </ActionIcon>
                        <ActionIcon 
                          size="sm" 
                          disabled={index === agendaItems.length - 1}
                          onClick={() => handleMoveAgendaItem(item._id, 'down')}
                          title="Move Down"
                        >
                          <IconArrowDown size={16} />
                        </ActionIcon>
                      </Group>
                      */}
                        <Menu position="bottom-end">
                        <Menu.Target>
                          <ActionIcon>
                            <IconDotsVertical size={16} />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>                          <Menu.Item 
                            leftSection={<IconEdit size={14} />}
                            onClick={() => handleEditAgendaItem(item)}
                          >
                            Edit
                          </Menu.Item>
                          {/* Show "View Analysis" when analysis is complete */}
                          {item.analysis && item.analysis.status === 32 ? (
                            <Menu.Item 
                              leftSection={<IconPresentation size={14} />}
                              onClick={() => {
                                console.log("Opening analysis for item:", item);
                                // Open the analysis directly in a new tab
                                const token = item.analysis?.token;
                                if (token) {
                                  window.open(`${import.meta.env.VITE_API_URL}/editor?id=${token}`, '_blank');
                                }
                              }}
                            >
                              View Analysis
                            </Menu.Item>
                          ) : (
                            // Show "Check Status" or "Analyze" button as appropriate
                            <Menu.Item 
                              leftSection={<IconPresentation size={14} />}
                              onClick={() => handleAnalyzeAgendaItem(item)}
                              disabled={isAnalysisLoading}
                              color={(item.analysis && item.analysis.status > 0 && item.analysis.status < 32) ? "blue" : undefined}
                            >
                              {(item.analysis && item.analysis.status > 0 && item.analysis.status < 32) ? 'Check Status' : 'Analyze'}
                            </Menu.Item>
                          )}
                          {item.files && item.files.length > 0 && (
                            <Menu.Item 
                              leftSection={<IconFileZip size={14} />}
                              onClick={() => handleDownloadAllFiles(item._id)}
                            >
                              Download All Files
                            </Menu.Item>
                          )}
                          <Menu.Item 
                            color="red" 
                            leftSection={<IconTrash size={14} />}
                            onClick={() => handleDeleteAgendaItem(item._id)}
                          >
                            Delete
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Group>
                  {item.description && (
                    <Text size="sm" c="dimmed" mb="xs">{item.description}</Text>
                  )}
                  {item.files && item.files.length > 0 && (
                    <Group gap="xs" mt="xs" align="flex-start" wrap="nowrap">
                      <IconPaperclip size={16} style={{ flexShrink: 0, marginTop: 2 }} />
                      <div>
                        <Text size="sm" fw={500} style={{ marginBottom: 4 }}>Attachments:</Text>
                        <Group gap="xs" style={{ flexWrap: 'wrap' }}>
                          {item.files.map(file => (
                            <Badge 
                              key={file._id}
                              variant="outline"
                              style={{ cursor: 'pointer' }}
                              onClick={() => handleDownloadFile(item._id, file._id, file.originalname)}
                              rightSection={
                                <Group gap={0}>
                                  <ActionIcon 
                                    size="xs" 
                                    color="red" 
                                    variant="transparent"
                                    onClick={(e) => {
                                      e.stopPropagation(); // Prevent badge click (download) when delete is clicked
                                      handleDeleteFile(item._id, file._id);
                                    }}
                                  >
                                    <IconTrash size={12} />
                                  </ActionIcon>
                                </Group>
                              }
                            >
                              {file.originalname}
                            </Badge>
                          ))}
                        </Group>
                      </div>
                    </Group>
                  )}
                  <Group mt="md">
                    <input
                      type="file"
                      id={`file-upload-${item._id}`}
                      style={{ display: 'none' }}
                      onChange={(e) => handleFileSelect(e, item._id)}
                      multiple
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.csv,.jpg,.jpeg,.png"
                    />
                    <Button
                      component="label"
                      htmlFor={`file-upload-${item._id}`}
                      variant="light"
                      leftSection={<IconUpload size={16} />}
                      size="sm"
                    >
                      Upload Files
                    </Button>
                  </Group>
                </Paper>
              ))}
            </Stack>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="attendees" pt="md">
          <Group justify="space-between" mb="md">
            <Text size="lg" fw={500}>Committee Members Attendance</Text>
            <Group>
              <Button
                color="teal"
                leftSection={<IconMail size={16} />}
                onClick={() => setIsInviteModalOpen(true)}
              >
                Invite Members
              </Button>
              <Button
                color="blue"
                leftSection={<IconFileCheck size={16} />}
                onClick={() => setIsAttendanceSheetUploaderOpen(true)}
              >
                Manage Attendance Sheets
              </Button>
            </Group>
          </Group>

          {availableUsers.length > 0 ? (
            <Stack>
              <Paper withBorder p="md" mb="md">
                <Group mb="md">
                  <Text fw={500}>Current Attendance Status</Text>
                  {meeting?.documents?.find(doc => doc.isAttendanceSheet) && (
                    <Badge color="green">Attendance Sheet Uploaded</Badge>
                  )}
                </Group>
                
                {/* Invitation summary */}
                {meeting?.invitations && meeting.invitations.length > 0 && (
                  <Group mb="md">
                    <Text size="sm">
                      <Text span fw={500}>{meeting.invitations.length}</Text> member(s) invited
                      {meeting.invitations.some(inv => inv.status === 'confirmed') && (
                        <Text span> • <Text span fw={500} c="cyan">
                          {meeting.invitations.filter(inv => inv.status === 'confirmed').length}
                        </Text> confirmed</Text>
                      )}
                      {meeting.invitations.some(inv => inv.status === 'declined') && (
                        <Text span> • <Text span fw={500} c="red">
                          {meeting.invitations.filter(inv => inv.status === 'declined').length}
                        </Text> declined</Text>
                      )}
                    </Text>
                  </Group>
                )}
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
                  {availableUsers.map((member) => {
                    const isAttending = meeting?.attendees?.some(attendee => 
                      (typeof attendee === 'string' ? attendee : attendee._id) === member._id
                    );
                    
                    // Check if member has been invited
                    const invitation = meeting?.invitations?.find(inv => 
                      (typeof inv.member === 'string' ? 
                        inv.member === member._id : 
                        inv.member._id === member._id)
                    );
                    
                    const isInvited = !!invitation;
                    const invitationStatus = invitation?.status || null;
                    
                    // Array to store status badges
                    const statusBadges = [];
                    
                    // Add attendance status badge
                    if (isAttending) {
                      statusBadges.push({ color: 'green', text: 'Present' });
                    } else {
                      statusBadges.push({ color: 'gray', text: 'Absent' });
                    }
                    
                    // Add invitation status badge if invited
                    if (isInvited) {
                      if (invitationStatus === 'confirmed') {
                        statusBadges.push({ color: 'cyan', text: 'Confirmed' });
                      } else if (invitationStatus === 'declined') {
                        statusBadges.push({ color: 'red', text: 'Declined' });
                      } else {
                        statusBadges.push({ color: 'blue', text: 'Invited' });
                      }
                    }
                    
                    return (
                      <Paper key={member._id} p="md" withBorder radius="md">
                        <Group>
                          <div style={{
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            backgroundColor: isAttending ? 'var(--mantine-color-green-1)' : 
                              isInvited ? 'var(--mantine-color-blue-1)' : 'var(--mantine-color-gray-1)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: isAttending ? 'var(--mantine-color-green-7)' : 
                              isInvited ? 'var(--mantine-color-blue-7)' : 'var(--mantine-color-gray-7)',
                            fontWeight: 600,
                            border: isInvited && !isAttending ? '1px dashed var(--mantine-color-blue-7)' : 'none'
                          }}>
                            {member.name ? member.name.charAt(0).toUpperCase() : '?'}
                          </div>
                          <div style={{ flex: 1 }}>
                            <Text fw={500} size="sm">{member.name}</Text>
                            <Text size="xs" c="dimmed">{member.designation || 'Member'}</Text>
                          </div>
                          <Stack gap={5}>
                            {statusBadges.map((badge, index) => (
                              <Badge key={index} color={badge.color}>
                                {badge.text}
                              </Badge>
                            ))}
                            {isInvited && (
                              <Text size="xs" c="dimmed" ta="center">
                                {new Date(invitation.invitedAt).toLocaleDateString()}
                              </Text>
                            )}
                          </Stack>
                        </Group>
                      </Paper>
                    );
                  })}
                </SimpleGrid>
              </Paper>
            </Stack>
          ) : (
            <Stack align="center" justify="center" h="60vh" gap="md">
              <Text size="xl" fw={500} c="dimmed" ta="center">No committee members found</Text>
              <Text size="sm" c="dimmed" ta="center" maw={400}>
                Please make sure members are assigned to this committee.
              </Text>
              <Button 
                leftSection={<IconUsers size={16} />}
                onClick={() => {
                  // Force refresh committee members
                  fetchAvailableUsers(true);
                }}
              >
                Refresh Member List
              </Button>
            </Stack>
          )}
        </Tabs.Panel>
      </Tabs>

      {/* Create/Edit Task Modal */}
      <Modal
        opened={isCreateTaskModalOpen || isEditTaskModalOpen}
        onClose={() => {
          setIsCreateTaskModalOpen(false);
          setIsEditTaskModalOpen(false);
          resetTaskForm();
        }}
        title={editingTask ? 'Edit Task' : 'Create New Task'}
      >
        <Stack>
          <TextInput
            label="Title"
            placeholder="Enter task title"
            value={taskForm.title}
            onChange={(e) => setTaskForm({ ...taskForm, title: e.target.value })}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter task description"
            value={taskForm.description}
            onChange={(e) => setTaskForm({ ...taskForm, description: e.target.value })}
            minRows={3}
            resize="vertical"
          />

          <Select
            label="Priority"
            value={taskForm.priority}
            onChange={(value) => setTaskForm({ ...taskForm, priority: value as 'low' | 'medium' | 'high' })}
            data={[
              { value: 'low', label: 'Low' },
              { value: 'medium', label: 'Medium' },
              { value: 'high', label: 'High' }
            ]}
            required
          />

          <Select
            label="Status"
            value={taskForm.status}
            onChange={(value) => setTaskForm({ ...taskForm, status: value as 'pending' | 'completed' })}
            data={[
              { value: 'pending', label: 'Pending' },
              { value: 'completed', label: 'Completed' }
            ]}
            required
          />

          <MultiSelect
            label="Assign To"
            placeholder="Select committee members"
            value={taskForm.assignedTo}
            onChange={(value) => setTaskForm({ ...taskForm, assignedTo: value })}
            data={
              typeof meeting?.committee === 'object' && meeting?.committee?.members
                ? meeting.committee.members.map(member => ({
                    value: member._id ?? "",
                    label: member.name ?? "-"
                  }))
                : []
            }
          />

          <DateInput
            label="Deadline"
            placeholder="Select a deadline"
            value={taskForm.deadline}
            onChange={(value: string | null) => setTaskForm({ ...taskForm, deadline: value ? new Date(value) : null })}
            clearable
          />

          <Button onClick={editingTask ? handleUpdateTask : handleCreateTask}>
            {editingTask ? 'Update Task' : 'Create Task'}
          </Button>
        </Stack>      </Modal>
      
      {/* Agenda Image Uploader Modal */}
      <Modal
        opened={isAgendaUploaderOpen}
        onClose={() => setIsAgendaUploaderOpen(false)}
        title="Upload Agenda PDF"
        size="xl"
        styles={{
          header: {
            backgroundColor: 'var(--mantine-color-body)',
            borderBottom: '1px solid var(--mantine-color-gray-3)',
            position: 'sticky',
            top: 0,
            zIndex: 1000
          },
          body: {
            padding: 0
          },
          content: {
            overflowY: 'hidden'
          }
        }}
      >
        <ScrollArea.Autosize mah="calc(90vh - 80px)" p="md">
          <AgendaImageUploader 
            meetingId={meetingId!} 
            onAgendaItemsCreated={() => {
              setIsAgendaUploaderOpen(false);
              fetchMeetingDetails();
            }} 
          />
        </ScrollArea.Autosize>
      </Modal>
      
      {/* Create/Edit Agenda Item Modal */}
      <Modal
        opened={isAgendaItemModalOpen}
        onClose={() => {
          setIsAgendaItemModalOpen(false);
          resetAgendaItemForm();
          setSelectedFiles([]);
        }}
        title={editingAgendaItem ? 'Edit Agenda Item' : 'Add Agenda Item'}
        size="lg"
      >        <Stack>
          <TextInput
            label="Title"
            placeholder="Enter agenda item title"
            value={agendaItemForm.title}
            onChange={(e) => setAgendaItemForm({ ...agendaItemForm, title: e.target.value })}
            required
          />            <Textarea
            label="Description"
            placeholder="Enter details about this agenda item"
            value={agendaItemForm.description}
            onChange={(e) => setAgendaItemForm({ ...agendaItemForm, description: e.target.value })}
            minRows={3}
            resize="vertical"
          />
          
          {/* Only show file upload when adding a new item, not when editing */}
          {!editingAgendaItem && (
            <>
              <Divider my="sm" label="File Attachments" labelPosition="center" />
              <Box mt="md">
                <Text size="sm" fw={500} mb="xs">Add Files</Text>
                <Group>
                  <Button
                    component="label"
                    htmlFor="fileInputModal"
                    leftSection={<IconUpload size={16} />}
                    variant="outline"
                  >
                    Select Files
                  </Button>
                  <input
                    type="file"
                    multiple
                    id="fileInputModal"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.csv,.jpg,.jpeg,.png"                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        // Append new files to existing ones instead of replacing them
                        const newFiles = Array.from(e.target.files) as File[];
                        setSelectedFiles((prevFiles) => [...prevFiles, ...newFiles]);
                      }
                    }}
                    style={{ display: 'none' }}
                  />
                </Group>
                  
                {selectedFiles.length > 0 && (
                  <>
                    <Text size="sm" mt="sm" fw={500}>Selected files:</Text>
                    <Paper p="xs" withBorder mt="xs">
                      <Group gap="xs" style={{ flexWrap: 'wrap' }}>
                        {selectedFiles.map((file, index) => (
                          <Badge 
                            key={index} 
                            variant="light"
                            size="lg"
                            style={{ paddingRight: 5 }}
                            rightSection={
                              <ActionIcon 
                                color="red" 
                                variant="transparent" 
                                size="xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedFiles((prevFiles) => 
                                    prevFiles.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <IconX size={14} />
                              </ActionIcon>
                            }
                          >
                            {file.name}
                          </Badge>
                        ))}
                      </Group>
                    </Paper>
                  </>
                )}
              </Box>
            </>
          )}

          <Group justify="flex-end" mt="lg">
            <Button 
              variant="default" 
              onClick={() => {
                setIsAgendaItemModalOpen(false);
                resetAgendaItemForm();
                setSelectedFiles([]);
              }}
            >
              Cancel
            </Button>
            <Button onClick={editingAgendaItem ? handleUpdateAgendaItem : handleCreateAgendaItem}>
              {editingAgendaItem ? 'Update Agenda Item' : 'Add Agenda Item'}
            </Button>          </Group>
        </Stack>      </Modal>

      {/* Analysis Modal */}
      <Modal
        opened={isAnalysisModalOpen}
        onClose={() => setIsAnalysisModalOpen(false)}
        title={analyzingAgendaItem ? `Analysis: ${analyzingAgendaItem.title}` : "Analysis"}
        size="xl"
      >
        {isAnalysisLoading ? (
          <Stack align="center" py="xl">
            <Loader />
            <Text>Analyzing agenda item...</Text>
            <Text size="sm" c="dimmed" mt="xs">
              This may take a moment as we analyze the content and attachments.
            </Text>
          </Stack>
        ) : analyzingAgendaItem?.analysis ? (
          <Stack>
            <Paper p="md" withBorder>
              {analyzingAgendaItem.analysis.status === 32 ? (
                <Text>Analysis is complete. You can view it in the new tab that opened.</Text>
              ) : analyzingAgendaItem.analysis.status === 16 ? (
                <Text>Analysis is in progress. Please wait...</Text>
              ) : analyzingAgendaItem.analysis.status === 1 ? (
                <Text>Analysis has been requested and is waiting to be processed.</Text>
              ) : (
                <Text>Analysis has not been started yet.</Text>
              )}
              
              {analyzingAgendaItem.analysis.token && (
                <Text mt="md" size="sm">Analysis Token: {analyzingAgendaItem.analysis.token}</Text>
              )}
              
              {analyzingAgendaItem.analysis.time && (
                <Text mt="md" size="sm">Requested: {new Date(analyzingAgendaItem.analysis.time).toLocaleString()}</Text>
              )}
            </Paper>
            <Group justify="flex-end">
              <Button 
                onClick={() => setIsAnalysisModalOpen(false)}
              >
                Close
              </Button>
            </Group>
          </Stack>
        ) : (
          <Text c="dimmed">No analysis available for this agenda item.</Text>
        )}
      </Modal>

      {/* Attendance Sheet Manager Modal */}
      <Modal
        opened={isAttendanceSheetUploaderOpen}
        onClose={() => setIsAttendanceSheetUploaderOpen(false)}
        title="Manage Meeting Attendance Sheets"
        size="lg"
      >
        <Stack>
          <Paper withBorder p="md" mb="md">
            <Text fw={500} size="md" mb="md">Attendance Management Instructions</Text>
            <Text size="sm" mb="xs">1. Download the attendance sheet Excel file with committee member details</Text>
            <Text size="sm" mb="xs">2. Print the sheet and have attendees sign or mark their presence</Text>
            <Text size="sm" mb="xs">3. Take a photo or scan of the filled sheet</Text>
            <Text size="sm" mb="xs">4. Upload the photo/scan to automatically record attendance</Text>
          </Paper>

          <Group grow mb="md">
            <Button
              variant="outline"
              leftSection={<IconDownload size={16} />}
              onClick={() => {
                console.log(meeting)
                if (meeting && typeof meeting.committee === 'object' && meeting.committee?._id) {
                  // Use apiClient to make the request with proper auth headers
                  apiClient.get(`${import.meta.env.VITE_API_URL}/api/committee/${meeting.committee._id}/attendance-sheet`, {
                    responseType: 'blob'
                  })
                    .then(response => {
                      // Create a URL for the blob
                      const url = window.URL.createObjectURL(new Blob([response.data]));
                      // Create a temporary link element
                      const link = document.createElement('a');
                      link.href = url;
                      link.setAttribute('download', `${typeof meeting.committee === 'object' && meeting.committee.name ? meeting.committee.name : 'committee'}-attendance-sheet.xlsx`);
                      // Append to the body, trigger download, and clean up
                      document.body.appendChild(link);
                      link.click();
                      link.parentNode?.removeChild(link);
                      window.URL.revokeObjectURL(url);
                    })
                    .catch(error => {
                      console.error('Failed to download attendance sheet:', error);
                      showNotification({
                        title: 'Error',
                        message: 'Failed to download attendance sheet',
                        color: 'red',
                      });
                    });
                } else {
                  showNotification({
                    title: 'Error',
                    message: 'Committee information is missing',
                    color: 'red',
                  });
                }
              }}
            >
              Download Attendance Sheet
            </Button>
          </Group>

          {meeting?.documents?.find(doc => doc.isAttendanceSheet) ? (
            <Paper withBorder p="md" mb="md">
              <Text fw={500} size="md" mb="md">Current Uploaded Sheet</Text>
              <Box mb="md">
                <a 
                  href={`/uploads/${meeting.documents.find(doc => doc.isAttendanceSheet)?.filename}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  style={{ textDecoration: 'none' }}
                >
                  <Button 
                    variant="light"
                    leftSection={<IconFileCheck size={16} />}
                    fullWidth
                    mb="md"
                  >
                    View Uploaded Attendance Sheet
                  </Button>
                </a>

                <Text size="sm" c="dimmed" mb="md">
                  You can upload a new sheet to replace the current one.
                </Text>
              </Box>
            </Paper>
          ) : (
            <Paper withBorder p="md" mb="md">
              <Text fw={500} size="md" mb="md">Upload Attendance Sheet</Text>
              <Text size="sm" c="dimmed" mb="md">
                Upload a photo or scan of the filled attendance sheet.
              </Text>
            </Paper>
          )}

          <Box>
            {attendanceProcessing ? (
              <Group justify="center" py="xl">
                <Stack align="center">
                  <Loader size="md" />
                  <Text size="sm" mt="md">Processing attendance sheet...</Text>
                </Stack>
              </Group>
            ) : (
              <Button 
                fullWidth
                leftSection={<IconUpload size={16} />}
                onClick={() => {
                  // Open file picker for attendance sheet upload
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = 'image/*,application/pdf';
                  input.onchange = async (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file) {
                      setAttendanceProcessing(true);
                      
                      const formData = new FormData();
                      formData.append('attendanceSheet', file);
                      
                      try {
                        await apiClient.post(
                          `/api/meetings/${meetingId}/attendance-sheet`, 
                          formData
                        );
                        
                        showNotification({
                          title: 'Success',
                          message: 'Attendance sheet uploaded successfully',
                          color: 'green',
                        });
                        
                        // Refresh meeting details to get updated attendees
                        fetchMeetingDetails();
                        
                        // Close the modal after a short delay to show success
                        setTimeout(() => {
                          setIsAttendanceSheetUploaderOpen(false);
                        }, 1500);
                        
                      } catch (error) {
                        console.error('Failed to upload attendance sheet:', error);
                        showNotification({
                          title: 'Error',
                          message: 'Failed to upload attendance sheet',
                          color: 'red',
                        });
                      } finally {
                        setAttendanceProcessing(false);
                      }
                    }
                  };
                  input.click();
                }}
              >
                {meeting?.documents?.find(doc => doc.isAttendanceSheet) ? 'Upload New Attendance Sheet' : 'Upload Attendance Sheet'}
              </Button>
            )}
          </Box>
        </Stack>
      </Modal>

      {/* Invite Members Modal */}
      <InviteMembersModal 
        opened={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        meeting={meeting}
        onInvitationsSent={() => fetchMeetingDetails()}
      />
    </Container>
  );
}
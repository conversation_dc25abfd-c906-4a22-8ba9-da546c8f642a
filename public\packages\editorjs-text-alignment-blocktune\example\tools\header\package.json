{"name": "@editorjs/header", "version": "2.6.0", "keywords": ["codex editor", "header", "tool", "editor.js", "editorjs"], "description": "Head<PERSON> for Editor.js", "license": "MIT", "repository": "https://github.com/editor-js/header", "main": "./dist/bundle.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development --watch"}, "author": {"name": "CodeX", "email": "<EMAIL>"}, "devDependencies": {"@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "babel-loader": "^8.0.5", "css-loader": "^1.0.0", "raw-loader": "^3.1.0", "style-loader": "^0.21.0", "webpack": "^4.29.5", "webpack-cli": "^3.2.3"}}
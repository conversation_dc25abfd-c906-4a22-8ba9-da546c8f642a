import { useState } from "react";
import { Routes, Route, useNavigate, useLocation } from "react-router-dom";
import {
  AppShell,
  Text,
  Burger,
  useMantineTheme,
  Group,
  Button,
  NavLink,
  Stack,
  Breadcrumbs,
  Anchor,

} from "@mantine/core";
import { IconUsers, IconAddressBook, IconDashboard, IconBuildingCommunity, IconCalendar, IconChecklist, IconLibrary } from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";
import Login from "../pages/Login";
import Register from "../pages/Register";
import Dashboard from "../pages/Dashboard";
import Committees from "../pages/Committees";

import Meetings from "../pages/Meetings";
import MeetingsList from "../pages/MeetingsList";
import TasksList from "../pages/TasksList";
import MeetingDetails from "../pages/MeetingDetails";
import Directory from "../pages/Directory";
import Departments from "../pages/Departments";
import PrivateRoute from "./PrivateRoute";
import ErrorBoundary from "./ErrorBoundary";
import Schemes from "@/pages/Schemes";
import path from "path";

export default function AppLayout() {
  const theme = useMantineTheme();
  const [opened, setOpened] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };
  const navItems = [
    { label: "Dashboard", icon: IconDashboard, path: "/" },
    { label: "Committees", icon: IconUsers, path: "/committees" },
    { label: "Meetings", icon: IconCalendar, path: "/meetings" },
    { label: "Tasks", icon: IconChecklist, path: "/tasks" },
    { label: "Directory", icon: IconAddressBook, path: "/directory" },
    { label: "Departments", icon: IconBuildingCommunity, path: "/departments" },
    {label: "Schemes", icon: IconLibrary, path: "/schemes"}
  ];

  // Generate breadcrumbs based on current location
  const generateBreadcrumbs = () => {
    const path = location.pathname;
    const breadcrumbs = [];

    // Always start with Dashboard
    breadcrumbs.push(
      <Anchor key="dashboard" onClick={() => navigate('/')}>
        Dashboard
      </Anchor>
    );

    if (path === '/') {
      document.title = 'Dashboard';
      return breadcrumbs;
    }

    if (path === '/committees') {
      breadcrumbs.push(<Text key="committees">Committees</Text>);
      document.title = 'Committees';
    } else if (path === '/meetings') {
      breadcrumbs.push(<Text key="meetings">Meetings</Text>);
      document.title = 'Meetings';
    } else if (path === '/tasks') {
      breadcrumbs.push(<Text key="tasks">Tasks</Text>);
      document.title = 'Tasks';
    } else if (path === '/directory') {
      breadcrumbs.push(<Text key="directory">Directory</Text>);
      document.title = 'Directory';
    } else if (path === '/departments') {
      breadcrumbs.push(<Text key="departments">Departments</Text>);
      document.title = 'Departments';
    } else if (path.startsWith('/committee/')) {
      const pathParts = path.split('/');
      const committeeId = pathParts[2];

      breadcrumbs.push(
        <Anchor key="committees" onClick={() => navigate('/committees')}>
          Committees
        </Anchor>
      );

      if (pathParts.length >= 4 && pathParts[3] === 'meetings') {
        breadcrumbs.push(<Text key="committee">Committee</Text>);

        if (pathParts.length === 4) {
          breadcrumbs.push(<Text key="meetings">Meetings</Text>);
        } else if (pathParts.length >= 5) {
          breadcrumbs.push(
            <Anchor key="meetings" onClick={() => navigate(`/committee/${committeeId}/meetings`)}>
              Meetings
            </Anchor>
          );
          breadcrumbs.push(<Text key="meeting">Meeting</Text>);
        }
      }
    } else if(path.startsWith('/departments/')) {
      // const pathParts = path.split('/');
      // const departmentId = pathParts[2];  // Todo Get the department name from API Later || do Something else for name

      breadcrumbs.push(
        <Anchor key="departments" onClick={() => navigate('/departments')}>
          Departments
        </Anchor>
      );

      breadcrumbs.push(<Text key="department">Department</Text>);
    }

    return breadcrumbs;
  };

  // Check if current route is a public route (login/register)
  const isPublicRoute = location.pathname === '/login' || location.pathname === '/register';

  // If it's a public route, render without AppShell
  if (isPublicRoute) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
      </Routes>
    );
  }

  // For authenticated routes, render with AppShell
  return (
    <AppShell
      layout="alt"
      padding="md"
      navbar={{
        width: { base: 200, lg: 300 },
        breakpoint: "sm",
        collapsed: { mobile: !opened },
      }}
      header={{
        height: 70,
      }}
    >
      <AppShell.Navbar p="md">
        <Stack>
          <Text size="lg" fw={600} mb="md">
            DCMAMP
          </Text>

          {navItems.map((item) => (
            <NavLink
              key={item.path}
              label={item.label}
              leftSection={<item.icon size={16} />}
              onClick={() => navigate(item.path)}
              active={location.pathname === item.path}
            />
          ))}
        </Stack>
      </AppShell.Navbar>

      <AppShell.Header>
        <Group h="100%" px="md">
          <Burger
            hiddenFrom="sm"
            opened={opened}
            onClick={() => setOpened((o) => !o)}
            size="sm"
            color={theme.colors.gray[6]}
          />

          <Group justify="space-between" style={{ flex: 1 }}>
            <Breadcrumbs>
              {generateBreadcrumbs()}
            </Breadcrumbs>
            {user && (
              <Group>
                <Text>{user.email}</Text>
                <Button variant="light" onClick={handleLogout}>
                  Logout
                </Button>
              </Group>
            )}
          </Group>
        </Group>
      </AppShell.Header>      <AppShell.Main>
        <ErrorBoundary>
          <Routes>
            <Route path="/" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
            <Route path="/committees" element={<PrivateRoute><Committees /></PrivateRoute>} />
            <Route path="/meetings" element={<PrivateRoute><MeetingsList /></PrivateRoute>} />
            <Route path="/tasks" element={<PrivateRoute><TasksList /></PrivateRoute>} />
            <Route path="/committee/:committeeId/meetings" element={<PrivateRoute><Meetings /></PrivateRoute>} />
            <Route path="/committee/:committeeId/meetings/:meetingId" element={<PrivateRoute><MeetingDetails /></PrivateRoute>} />
            <Route path="/directory" element={<PrivateRoute><Directory /></PrivateRoute>} />
            <Route path="/departments" element={<PrivateRoute><Departments /></PrivateRoute>} />
            <Route path="/schemes" element={<PrivateRoute><Schemes /></PrivateRoute>} />
            <Route path="/departments/:departmentId" element={<PrivateRoute><Directory /></PrivateRoute>} />
          </Routes>
        </ErrorBoundary>
      </AppShell.Main>
    </AppShell>
  );
}

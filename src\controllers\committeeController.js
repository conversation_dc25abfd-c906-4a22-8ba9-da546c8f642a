import dayjs from "dayjs";
import Committee from "../models/committee.js";
import Meeting from "../models/meeting.js";
import { handleNotificationForSingleCommittee } from "./notification.js";

export const getCommittees = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      sortBy = 'createdAt', 
      sortOrder = 'desc' 
    } = req.query;

    // Build search filter
    const searchFilter = search ? {
      $or: [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { meetingFrequency: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Get total count for pagination
    const total = await Committee.countDocuments(searchFilter);

    // Get paginated results
    const data = await Committee.find(searchFilter)
      .populate("members")
      .populate("assistant")
      .populate("createdBy")
      .populate("department")
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    res.json({
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getCommittee = async (req, res) => {
  try {
    const data = await Committee.findById(req.params.id)
      .populate("members")
      .populate("assistant")
      .populate("createdBy")
      .populate("department");
    res.json(data);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const createCommittee = async (req, res) => {
  try {
    const committeeData = { ...req.body, createdBy: req.user._id };
    
    // If no department is selected but there's an assistant, use the assistant's department as default
    if (!committeeData.department && committeeData.assistant) {
      try {
        // Import the Directory model
        const Directory = (await import('../models/directory.js')).default;
        
        // Find the assistant's directory entry with department populated
        const assistant = await Directory.findById(committeeData.assistant)
          .populate('department')
          .exec();
          
        // If assistant exists and has a department, use it as the committee's department
        if (assistant && assistant.department) {
          committeeData.department = assistant.department;
          console.log(`Using assistant's department (${assistant.department.name}) as default for committee`);
        }
      } catch (err) {
        console.error("Error getting assistant's department:", err.message);
        // Continue creating the committee without a department if there's an error
      }
    }
    
    // Set startDate based on meeting frequency
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    switch (committeeData.meetingFrequency) {
      case 'monthly':
        // First day of current month
        committeeData.startDate = new Date(currentYear, currentMonth, 1);
        break;
      case 'quarterly':
        // First day of current quarter
        const currentQuarter = Math.floor(currentMonth / 3);
        committeeData.startDate = new Date(currentYear, currentQuarter * 3, 1);
        break;
      case 'halfyearly':
        // First day of current half-year
        const currentHalf = Math.floor(currentMonth / 6);
        committeeData.startDate = new Date(currentYear, currentHalf * 6, 1);
        break;
      case 'yearly':
        // First day of current year
        committeeData.startDate = new Date(currentYear, 0, 1);
        break;
      case 'biweekly':
        // Current date aligned to start of a week (Monday)
        const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday
        const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Calculate days to subtract to get to Monday
        committeeData.startDate = new Date(now);
        committeeData.startDate.setDate(now.getDate() - daysToSubtract);
        break;
      default:
        // Default to today
        committeeData.startDate = new Date();
    }
    
    const committee = new Committee(committeeData);
    const saved = await committee.save();
    res.status(201).json(saved);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const updateCommittee = async (req, res) => {
  try {
    const committeeData = { ...req.body };
    
    // Only update startDate if meeting frequency has changed
    if (committeeData.meetingFrequency) {
      // Set startDate based on meeting frequency
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth();
      
      switch (committeeData.meetingFrequency) {
        case 'monthly':
          // First day of current month
          committeeData.startDate = new Date(currentYear, currentMonth, 1);
          break;
        case 'quarterly':
          // First day of current quarter
          const currentQuarter = Math.floor(currentMonth / 3);
          committeeData.startDate = new Date(currentYear, currentQuarter * 3, 1);
          break;
        case 'halfyearly':
          // First day of current half-year
          const currentHalf = Math.floor(currentMonth / 6);
          committeeData.startDate = new Date(currentYear, currentHalf * 6, 1);
          break;
        case 'yearly':
          // First day of current year
          committeeData.startDate = new Date(currentYear, 0, 1);
          break;
        case 'biweekly':
          // Current date aligned to start of a week (Monday)
          const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday
          const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Calculate days to subtract to get to Monday
          committeeData.startDate = new Date(now);
          committeeData.startDate.setDate(now.getDate() - daysToSubtract);
          break;
        default:
          // Default to today
          committeeData.startDate = new Date();
      }
    }
    
    const updated = await Committee.findByIdAndUpdate(req.params.id, committeeData, { new: true });
    res.json(updated);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const deleteCommittee = async (req, res) => {
  try {
    await Committee.findByIdAndDelete(req.params.id);
    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getCommitteeMeetings = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      sortBy = 'startDate', 
      sortOrder = 'desc' 
    } = req.query;

    // Build search filter
    const searchFilter = {
      committee: id,
      ...(search && {
        $or: [
          { title: { $regex: search, $options: 'i' } },
          { agenda: { $regex: search, $options: 'i' } }
        ]
      })
    };

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Get total count for pagination
    const total = await Meeting.countDocuments(searchFilter);

    const meetings = await Meeting.find(searchFilter)
      .populate("committee")
      .populate({
        path: "tasks",
        select: "_id" // Only get task IDs for counting
      })
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Transform the data to include task count
    const meetingsWithTaskCount = meetings.map(meeting => {
      const { tasks, ...meetingData } = meeting.toObject();
      return {
        ...meetingData,
        taskCount: tasks?.length || 0
      };
    });

    res.json({
      data: meetingsWithTaskCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const createCommitteeMeeting = async (req, res) => {
  try {
    const { id } = req.params; // committee ID

    const meeting = new Meeting({
      ...req.body,
      committee: id // Set the committee from the URL parameter
    });

    const saved = await meeting.save();
    handleNotificationForSingleCommittee(saved.committee._id, dayjs(saved.startDate)); 
    // Check if this is the first meeting for this committee
    const meetingCount = await Meeting.countDocuments({
      committee: id,
      _id: { $ne: saved._id } // Exclude the meeting we just created
    });
    
    // If this is not the first meeting, automatically create an agenda item for MoM discussion
    if (meetingCount > 0) {
      const AgendaItem = (await import('../models/agendaItem.js')).default;
      
      // Create a MoM discussion agenda item as the first item (order 0)
      const momAgendaItem = new AgendaItem({
        title: "Agenda Item 1",
        description: "Confirmation on Minutes of Meeting (MoM) of previous meeting",
        meeting: saved._id,
        order: 0,
        createdBy: req.user ? req.user.id : null
      });
      
      await momAgendaItem.save();
      console.log("Created MoM discussion agenda item for meeting:", saved._id);
      
      // Reorder existing agenda items if necessary
      const reorderAgendaItems = async (meetingId) => {
        try {
          const AgendaItem = (await import('../models/agendaItem.js')).default;
          
          // Get all agenda items for this meeting except the MoM item (which is at order 0)
          const existingItems = await AgendaItem.find({
            meeting: meetingId,
            order: { $gt: 0 }
          }).sort({ order: 1 });
          
          // If there are existing items, make sure they're properly ordered starting from 1
          if (existingItems.length > 0) {
            const bulkOperations = existingItems.map((item, index) => ({
              updateOne: {
                filter: { _id: item._id },
                update: { $set: { order: index + 1 } }
              }
            }));
            
            if (bulkOperations.length > 0) {
              await AgendaItem.bulkWrite(bulkOperations);
              console.log(`Reordered ${bulkOperations.length} agenda items after MoM insertion`);
            }
          }
        } catch (error) {
          console.error("Error reordering agenda items:", error);
        }
      };
      await reorderAgendaItems(saved._id);
    }

    res.status(201).json(saved);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const deleteCommitteeMeeting = async (req, res) => {
  try {
    const { id, meetingId } = req.params; // committee ID and meeting ID

    // Verify the meeting belongs to this committee before deleting
    const meeting = await Meeting.findOne({ _id: meetingId, committee: id });
    if (!meeting) {
      return res.status(404).json({ error: "Meeting not found or doesn't belong to this committee" });
    }

    const updated = await Meeting.findByIdAndDelete(meetingId);
    handleNotificationForSingleCommittee(updated.committee, dayjs(updated.startDate));
    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get committee members for attendee selection
export const getCommitteeMembers = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Getting members for committee ID: ${id}`);
    
    const committee = await Committee.findById(id)
      .populate({
        path: "members",
        select: "name designation email"
      });
    
    if (!committee) {
      console.log(`Committee not found with ID: ${id}`);
      return res.status(404).json({ error: "Committee not found" });
    }
    
    console.log(`Found committee: ${committee.name}, Members count: ${committee.members?.length || 0}`);
    res.json(committee.members || []);
  } catch (error) {
    console.error(`Error getting committee members: ${error.message}`);
    res.status(400).json({ error: error.message });
  }
};

{"name": "pptxgenjs", "version": "3.12.0", "author": {"name": "<PERSON>", "url": "https://github.com/gitbrent/"}, "description": "Create JavaScript PowerPoint Presentations", "homepage": "https://gitbrent.github.io/PptxGenJS/", "license": "MIT", "main": "dist/pptxgen.cjs.js", "module": "dist/pptxgen.es.js", "files": ["dist", "types"], "types": "types", "scripts": {"build": "rollup -c", "start": "gulp", "ship": "gulp ship", "defs": "gulp reactTestDefs", "watch": "rollup -cw", "doctoc": "./node_modules/doctoc/doctoc.js README.md"}, "browser": {"express": false, "fs": false, "https": false, "image-size": false, "os": false, "path": false}, "dependencies": {"@types/node": "^18.7.3", "https": "^1.0.0", "image-size": "^1.0.0", "jszip": "^3.7.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.2", "@rollup/plugin-node-resolve": "^13.0.5", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.0", "doctoc": "^2.0.1", "eslint": "^8.22.0", "eslint-config-standard-with-typescript": "^22.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.4", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.30.1", "express": "^4.17.1", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-delete-lines": "0.0.7", "gulp-ignore": "^3.0.0", "gulp-insert": "^0.5.0", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.2", "rollup": "^2.57.0", "rollup-plugin-typescript2": "^0.31.2", "tslib": "^2.3.1", "typescript": "^4.7.4"}, "repository": {"type": "git", "url": "git+https://github.com/gitbrent/PptxGenJS.git"}, "keywords": ["html-to-powerpoint", "javascript-create-powerpoint", "javascript-create-pptx", "javascript-generate-pptx", "javascript-powerpoint", "javascript-powerpoint-charts", "javascript-pptx", "js-create-powerpoint", "js-create-pptx", "js-generate-powerpoint", "js-powerpoint", "js-powerpoint-library", "js-powerpoint-pptx", "react-powerpoint", "typescript-powerpoint"], "bugs": {"url": "https://github.com/gitbrent/PptxGenJS/issues"}}
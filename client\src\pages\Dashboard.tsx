import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { apiClient } from "../config/axios";
import {
  Button,
  Container,
  Text,
  SimpleGrid,
  Group,
  Stack,
  Badge,
  Progress,
  Title,
  Card,
  Grid,
  Skeleton,
} from "@mantine/core";
import { notifications } from '@mantine/notifications';
import {
  IconCalendar,
  IconList,
  IconTrendingUp,
  IconTrendingDown,
  IconClock,
  IconChevronRight,
  IconAlertTriangle,
  IconBrightnessAuto,
} from '@tabler/icons-react';
import { Task, Meeting, Committee } from "../types";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isBetween from 'dayjs/plugin/isBetween';
dayjs.extend(isBetween);
dayjs.extend(quarterOfYear);


interface CommitteePerformance {
  committee: Committee;
  totalTasks: number;
  completedTasks: number;
  completionRate: number;
  upcomingMeetings: number;
}

interface DashboardStats {
  total: number;
  completed: number;
  pending: number;
  highPriority: number;
  allMeetings: Meeting[];
  upcomingMeetings: UpcomingCommitteeMeeting[];
  missedMeetings: UpcomingCommitteeMeeting[];
  activeCommittees: number;
  tasksDueThisWeek: Task[];
  overdueTasks: Task[];
  bestPerformingCommittee: CommitteePerformance | null;
  worstPerformingCommittee: CommitteePerformance | null;
  allCommitteePerformance: CommitteePerformance[];
}

type UpcomingCommitteeMeeting = {
  meeting?: {
    _id: string,
    name: string,
  },
  committee: {
    _id: string,
    name: string,
  },
  startDate: string,
}

function getColorForUpcomingMeeting(type: "green" | "yellow" | "red" | undefined) {
  if (type === "green") {
    return ""
  }
  if (type === "yellow") {
    return "#C49100"
  }
  if (type === "red") {
    return "#D32F2F"
  }
  return ""
}

export default function Dashboard() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  const [stats, setStats] = useState<DashboardStats>({
    total: 0,
    completed: 0,
    pending: 0,
    highPriority: 0,
    allMeetings: [],
    upcomingMeetings: [],
    missedMeetings: [],
    activeCommittees: 0,
    tasksDueThisWeek: [],
    overdueTasks: [],
    bestPerformingCommittee: null,
    worstPerformingCommittee: null,
    allCommitteePerformance: [],
  });


  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {      // Fetch committees first - get all committees for dashboard
      const committeesRes = await apiClient.get<{ data: Committee[]; total: number; page: number; limit: number; }>("/api/committee", {
        params: { limit: 1000 } // Request a large number to ensure we get all committees for dashboard calculations
      });
      const committees: Committee[] = committeesRes.data.data;

      // Fetch meetings and tasks for each committee
      const allTasks: Task[] = [];
      const allMeetings: Meeting[] = [];
      const committeePerformance: CommitteePerformance[] = [];
      const committeesForUpcomingMeeting: UpcomingCommitteeMeeting[] = [];
      for (const committee of committees) {
        try {
          const meetingsRes = await apiClient.get(`/api/committee/${committee._id}/meetings`, {
            params: { limit: 1000 } // Get all meetings for dashboard stats
          });
          const committeeMeetings: Meeting[] = meetingsRes.data.data || meetingsRes.data;
          const enhancedMeetings = committeeMeetings.map(meeting => ({
            ...meeting,
            committee
          }));
          
          allMeetings.push(...enhancedMeetings);
          
          if (committeeMeetings.length > 0) {
            committeeMeetings.sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime());
            for (const meeting of committeeMeetings) {
              const tasksRes = await apiClient.get<{ data: Task[] }>(`/api/meetings/${meeting._id}/tasks`);
              const meetingTasks = tasksRes.data.data || tasksRes.data;
              const enhancedTasks = meetingTasks.map<Task>(task => ({
                ...task,
                committeeId: committee._id,
                committeeName: committee.name,
              }));
              
              allTasks.push(...enhancedTasks);
            }
          }
          
        } catch (error) {
          console.error(`Failed to fetch data for committee ${committee._id}:`, error);
        }
      }
      const total = allTasks.length;
      const completed = allTasks.filter(t => t.status === "completed").length;
      const pending = total - completed;
      const highPriority = allTasks.filter(t => t.priority === "high").length;

      // Get upcoming meetings (next 30 days)
      const now = new Date();

      let upcomingMeeting: UpcomingCommitteeMeeting[] = [];
      let missedMeetings: UpcomingCommitteeMeeting[] = [];
      try {
        const upcomingMeetings = await apiClient.get(`/api/dashboard/upcomingMeetings`);
        upcomingMeetings.data.upcomingMeetings.map((ele: any) => {
          upcomingMeeting.push({
            committee: {
              _id: ele.committee._id,
              name: ele.committee.title,
            },
            meeting: {
              name: ele.title,
              _id: ele._id,
            },
            startDate: ele.startDate,
          });
        });
        upcomingMeetings.data.autoGenerated.map((ele: any) => {
          upcomingMeeting.push({
            committee: {
              _id: ele._id,
              name: ele.name,
            },
            startDate: ele.startDate,
            meeting: {
              _id: "",
              name: "Meeting Due",
            }
          });
        });
      } catch (error) {}
      try {
        const missedMeetingsFetchData = await apiClient.get(`/api/dashboard/missedMeetings`);
        console.log("Missed Meeting: ", missedMeetingsFetchData.data);
        missedMeetingsFetchData.data.map((ele: any) => {
          missedMeetings.push({
            committee: {
              _id: ele._id,
              name: ele.name,
            },
            startDate: ele.startDate,
            meeting: {
              _id: "",
              name: "Missing Meeting",
            }
          });
        });
      } catch (error) {}
      const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      const tasksDueThisWeek = allTasks
        .filter(t => {
          if (!t.deadline) return false;
          const deadline = new Date(t.deadline);
          return deadline >= now && deadline <= nextWeek && t.status === 'pending';
        });

      // Get overdue tasks
      const overdueTasks = allTasks.filter(t => {
        if (!t.deadline) return false;
        const deadline = new Date(t.deadline);
        return deadline < now && t.status === 'pending';
      });
      // Find best and worst performing committees
      const committeesWithTasks = committeePerformance.filter(c => c.totalTasks > 0);
      const bestPerforming = committeesWithTasks.length > 0
        ? committeesWithTasks.reduce((best, current) =>
          current.completionRate > best.completionRate ? current : best
        )
        : null;

      const worstPerforming = committeesWithTasks.length > 0
        ? committeesWithTasks.reduce((worst, current) =>
          current.completionRate < worst.completionRate ? current : worst
        )
        : null;
      setStats({
        total,
        completed,
        pending,
        highPriority,
        allMeetings,
        upcomingMeetings: upcomingMeeting,
        missedMeetings: missedMeetings,
        activeCommittees: committees.length,
        tasksDueThisWeek,
        overdueTasks,
        bestPerformingCommittee: bestPerforming,
        worstPerformingCommittee: worstPerforming,
        allCommitteePerformance: committeePerformance,
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load dashboard data. Please try again later.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };




  const handleViewCommittee = (committeeId: string) => {
    navigate(`/committee/${committeeId}/meetings`);
  };

  const handleViewMeeting = (committeeId: string, meetingId: string) => {
    navigate(`/committee/${committeeId}/meetings/${meetingId}`);
  }; const handleViewTask = (task: Task) => {
    // Find meeting that contains this task
    if (task.meeting && task.committeeId) {
      navigate(`/committee/${task.committeeId}/meetings/${task.meeting}`);
    }
  };



  if (loading) {
    return (
      <Container size="xl" py="xl">
        {/* Dashboard Title Skeleton */}
        <Group justify="space-between" mb="xl">
          <div>
            <Skeleton height={36} width={500} mb={8} />
            <Skeleton height={16} width={300} />
          </div>
        </Group>

        {/* Key Metrics Skeleton */}
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={`metric-skeleton-${i}`} withBorder p="lg">
              <Group justify="space-between">
                <div>
                  <Skeleton height={14} width={80} mb={10} />
                  <Skeleton height={28} width={40} />
                </div>
                <Skeleton height={32} width={32} circle />
              </Group>
              <Skeleton height={8} mt="md" />
              <Skeleton height={14} width="40%" mt="xs" />
            </Card>
          ))}
        </SimpleGrid>
        
        {/* Upcoming Meetings & Tasks Skeleton */}
        <Grid gutter="md" mb="xl">
          {Array.from({ length: 2 }).map((_, i) => (
            <Grid.Col key={`section-skeleton-${i}`} span={{ base: 12, md: 6 }}>
              <Card withBorder p="lg" h={400}>
                <Group justify="space-between" mb="md">
                  <Skeleton height={24} width={180} />
                  <Skeleton height={24} width={24} />
                </Group>
                <Stack gap="md">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <Card key={`item-skeleton-${i}-${j}`} p="md" withBorder radius="sm">
                      <Group justify="space-between" wrap="nowrap">
                        <div style={{ width: '60%' }}>
                          <Skeleton height={16} width="80%" mb={8} />
                          <Skeleton height={14} width="50%" />
                        </div>
                        <div style={{ textAlign: 'right', width: '30%' }}>
                          <Skeleton height={16} width="100%" mb={8} />
                          <Skeleton height={12} width="70%" />
                        </div>
                      </Group>
                    </Card>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        {/* Committee Performance Skeleton */}
        <SimpleGrid cols={{ base: 1, lg: 2 }} spacing="md" mb="xl">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={`performance-skeleton-${i}`} withBorder p="lg">
              <Group justify="space-between" mb="md">
                <div>
                  <Skeleton height={14} width={120} mb={8} />
                  <Skeleton height={20} width={180} />
                </div>
                <Skeleton height={24} width={24} circle />
              </Group>
              <Group justify="space-between" mb="xs">
                <Skeleton height={14} width={100} />
                <Skeleton height={14} width={60} />
              </Group>
              <Skeleton height={8} mb="md" />
              <Group justify="space-between">
                <Skeleton height={12} width={80} />
                <Skeleton height={24} width={60} />
              </Group>
            </Card>
          ))}
        </SimpleGrid>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="xl">
        <div>
          <Title order={1}>District Committee Meeting Automation & Monitoring Platform</Title>
          <Text c="dimmed">Overview of your committees, meetings, and tasks</Text>
        </div>
      </Group>


      {/* Key Metrics */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <Card withBorder p="lg" onClick={() => navigate('/tasks')} style={{ cursor: 'pointer' }}>
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Tasks</Text>
              <Text size="xl" fw={700}>{stats.total}</Text>
            </div>
            <IconList size={32} stroke={1.5} />
          </Group>
          <Progress
            value={stats.total > 0 ? (stats.completed / stats.total) * 100 : 0}
            mt="md"
            color="blue"
          />
          <Text size="xs" c="dimmed" mt="xs">
            {stats.completed} completed, {stats.pending} pending
          </Text>
        </Card>

        <Card withBorder p="lg" onClick={() => navigate('/meetings')} style={{ cursor: 'pointer' }}>
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Upcoming Meetings</Text>
              <Text size="xl" fw={700}>{stats.upcomingMeetings.length}</Text>
            </div>
            <IconCalendar size={32} stroke={1.5} />
          </Group>
          <Text size="xs" c="dimmed" mt="md">Next 30 days</Text>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Due This Week</Text>
              <Text size="xl" fw={700} c={stats.tasksDueThisWeek.length > 0 ? "orange" : "gray"}>
                {stats.tasksDueThisWeek.length}
              </Text>
            </div>
            <IconClock size={32} stroke={1.5} />
          </Group>
          <Text size="xs" c="dimmed" mt="md">Tasks with deadlines</Text>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Overdue</Text>
              <Text size="xl" fw={700} c={stats.overdueTasks.length > 0 ? "red" : "gray"}>
                {stats.overdueTasks.length}
              </Text>
            </div>
            <IconAlertTriangle size={32} stroke={1.5} />
          </Group>
          <Text size="xs" c="dimmed" mt="md">Past deadline</Text>
        </Card>
      </SimpleGrid>

      {/* Upcoming Meetings & Tasks Due This Week */}
      <Grid gutter="md" mb="xl">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder p="lg" h={400}>
            <Group justify="space-between" mb="md">
              <Title order={3}>Upcoming Meetings</Title>
              <IconCalendar size={24} stroke={1.5} />            </Group>
            <Stack h="calc(100% - 60px)" style={{ overflow: 'auto' }}>
              {stats.upcomingMeetings.length === 0 ? (
                <Stack align="center" justify="center" h={200} gap="md">
                  <IconCalendar size={48} stroke={1.5} color="var(--mantine-color-gray-3)" />
                  <div style={{ textAlign: 'center' }}>
                    <Text size="lg" fw={500} c="dimmed">No Upcoming Meetings</Text>
                    <Text size="sm" c="dimmed" mt={4}>
                      There are no meetings scheduled at this time
                    </Text>
                  </div>
                </Stack>
              ) : (
                <Stack gap="md">
                  {stats.upcomingMeetings.slice(0, 50).map((meeting) => (
                  <Card
                    key={meeting.meeting?._id + meeting.committee._id?.toString()}
                    p="md"
                    withBorder
                    radius="sm"
                    bg="gray.0"
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleViewMeeting(
                      typeof meeting.committee === 'string' ? meeting.committee : meeting.committee?._id,
                      meeting.meeting?._id as string
                    )}
                  >
                    <Group justify="space-between" wrap="nowrap">
                      <div>
                        <Text style={{ display: "flex", gap: '4px' }} fw={500}>
                          {meeting.meeting?.name}
                          {meeting.meeting?._id == "" ?
                            <IconBrightnessAuto color={getColorForUpcomingMeeting("yellow")} />
                            :
                            <></>
                          }
                        </Text>
                        <Text size="sm" c="dimmed">
                          {typeof meeting.committee === 'string' ? 'Committee' : meeting.committee?.name}
                        </Text>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <Text size="sm" fw={500}>
                          {new Date(meeting.startDate).toLocaleDateString()}
                        </Text>
                      </div>
                    </Group>
                  </Card>
                ))}
                  {stats.upcomingMeetings.length > 5 && (
                    <Text size="sm" c="dimmed" ta="center">
                      And {stats.upcomingMeetings.length - 5} more meetings...
                    </Text>
                  )}
                </Stack>
              )}
            </Stack>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder p="lg" h={400}>
            <Group justify="space-between" mb="md">
              <Title order={3}>Missed Meetings</Title>
              <IconCalendar size={24} stroke={1.5} />
            </Group>
            <Stack h="calc(100% - 60px)" style={{ overflow: 'auto' }}>
              {stats.missedMeetings.length === 0 ? (
                <Stack align="center" justify="center" h={200} gap="md">
                  <IconCalendar size={48} stroke={1.5} color="var(--mantine-color-gray-3)" />
                  <div style={{ textAlign: 'center' }}>
                    <Text size="lg" fw={500} c="dimmed">No Missed Meetings</Text>
                    <Text size="sm" c="dimmed" mt={4}>
                      There are no meetings missed
                    </Text>
                  </div>
                </Stack>
              ) : (
                <Stack gap="md">
                  {stats.missedMeetings.slice(0, 50).map((meeting) => (
                  <Card
                    key={meeting.meeting?._id + meeting.committee._id?.toString()}
                    p="md"
                    withBorder
                    radius="sm"
                    bg="gray.0"
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleViewMeeting(
                      meeting.committee._id,
                      meeting.meeting?._id ?? "",
                    )}
                  >
                    <Group justify="space-between" wrap="nowrap">
                      <div>
                        <Text style={{ display: "flex", gap: '4px' }} fw={500}>
                          {meeting.meeting?.name}
                          {meeting.meeting?._id == "" ?
                            <IconBrightnessAuto color={getColorForUpcomingMeeting("red")} />
                            :
                            <></>
                          }
                        </Text>
                        <Text size="sm" c="dimmed">
                          {typeof meeting.committee === 'string' ? 'Committee' : meeting.committee?.name}
                        </Text>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <Text size="sm" fw={500}>
                          {new Date(meeting.startDate).toLocaleDateString()}
                        </Text>
                      </div>
                    </Group>
                  </Card>
                ))}
                  {stats.missedMeetings.length > 5 && (
                    <Text size="sm" c="dimmed" ta="center">
                      And {stats.missedMeetings.length - 5} more meetings...
                    </Text>
                  )}
                </Stack>
              )}
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      <Grid gutter="md" mb="xl">
        <Grid.Col span={{ base: 12, md: 12 }}>
          <Card withBorder p="lg" h={400}>
            <Group justify="space-between" mb="md">
              <Title order={3}>Tasks Due This Week</Title>
              <IconClock size={24} stroke={1.5} />
            </Group>
            <Stack h="calc(100% - 60px)" style={{ overflow: 'auto' }}>
              {stats.tasksDueThisWeek.length === 0 ? (
                <Stack align="center" justify="center" h={200} gap="md">
                  <IconClock size={48} stroke={1.5} color="var(--mantine-color-gray-3)" />
                  <div style={{ textAlign: 'center' }}>
                    <Text size="lg" fw={500} c="dimmed">No Tasks Due</Text>
                    <Text size="sm" c="dimmed" mt={4}>
                      There are no tasks due this week
                    </Text>
                  </div>
                </Stack>
              ) : (
                <Stack gap="md" style={{ paddingRight: '8px' }}>
                  {stats.tasksDueThisWeek.map((task) => {
                    const daysUntilDue = Math.ceil((new Date(task.deadline!).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                    return (
                      <Card
                        key={task._id}
                        p="md"
                        withBorder
                        radius="sm"
                        bg="orange.0"
                        style={{ cursor: 'pointer' }}
                        onClick={() => handleViewTask(task)}
                      >
                        <Group justify="space-between" wrap="nowrap">
                          <div>
                            <Text fw={500}>{task.title}</Text>
                            <Group gap="xs">
                              <Badge
                                color={task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'blue'}
                                size="sm"
                              >
                                {task.priority}
                              </Badge>
                              <Text size="sm" c="dimmed">
                                {task.committeeName}
                              </Text>
                            </Group>
                          </div>
                          <div style={{ textAlign: 'right', whiteSpace: "nowrap" }}>
                            <Text size="sm" fw={500} c={daysUntilDue <= 1 ? "red" : "orange"}>
                              {daysUntilDue === 0 ? 'Due Today' :
                                daysUntilDue === 1 ? 'Due Tomorrow' :
                                  `${daysUntilDue} days left`}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {new Date(task.deadline!).toLocaleDateString()}
                            </Text>
                          </div>
                        </Group>
                      </Card>
                    );
                  })}
                </Stack>
              )}
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Committee Performance */}
      <SimpleGrid cols={{ base: 1, lg: 2 }} spacing="md" mb="xl">
        {stats.bestPerformingCommittee && (
          <Card withBorder p="lg">
            <Group justify="space-between" mb="md">
              <div>
                <Text size="sm" c="dimmed" tt="uppercase" fw={700}>Best Performing</Text>
                <Text size="lg" fw={600}>{stats.bestPerformingCommittee.committee.name}</Text>
              </div>
              <IconTrendingUp size={24} color="green" />
            </Group>
            <Group justify="space-between" mb="xs">
              <Text size="sm">Completion Rate</Text>
              <Text size="sm" fw={600} c="green">
                {stats.bestPerformingCommittee.completionRate.toFixed(1)}%
              </Text>
            </Group>
            <Progress value={stats.bestPerformingCommittee.completionRate} color="green" mb="md" />
            <Group justify="space-between">
              <Text size="xs" c="dimmed">
                {stats.bestPerformingCommittee.completedTasks}/{stats.bestPerformingCommittee.totalTasks} tasks
              </Text>
              <Button
                variant="light"
                size="xs"
                rightSection={<IconChevronRight size={14} />}
                onClick={() => handleViewCommittee(stats.bestPerformingCommittee!.committee._id)}
              >
                View
              </Button>
            </Group>
          </Card>
        )}

        {stats.worstPerformingCommittee && (
          <Card withBorder p="lg">
            <Group justify="space-between" mb="md">
              <div>
                <Text size="sm" c="dimmed" tt="uppercase" fw={700}>Needs Attention</Text>
                <Text size="lg" fw={600}>{stats.worstPerformingCommittee.committee.name}</Text>
              </div>
              <IconTrendingDown size={24} color="red" />
            </Group>
            <Group justify="space-between" mb="xs">
              <Text size="sm">Completion Rate</Text>
              <Text size="sm" fw={600} c="red">
                {stats.worstPerformingCommittee.completionRate.toFixed(1)}%
              </Text>
            </Group>
            <Progress value={stats.worstPerformingCommittee.completionRate} color="red" mb="md" />
            <Group justify="space-between">
              <Text size="xs" c="dimmed">
                {stats.worstPerformingCommittee.completedTasks}/{stats.worstPerformingCommittee.totalTasks} tasks
              </Text>
              <Button
                variant="light"
                size="xs"
                rightSection={<IconChevronRight size={14} />}
                onClick={() => handleViewCommittee(stats.worstPerformingCommittee!.committee._id)}
              >
                View
              </Button>
            </Group>
          </Card>
        )}
      </SimpleGrid>



      {/* Overdue Tasks Alert */}
      {stats.overdueTasks.length > 0 && (
        <Card withBorder p="lg" mb="xl" style={{ borderColor: 'var(--mantine-color-red-4)' }}>
          <Group justify="space-between" mb="md">
            <Group>
              <IconAlertTriangle size={24} color="red" />
              <Title order={3}>Overdue Tasks</Title>
            </Group>
          </Group>
          <Stack gap="md">
            {stats.overdueTasks.slice(0, 50).map((task) => {
              const daysOverdue = Math.ceil((new Date().getTime() - new Date(task.deadline!).getTime()) / (1000 * 60 * 60 * 24));
              return (
                <Card
                  key={task._id}
                  p="md"
                  withBorder
                  radius="sm"
                  bg="red.0"
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleViewTask(task)}
                >
                  <Group justify="space-between" wrap="nowrap">
                    <div>
                      <Text fw={500}>{task.title}</Text>
                      <Group gap="xs">
                        <Badge
                          color={task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'blue'}
                          size="sm"
                        >
                          {task.priority}
                        </Badge>
                        <Text size="sm" c="dimmed">
                          {task.committeeName}
                        </Text>
                      </Group>
                    </div>
                    <div style={{ textAlign: 'right', whiteSpace: "nowrap" }}>
                      <Text size="sm" fw={500} c="red">
                        {daysOverdue === 1 ? '1 day overdue' : `${daysOverdue} days overdue`}
                      </Text>
                      <Text size="xs" c="dimmed">
                        Due: {new Date(task.deadline!).toLocaleDateString()}
                      </Text>
                    </div>
                  </Group>
                </Card>
              );
            })}
            {stats.overdueTasks.length > 5 && (
              <Text size="sm" c="dimmed" ta="center">
                And {stats.overdueTasks.length - 5} more overdue tasks...
              </Text>
            )}
          </Stack>
        </Card>
      )}
    </Container>
  );
}

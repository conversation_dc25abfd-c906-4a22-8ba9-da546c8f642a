# Data Seeding Scripts

This directory contains scripts to populate your database with sample data for testing and development purposes.

## Available Scripts

### `seed-sample-data.js`

Creates sample departments and directory members/contacts for the task scheduler application.

**What it creates:**
- A system user (if not already exists) for data ownership
- 10 sample departments (HR, IT, Finance, Marketing, Operations, R&D, QA, Sales, Legal, Customer Support)
- 30+ directory members with realistic contact information
- Proper relationships between departments and members
- Department heads assignment

**Usage:**
```bash
npm run seed-sample-data
```

**Data Created:**

#### Departments:
- **Human Resources (HR)** - Employee relations and recruitment
- **Information Technology (IT)** - Technology infrastructure and development
- **Finance and Accounting (FIN)** - Financial planning and accounting
- **Marketing and Communications (MKT)** - Brand promotion and communications
- **Operations and Management (OPS)** - Daily operations and project management
- **Research and Development (R&D)** - Research and innovation
- **Quality Assurance (QA)** - Quality standards and compliance
- **Sales and Business Development (SALES)** - Sales and client relationships
- **Legal and Compliance (LEGAL)** - Legal matters and compliance
- **Customer Support (CS)** - Customer service and support

#### Directory Members:
Each department includes multiple members with different roles:
- Directors/Heads
- Managers
- Specialists/Analysts
- Representatives

Each member includes:
- Full name and designation
- Email address (company.com domain)
- Phone number
- Department association
- Professional description
- Some include office addresses

## Prerequisites

Make sure you have:
1. MongoDB running and accessible
2. Environment variables set up (especially `MONGODB_URI`)
3. All dependencies installed (`npm install`)

## Important Notes

⚠️ **Warning**: These scripts will **delete existing data** in the following collections:
- `departments` (seed-sample-data and seed-quick-data)
- `directories` (all scripts)
- `committees` (seed-indian-committees)

The `seed-indian-committees.js` script will completely replace your directory members and committees with Indian data.

The scripts will also create a system user (`<EMAIL>`) if one doesn't exist. This user is used for data ownership purposes.

Only run these scripts on development/testing databases, never on production data.

## Sample Data Overview

After running the seed script, you'll have:
- 10 departments with proper codes and descriptions
- 30+ directory members across all departments
- Department heads properly assigned
- Realistic contact information for testing

This data is perfect for:
- Testing committee creation workflows
- Testing meeting scheduling with attendees
- Testing task assignment to team members
- Demo purposes

## Customization

You can modify the `sampleDepartments` and `sampleDirectoryMembers` arrays in the script to:
- Add more departments
- Change contact information
- Add different roles/designations
- Modify company structure

## System User

The scripts create a system user with the following credentials (for data ownership):
- Email: `<EMAIL>`
- Password: `systemPassword123`
- Role: Deputy Commissioner (1)

This user is used as the `createdBy` reference for all seeded directory entries. In a production environment, you would use actual user IDs.

## Troubleshooting

If you encounter issues:

1. **Connection Error**: Ensure MongoDB is running and `MONGODB_URI` is correct
2. **Permission Error**: Make sure the database user has write permissions
3. **Validation Error**: Check that all required fields are properly set in the sample data

## Example Output

```
Connecting to MongoDB...
Connected to MongoDB successfully!
Clearing existing departments and directory data...
Existing data cleared!
Creating sample departments...
Created 10 departments!
Creating sample directory members...
Created 30 directory members!
Assigning department heads...
Updated 10 departments with heads!

=== DATA SEEDING COMPLETE ===
Created 10 departments:
  - Human Resources (HR): 3 members
  - Information Technology (IT): 4 members
  - Finance and Accounting (FIN): 3 members
  - Marketing and Communications (MKT): 3 members
  - Operations and Management (OPS): 3 members
  - Research and Development (R&D): 3 members
  - Quality Assurance (QA): 3 members
  - Sales and Business Development (SALES): 3 members
  - Legal and Compliance (LEGAL): 2 members
  - Customer Support (CS): 3 members

Total directory members: 30
Sample data has been successfully seeded to your database!
```

### `seed-quick-data.js`

Creates minimal test data quickly for basic testing purposes.

**What it creates:**
- A system user (if not already exists)
- 3 basic departments (HR, IT, Operations)
- 6 directory members (2 per department)
- Basic department structure

**Usage:**
```bash
npm run seed-quick-data
```

### `seed-indian-committees.js` ⚠️ **DEPRECATED**

Creates Indian-style committees and directory members, but uses "System User" for all data attribution.

**Issues:**
- All data shows "Created by System User"
- Not suitable for production-like testing
- Use `seed-committees-with-real-users.js` instead

**Usage:**
```bash
npm run seed-indian-committees
```

### `seed-committees-with-real-users.js` ⭐ **RECOMMENDED**

Creates Indian-style committees and directory members with **proper user attribution** instead of using a system user.

**What it creates:**
- Updates existing admin user with proper name
- Creates additional real users for better attribution
- Indian-style committees with Indian names and themes
- Directory members with Indian names and designations
- **All data is attributed to real users instead of "System User"**
- Random creation dates within the last 30 days for realistic data

**Usage:**
```bash
npm run seed-committees-real-users
```

**Features:**
- ✅ Uses real users for `createdBy` fields
- ✅ Creates additional users if needed
- ✅ Distributes committee creation among multiple users
- ✅ Indian-themed committee names and descriptions
- ✅ Realistic Indian names for directory members
- ✅ Random creation timestamps for organic feel
- ✅ Proper user attribution throughout

**⚠️ This script replaces existing committees and directory data!**

{"name": "j<PERSON><PERSON>", "version": "3.10.1", "author": "<PERSON> <<EMAIL>>", "description": "Create, read and edit .zip files with JavaScript http://stuartk.com/jszip", "scripts": {"test": "npm run test-node && npm run test-browser && tsc", "test-node": "qunit --require ./test/helpers/test-utils.js --require ./test/helpers/node-test-utils.js test/asserts/", "test-browser": "grunt build && node test/run.js --test", "benchmark": "npm run benchmark-node && npm run benchmark-browser", "benchmark-node": "node test/benchmark/node.js", "benchmark-browser": "node test/run.js --benchmark", "lint": "eslint ."}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "yim<PERSON><PERSON>"}], "main": "./lib/index", "browser": {"./lib/index": "./dist/jszip.min.js", "readable-stream": "./lib/readable-stream-browser.js"}, "types": "./index.d.ts", "repository": {"type": "git", "url": "https://github.com/Stuk/jszip.git"}, "keywords": ["zip", "deflate", "inflate"], "devDependencies": {"benchmark": "^2.1.4", "browserify": "~13.0.0", "eslint": "^8.18.0", "grunt": "~0.4.1", "grunt-browserify": "~5.0.0", "grunt-cli": "~1.1.0", "grunt-contrib-uglify": "~4.0.1", "http-server": "^13.0.2", "jszip-utils": "~0.0.2", "package-json-versionify": "1.0.2", "playwright": "^1.15.2", "qunit": "~2.9.2", "tmp": "0.0.28", "typescript": "^4.6.3"}, "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}, "license": "(MIT OR GPL-3.0-or-later)"}
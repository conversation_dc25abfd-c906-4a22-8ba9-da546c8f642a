{"name": "@editorjs/marker", "version": "1.4.0", "keywords": ["codex editor", "marker", "editor.js", "editorjs"], "description": "<PERSON><PERSON> for Editor.js", "license": "MIT", "repository": "https://github.com/editor-js/marker", "files": ["dist"], "main": "./dist/marker.umd.js", "module": "./dist/marker.mjs", "exports": {".": {"import": "./dist/marker.mjs", "require": "./dist/marker.umd.js"}}, "scripts": {"dev": "vite", "build": "vite build"}, "author": {"name": "CodeX", "email": "<EMAIL>"}, "devDependencies": {"vite": "^4.5.0", "vite-plugin-css-injected-by-js": "^3.3.0"}, "dependencies": {"@codexteam/icons": "^0.0.5"}}
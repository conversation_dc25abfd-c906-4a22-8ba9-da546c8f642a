import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Paper,
  TextInput,
  Modal,
  Stack,
  MultiSelect,
  Select,
  Textarea,
  Card,
  ActionIcon,
  Grid,
  List,
  Alert,
  Skeleton,
} from '@mantine/core';
// Import removed: DateInput is no longer needed
import { notifications } from '@mantine/notifications';
import { IconPlus, IconEdit, IconTrash, IconCalendar, IconUsers, IconBuilding, IconUserStar } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Committee, Directory, Department } from '../types';
import { useNavigate } from 'react-router-dom';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';
import { DateInput } from '@mantine/dates';

// Memoized committee card component to prevent unnecessary re-renders
const CommitteeCard = memo(({ 
  committee, 
  onEdit, 
  onDelete, 
  onViewMeetings,
  getMemberNames,
  getAssistantName,
}: {
  committee: Committee;
  onEdit: (id: string) => void;
  onDelete: (committee: Committee) => void;
  onViewMeetings: (id: string) => void;
  getMemberNames: (members: Array<{ _id: string; name: string }>) => string;
  getAssistantName: (assistant?: { _id: string; name: string }) => string;
  getFrequencyDescription: (frequency: string) => string;
}) => (
  <Card withBorder shadow="sm">
    <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
      <Text fw={500} size="lg">
        {committee.name}
      </Text>
      <Group gap="xs" wrap="nowrap">
        <ActionIcon
          variant="light"
          color="blue"
          onClick={() => onEdit(committee._id)}
        >
          <IconEdit size={16} />
        </ActionIcon>
        <ActionIcon
          variant="light"
          color="red"
          onClick={() => onDelete(committee)}
        >
          <IconTrash size={16} />
        </ActionIcon>
      </Group>
    </Group>

    {committee.description && (
      <Text size="sm" c="dimmed" mb="md">
        {committee.description}
      </Text>
    )}

    <Stack gap="xs">
      <Group gap="xs" align="flex-start" wrap="nowrap">
        <IconUsers size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
        <Text size="sm">
          <Text component="span" fw={500} c="dimmed">Members: </Text>
          <Text component="span">{getMemberNames(committee.members)}</Text>
        </Text>
      </Group>

      <Group gap="xs" align="flex-start" wrap="nowrap">
        <IconUserStar size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
        <Text size="sm">
          <Text component="span" fw={500} c="dimmed">Member Secretary: </Text>
          <Text component="span">{getAssistantName(committee.assistant)}</Text>
        </Text>
      </Group>

      {committee.department && typeof committee.department === 'object' && committee.department.name && (
        <Group gap="xs" align="flex-start" wrap="nowrap">
          <IconBuilding size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
          <Text size="sm">
            <Text component="span" fw={500} c="dimmed">Department: </Text>
            <Text component="span">{committee.department.name}</Text>
          </Text>
        </Group>
      )}

      {committee.lastMeetingDate &&
      <Group gap="xs" align="flex-start" wrap="nowrap">
        <IconCalendar size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
        <Text size="sm">
          <Text component="span" fw={500} c="dimmed">Last Meeting Date: </Text>            <Text component="span">
            {new Date(committee.lastMeetingDate).toLocaleDateString()}
          </Text>
        </Text>
      </Group>}
      
      <Group gap="xs" align="flex-start" wrap="nowrap">
        <IconCalendar size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
        <Text size="sm">
          <Text component="span" fw={500} c="dimmed">Meeting Frequency: </Text>            <Text component="span">
            {committee.meetingFrequency}
          </Text>
        </Text>
      </Group>

      <Text size="xs" c="dimmed">
        Created by {committee.createdBy?.name || 'Unknown'} on {new Date(committee.createdAt).toLocaleDateString()}
      </Text>
    </Stack>

    <Button
      variant="light"
      fullWidth
      mt="md"
      onClick={() => onViewMeetings(committee._id)}
    >
      View Meetings
    </Button>
  </Card>
));

export default function CommitteesPage() {
  const navigate = useNavigate();
  const [directories, setDirectories] = useState<Directory[]>([]);
  const [departments, setDepartments] = useState<{value: string, label: string}[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editingCommitteeId, setEditingCommitteeId] = useState<string | null>(null);
  const [deletingCommittee, setDeletingCommittee] = useState<Committee | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    members: [] as string[],
    meetingFrequency: '',
    assistant: '',
    department: '',
    lastMeetingDate: ''
  });
  // Fetch function for pagination
  const fetchCommittees = useCallback(async (params: any) => {
    const response = await apiClient.get('/api/committee', { 
      params: { ...params, populate: 'createdBy' }
    });
    return response.data;
  }, []);
  const {
    data: committees,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Committee>(fetchCommittees, {
    sortBy: 'name',
    sortOrder: 'asc'
  });

  const sortOptions = [
    { value: 'name', label: 'Committee Name' },
    { value: 'meetingFrequency', label: 'Meeting Frequency' },
    // { value: 'startDate', label: 'Start Date' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  // Optimize data fetching to reduce calls
  useEffect(() => {
    fetchDirectories();
    fetchDepartments();
  }, []); // Only run once on mount

  // Clear assistant if they're removed from members
  useEffect(() => {
    if (formData.assistant && !formData.members.includes(formData.assistant)) {
      setFormData(prev => ({ ...prev, assistant: '' }));
    }
  }, [formData.members, formData.assistant]);

  // Auto-set department when member secretary is selected and department is empty
  useEffect(() => {
    if (formData.assistant && !formData.department && directories.length > 0) {
      const selectedSecretary = directories.find(dir => dir._id === formData.assistant);
      
      if (selectedSecretary?.department) {
        const departmentId = typeof selectedSecretary.department === 'object' 
          ? selectedSecretary.department._id 
          : selectedSecretary.department;
        
        setFormData(prev => ({ ...prev, department: departmentId }));
      }
    }
  }, [formData.assistant, directories]);
  const fetchDirectories = useCallback(async () => {
    try {
      // Fetch directories with department population, but with a reasonable limit
      const response = await apiClient.get('/api/directory', { 
        params: { 
          limit: 100, // Reduced from 1000 for better performance
          populate: 'department'
        }
      });
      const directoriesData = response.data.data || response.data;
      setDirectories(directoriesData);
    } catch (error) {
      console.error('Failed to fetch directories:', error);
    }
  }, []);

  const fetchDepartments = useCallback(async () => {
    try {
      const response = await apiClient.get('/api/departments', {
        params: { limit: 50 } // Reduced from 1000, most orgs don't have that many departments
      });
      const deptOptions = response.data.data.map((dept: Department) => ({
        value: dept._id,
        label: dept.name
      }));
      setDepartments(deptOptions);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  }, []);
  // Memoized data for form selectors to prevent unnecessary re-renders
  const directoryOptions = useMemo(() => 
    directories.map((dir) => ({
      value: dir._id,
      label: dir.name
    })), [directories]
  );

  const memberSecretaryOptions = useMemo(() => 
    directories
      .filter(dir => formData.members.includes(dir._id))
      .map((dir) => ({
        value: dir._id,
        label: dir.name
      })), [directories, formData.members]
  );

  const meetingFrequencyOptions = useMemo(() => [
    { value: 'weekly', label: 'Weekly' },
    { value: 'biweekly', label: 'Bi-weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'halfyearly', label: 'Half Yearly' },
    { value: 'yearly', label: 'Yearly' }
  ], []);

  // Optimized form handlers to prevent unnecessary re-renders
  const handleFormChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleMembersChange = useCallback((value: string[]) => {
    setFormData(prev => ({ ...prev, members: value }));
  }, []);

  const handleAssistantChange = useCallback((value: string | null) => {
    setFormData(prev => ({ ...prev, assistant: value || '' }));
  }, []);

  const handleSubmitCommittee = async (isEdit: boolean = false) => {
    try {
      // Validate required fields
      if (!formData.name.trim()) {
        notifications.show({
          title: 'Validation Error',
          message: 'Committee name is required',
          color: 'red',
        });
        return;
      }

      if (!formData.members || formData.members.length === 0) {
        notifications.show({
          title: 'Validation Error',
          message: 'At least one committee member is required',
          color: 'red',
        });
        return;
      }      
      
      if (!formData.meetingFrequency) {
        notifications.show({
          title: 'Validation Error',
          message: 'Meeting frequency is required',
          color: 'red',
        });
        return;
      }

      if (!formData.assistant) {
        notifications.show({
          title: 'Validation Error',
          message: 'Member Secretary is required',
          color: 'red',
        });
        return;
      }

      if (!formData.department) {
        notifications.show({
          title: 'Validation Error',
          message: 'Department is required',
          color: 'red',
        });
        return;
      }

      const payload = {
        ...formData
      };

      if (isEdit && editingCommitteeId) {
        await apiClient.patch(`/api/committee/${editingCommitteeId}`, payload);
      } else {
        await apiClient.post('/api/committee', payload);
      }

      notifications.show({
        title: `Committee ${isEdit ? 'Updated' : 'Created'}`,
        message: `Committee successfully ${isEdit ? 'updated' : 'created'}`,
        color: 'green',
      });

      refresh();
      setIsCreateModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Failed to save committee:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save committee',
        color: 'red',
      });
    }
  };

  const handleDeleteCommittee = useCallback((committee: Committee) => {
    setDeletingCommittee(committee);
    setIsDeleteModalOpen(true);
  }, []);

  const confirmDeleteCommittee = async () => {
    if (!deletingCommittee) return;

    try {
      await apiClient.delete(`/api/committee/${deletingCommittee._id}`);
      notifications.show({
        title: 'Success',
        message: 'Committee deleted successfully',
        color: 'green',
      });
      refresh();
      setIsDeleteModalOpen(false);
      setDeletingCommittee(null);
    } catch (error) {
      console.error('Failed to delete committee:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete committee',
        color: 'red',
      });
    }
  };
  const handleViewMeetings = useCallback((committeeId: string) => {
    navigate(`/committee/${committeeId}/meetings`);
  }, [navigate]);

  const handleEditCommittee = useCallback(async (id: string) => {
    const committee = committees.find(c => c._id === id);
    if (committee) {
      setFormData({
        name: committee.name,
        description: committee.description || '',
        members: committee.members.map((m: any) => m._id),
        meetingFrequency: committee.meetingFrequency,
        lastMeetingDate: committee.lastMeetingDate || '',
        assistant: committee.assistant?._id || '',
        department: committee.department && typeof committee.department === 'object' 
          ? committee.department._id 
          : committee.department || '',
      });
      setEditingCommitteeId(id);
      setIsCreateModalOpen(true);
    }
  }, [committees]);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      members: [],
      meetingFrequency: '',
      assistant: '',
      department: '',
      lastMeetingDate: ''
    });
    setEditingCommitteeId(null);
  };

  const getMemberNames = useCallback((members: Array<{ _id: string; name: string }>) => {
    return members.map(member => member.name).join(', ');
  }, []);

  const getAssistantName = useCallback((assistant?: { _id: string; name: string }) => {
    return assistant?.name || 'Not assigned';
  }, []);

  const getFrequencyDescription = useCallback((frequency: string) => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 
                        'July', 'August', 'September', 'October', 'November', 'December'];
    
    switch (frequency) {
      case 'monthly':
        return `1st of every month`;
      case 'quarterly':
        const currentQuarter = Math.floor(currentMonth / 3) + 1;
        return `Q${currentQuarter} starts ${monthNames[Math.floor(currentMonth / 3) * 3]} 1`;
      case 'halfyearly':
        return currentMonth < 6 ? 'H1 starts Jan 1' : 'H2 starts Jul 1';
      case 'yearly':
        return `Starts Jan 1, ${currentYear}`;
      case 'biweekly':
        return 'Every two weeks';
      default:
        return frequency;
    }
  }, []);

  return (
    <Container size="xl" py="md">      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>Committees</Title>
          <Text c="dimmed">Manage your committees and their meetings</Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Create Committee
        </Button>
      </Group>

      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>      {loading ? (
        <Grid gutter="md">
          {Array.from({ length: 3 }).map((_, index) => ( // Reduced from 6 to 3 for faster loading
            <Grid.Col key={`skeleton-${index}`} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm" padding="lg">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Skeleton height={24} width="70%" />
                  <Group gap="xs" wrap="nowrap">
                    <Skeleton height={28} width={28} circle />
                    <Skeleton height={28} width={28} circle />
                  </Group>
                </Group>
                
                <Skeleton height={16} mb="lg" width="90%" />
                
                <Stack gap="md" mb="lg">
                  <Group gap="xs">
                    <Skeleton height={16} width={16} circle />
                    <Skeleton height={16} width="30%" />
                    <Skeleton height={16} width="50%" />
                  </Group>
                  
                  <Group gap="xs">
                    <Skeleton height={16} width={16} circle />
                    <Skeleton height={16} width="40%" />
                    <Skeleton height={16} width="40%" />
                  </Group>
                  
                  <Group gap="xs">
                    <Skeleton height={16} width={16} circle />
                    <Skeleton height={16} width="45%" />
                    <Skeleton height={16} width="35%" />
                  </Group>
                </Stack>
                
                <Skeleton height={36} width="100%" mt="md" />
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      ) : committees.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No committees found</Text>
            <Button
              variant="light"
              onClick={() => setIsCreateModalOpen(true)}
            >
              Create your first committee
            </Button>
          </Stack>
        </Paper>
      ) : (
        <Grid gutter="md">
          {committees.map((committee) => (
            <Grid.Col key={committee._id} span={{ base: 12, md: 6, lg: 4 }}>
              <CommitteeCard
                committee={committee}
                onEdit={handleEditCommittee}
                onDelete={handleDeleteCommittee}
                onViewMeetings={handleViewMeetings}
                getMemberNames={getMemberNames}
                getAssistantName={getAssistantName}
                getFrequencyDescription={getFrequencyDescription}
              />
            </Grid.Col>
          ))}
        </Grid>
      )}

      <Modal
        opened={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title={editingCommitteeId ? "Edit Committee" : "Create Committee"}
        size="lg"
      >
        <Stack>
          <TextInput
            label="Committee Name"
            placeholder="Enter committee name"
            value={formData.name}
            onChange={(e) => handleFormChange('name', e.target.value)}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter committee description"
            value={formData.description}
            onChange={(e) => handleFormChange('description', e.target.value)}
            minRows={3}
            resize="vertical"
          />

          <MultiSelect
            label="Committee Members"
            placeholder="Select members"
            value={formData.members}
            onChange={handleMembersChange}
            data={directoryOptions}
            searchable
            required
          />

          <Select 
            label="Member Secretary"
            placeholder={formData.members.length === 0 ? "Select members first" : "Select member secretary from members"}
            value={formData.assistant}
            onChange={handleAssistantChange}
            data={memberSecretaryOptions}
            searchable
            disabled={formData.members.length === 0}
            description={formData.members.length === 0 ? "Member Secretary must be selected from committee members" : undefined}
            required
            withAsterisk
          />

          <Select
            label="Meeting Frequency"
            placeholder="Select frequency"
            value={formData.meetingFrequency}
            onChange={(value) => handleFormChange('meetingFrequency', value || '')}
            data={meetingFrequencyOptions}
            required
          />
          <DateInput
            label="Last Meeting Date"
            placeholder="Select a date"
            value={formData.lastMeetingDate}
            onChange={(value: string | null) => handleFormChange('lastMeetingDate', value ? new Date(value) : '')}
            clearable
          />
          <Select
            label="Department"
            placeholder="Select department"
            value={formData.department}
            onChange={(value) => handleFormChange('department', value || '')}
            data={departments}
            required
            searchable
            clearable
            description={
              formData.assistant && !formData.department 
                ? "Auto-filled from Member Secretary's department"
                : "Department will auto-fill when Member Secretary is selected"
            }
          />

          {/* Note about start dates removed */}
          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={() => {
              setIsCreateModalOpen(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={() => handleSubmitCommittee(!!editingCommitteeId)}>
              {editingCommitteeId ? 'Update' : 'Create'}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeletingCommittee(null);
        }}
        title="Delete Committee"
        size="md"
      >
        <Stack>
          <Alert color="red" title="Warning: This action cannot be undone">
            You are about to permanently delete the committee <strong>"{deletingCommittee?.name}"</strong>.
          </Alert>

          <Text size="sm" fw={500} mb="xs">This will permanently delete:</Text>
          <List size="sm" spacing="xs">
            <List.Item>The committee and all its information</List.Item>
            <List.Item>All meetings associated with this committee</List.Item>
            <List.Item>All tasks from those meetings</List.Item>
            <List.Item>All meeting agendas and attendee records</List.Item>
            <List.Item>All historical data and progress tracking</List.Item>
          </List>

          <Text size="sm" c="dimmed" mt="md">
            Committee members will not be deleted from the directory, but their association with this committee will be lost.
          </Text>

          <Group justify="flex-end" mt="lg">
            <Button
              variant="light"
              onClick={() => {
                setIsDeleteModalOpen(false);
                setDeletingCommittee(null);
              }}
            >
              Cancel
            </Button>
            <Button
              color="red"
              onClick={confirmDeleteCommittee}
            >
              Delete Committee
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}

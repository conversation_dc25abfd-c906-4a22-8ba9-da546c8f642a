{"name": "@editorjs/underline", "version": "1.2.1", "keywords": ["underline", "tool", "plugin", "editor.js", "editorjs"], "description": "Inline tool for underlining text fragments", "license": "MIT", "repository": "https://github.com/editor-js/underline", "main": "./dist/underline.umd.js", "module": "./dist/underline.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/underline.mjs", "require": "./dist/underline.umd.js", "types": "./dist/index.d.ts"}}, "scripts": {"dev": "vite", "build": "vite build"}, "author": {"name": "fajardm", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/editor-js/underline/issues"}, "homepage": "https://github.com/editor-js/underline#readme", "devDependencies": {"typescript": "^5.5.4", "vite": "^5.3.5", "vite-plugin-css-injected-by-js": "^3.5.1", "vite-plugin-dts": "^3.9.1", "@editorjs/editorjs": "^2.30.2"}, "dependencies": {"@codexteam/icons": "^0.3.2"}}
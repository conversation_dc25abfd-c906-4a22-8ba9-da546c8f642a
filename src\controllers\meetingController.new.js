import Meeting from "../models/meeting.js";

export const getMeetings = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      sortBy = 'startDate', 
      sortOrder = 'desc',
      committee 
    } = req.query;

    // Build filter based on query parameters
    const filter = {};
    if (committee) {
      filter.committee = committee;
    }

    // Add search filter
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { agenda: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Get total count for pagination
    const total = await Meeting.countDocuments(filter);

    const data = await Meeting.find(filter)
      .populate("committee")
      .populate({
        path: "tasks",
        select: "_id" // Only get task IDs for counting
      })
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Transform the data to include task count
    const meetingsWithTaskCount = data.map(meeting => {
      const { tasks, ...meetingData } = meeting.toObject();
      return {
        ...meetingData,
        taskCount: tasks?.length || 0
      };
    });

    res.json({
      data: meetingsWithTaskCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getMeeting = async (req, res) => {
  try {
    const data = await Meeting.findById(req.params.id)
      .populate("committee")
      .populate({
        path: "tasks",
        populate: {
          path: "assignedTo",
          select: "name designation"
        }
      });
    res.json(data);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

/**
 * Helper function to reorder existing agenda items after inserting a MoM item at the beginning
 * @param {string} meetingId - The ID of the meeting
 * @private
 */
async function reorderAgendaItemsAfterMoMInsertion(meetingId) {
  try {
    const AgendaItem = (await import('../models/agendaItem.js')).default;
    
    // Get all agenda items for this meeting except the MoM item (which is at order 0)
    const existingItems = await AgendaItem.find({
      meeting: meetingId,
      order: { $gt: 0 }
    }).sort({ order: 1 });
    
    // If there are existing items, make sure they're properly ordered starting from 1
    if (existingItems.length > 0) {
      const bulkOperations = existingItems.map((item, index) => ({
        updateOne: {
          filter: { _id: item._id },
          update: { $set: { order: index + 1 } }
        }
      }));
      
      if (bulkOperations.length > 0) {
        await AgendaItem.bulkWrite(bulkOperations);
        console.log(`Reordered ${bulkOperations.length} agenda items after MoM insertion`);
      }
    }
  } catch (error) {
    console.error("Error reordering agenda items:", error);
  }
}

export const createMeeting = async (req, res) => {
  try {
    const meeting = new Meeting(req.body);
    const saved = await meeting.save();
    
    // If there's a committee, check if this is the first meeting
    if (meeting.committee) {
      // Count existing meetings for this committee
      const meetingCount = await Meeting.countDocuments({ 
        committee: meeting.committee,
        _id: { $ne: saved._id } // Exclude the meeting we just created
      });
      
      // If this is not the first meeting, automatically create an agenda item for MoM discussion
      if (meetingCount > 0) {
        const AgendaItem = (await import('../models/agendaItem.js')).default;
        
        // Create a MoM discussion agenda item as the first item (order 0)
        const momAgendaItem = new AgendaItem({
          title: "Minutes of Previous Meeting",
          description: "Discussion on Minutes of Meeting (MoM) of previous meeting",
          meeting: saved._id,
          order: 0,
          createdBy: req.user ? req.user.id : null
        });
        
        await momAgendaItem.save();
        console.log("Created MoM discussion agenda item for meeting:", saved._id);
        
        // Reorder existing agenda items if necessary
        await reorderAgendaItemsAfterMoMInsertion(saved._id);
      }
    }
    
    res.status(201).json(saved);
  } catch (error) {
    console.error("Error creating meeting:", error);
    res.status(400).json({ error: error.message });
  }
};

export const updateMeeting = async (req, res) => {
  try {
    const updated = await Meeting.findByIdAndUpdate(req.params.id, req.body, { new: true });
    res.json(updated);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const deleteMeeting = async (req, res) => {
  try {
    await Meeting.findByIdAndDelete(req.params.id);
    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

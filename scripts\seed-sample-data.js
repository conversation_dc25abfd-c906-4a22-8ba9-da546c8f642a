import "dotenv/config";
import mongoose from "mongoose";
import Department from "../src/models/department.js";
import Directory from "../src/models/directory.js";
import User from "../src/models/user.js";
import bcrypt from "bcryptjs";

// Sample departments data
const sampleDepartments = [
  {
    name: "Human Resources",
    description: "Manages employee relations, recruitment, and organizational development",
    code: "HR",
    head: null, // Will be assigned after creating directory members
    isActive: true
  },
  {
    name: "Information Technology",
    description: "Manages technology infrastructure, software development, and IT support",
    code: "IT",
    head: null,
    isActive: true
  },
  {
    name: "Finance and Accounting",
    description: "Handles financial planning, budgeting, accounting, and financial reporting",
    code: "FIN",
    head: null,
    isActive: true
  },
  {
    name: "Marketing and Communications",
    description: "Manages brand promotion, public relations, and customer communications",
    code: "MKT",
    head: null,
    isActive: true
  },
  {
    name: "Operations and Management",
    description: "Oversees daily operations, project management, and process optimization",
    code: "OPS",
    head: null,
    isActive: true
  },
  {
    name: "Research and Development",
    description: "Conducts research, innovation, and product development activities",
    code: "R&D",
    head: null,
    isActive: true
  },
  {
    name: "Quality Assurance",
    description: "Ensures quality standards, compliance, and continuous improvement",
    code: "QA",
    head: null,
    isActive: true
  },
  {
    name: "Sales and Business Development",
    description: "Manages sales activities, client relationships, and business growth",
    code: "SALES",
    head: null,
    isActive: true
  },
  {
    name: "Legal and Compliance",
    description: "Handles legal matters, regulatory compliance, and risk management",
    code: "LEGAL",
    head: null,
    isActive: true
  },
  {
    name: "Customer Support",
    description: "Provides customer service, technical support, and customer success",
    code: "CS",
    head: null,
    isActive: true
  }
];

// Sample directory members data
const sampleDirectoryMembers = [
  // HR Department
  {
    name: "Sarah Johnson",
    designation: "HR Director",
    email: "<EMAIL>",
    phone: "******-0101",
    department: "HR",
    description: "Experienced HR professional with 15+ years in talent management",
    address: "123 Business Ave, Suite 100, New York, NY 10001"
  },
  {
    name: "Michael Chen",
    designation: "HR Manager",
    email: "<EMAIL>",
    phone: "******-0102",
    department: "HR",
    description: "Specializes in recruitment and employee development programs"
  },
  {
    name: "Emily Rodriguez",
    designation: "HR Specialist",
    email: "<EMAIL>",
    phone: "******-0103",
    department: "HR",
    description: "Handles employee relations and policy implementation"
  },

  // IT Department
  {
    name: "David Kim",
    designation: "CTO",
    email: "<EMAIL>",
    phone: "******-0201",
    department: "IT",
    description: "Technology leader with expertise in cloud infrastructure and digital transformation",
    address: "456 Tech Street, Floor 12, San Francisco, CA 94105"
  },
  {
    name: "Jennifer Walsh",
    designation: "IT Manager",
    email: "<EMAIL>",
    phone: "******-0202",
    department: "IT",
    description: "Manages IT operations and cybersecurity initiatives"
  },
  {
    name: "Alex Thompson",
    designation: "Senior Developer",
    email: "<EMAIL>",
    phone: "******-0203",
    department: "IT",
    description: "Full-stack developer with expertise in modern web technologies"
  },
  {
    name: "Maria Gonzalez",
    designation: "DevOps Engineer",
    email: "<EMAIL>",
    phone: "******-0204",
    department: "IT",
    description: "Specializes in CI/CD pipelines and cloud infrastructure automation"
  },

  // Finance Department
  {
    name: "Robert Wilson",
    designation: "CFO",
    email: "<EMAIL>",
    phone: "******-0301",
    department: "FIN",
    description: "Financial executive with 20+ years experience in corporate finance",
    address: "789 Financial Plaza, Suite 2500, Chicago, IL 60601"
  },
  {
    name: "Lisa Brown",
    designation: "Finance Manager",
    email: "<EMAIL>",
    phone: "******-0302",
    department: "FIN",
    description: "Oversees budgeting, forecasting, and financial analysis"
  },
  {
    name: "James Taylor",
    designation: "Senior Accountant",
    email: "<EMAIL>",
    phone: "******-0303",
    department: "FIN",
    description: "Handles accounts payable, receivable, and monthly financial reporting"
  },

  // Marketing Department
  {
    name: "Amanda Davis",
    designation: "Marketing Director",
    email: "<EMAIL>",
    phone: "******-0401",
    department: "MKT",
    description: "Creative marketing leader with expertise in digital marketing and brand management",
    address: "321 Creative Blvd, Studio 5, Los Angeles, CA 90210"
  },
  {
    name: "Kevin Miller",
    designation: "Digital Marketing Manager",
    email: "<EMAIL>",
    phone: "******-0402",
    department: "MKT",
    description: "Manages social media campaigns and online advertising strategies"
  },
  {
    name: "Rachel Green",
    designation: "Content Specialist",
    email: "<EMAIL>",
    phone: "******-0403",
    department: "MKT",
    description: "Creates engaging content for various marketing channels"
  },

  // Operations Department
  {
    name: "Thomas Anderson",
    designation: "Operations Director",
    email: "<EMAIL>",
    phone: "******-0501",
    department: "OPS",
    description: "Operations expert focused on process improvement and efficiency optimization",
    address: "654 Operations Center, Building B, Dallas, TX 75201"
  },
  {
    name: "Nicole White",
    designation: "Project Manager",
    email: "<EMAIL>",
    phone: "******-0502",
    department: "OPS",
    description: "Certified PMP with experience managing complex cross-functional projects"
  },
  {
    name: "Daniel Lee",
    designation: "Operations Analyst",
    email: "<EMAIL>",
    phone: "******-0503",
    department: "OPS",
    description: "Analyzes operational data and implements process improvements"
  },

  // R&D Department
  {
    name: "Dr. Patricia Clark",
    designation: "R&D Director",
    email: "<EMAIL>",
    phone: "******-0601",
    department: "R&D",
    description: "PhD in Engineering with 18 years of research and innovation experience",
    address: "987 Innovation Park, Lab Complex A, Boston, MA 02101"
  },
  {
    name: "Andrew Martin",
    designation: "Research Scientist",
    email: "<EMAIL>",
    phone: "******-0602",
    department: "R&D",
    description: "Conducts applied research and develops new product concepts"
  },
  {
    name: "Stephanie Young",
    designation: "Product Development Manager",
    email: "<EMAIL>",
    phone: "******-0603",
    department: "R&D",
    description: "Manages product lifecycle from concept to market launch"
  },

  // Quality Assurance Department
  {
    name: "Christopher Hall",
    designation: "QA Director",
    email: "<EMAIL>",
    phone: "******-0701",
    department: "QA",
    description: "Quality management expert with Six Sigma Black Belt certification",
    address: "147 Quality Drive, Testing Center, Austin, TX 78701"
  },
  {
    name: "Melissa Garcia",
    designation: "QA Manager",
    email: "<EMAIL>",
    phone: "******-0702",
    department: "QA",
    description: "Implements quality control processes and compliance standards"
  },
  {
    name: "Brian Adams",
    designation: "QA Analyst",
    email: "<EMAIL>",
    phone: "******-0703",
    department: "QA",
    description: "Performs quality testing and ensures adherence to quality standards"
  },

  // Sales Department
  {
    name: "Victoria Scott",
    designation: "Sales Director",
    email: "<EMAIL>",
    phone: "******-0801",
    department: "SALES",
    description: "Results-driven sales leader with consistent track record of exceeding targets",
    address: "258 Sales Plaza, Tower 1, Miami, FL 33101"
  },
  {
    name: "Ryan Murphy",
    designation: "Senior Sales Manager",
    email: "<EMAIL>",
    phone: "******-0802",
    department: "SALES",
    description: "Manages key client relationships and develops new business opportunities"
  },
  {
    name: "Catherine Lewis",
    designation: "Sales Representative",
    email: "<EMAIL>",
    phone: "******-0803",
    department: "SALES",
    description: "Focuses on lead generation and client acquisition"
  },

  // Legal Department
  {
    name: "Jonathan Turner",
    designation: "General Counsel",
    email: "<EMAIL>",
    phone: "******-0901",
    department: "LEGAL",
    description: "Experienced corporate attorney specializing in business law and compliance",
    address: "369 Legal Center, 30th Floor, Washington, DC 20001"
  },
  {
    name: "Samantha Collins",
    designation: "Legal Counsel",
    email: "<EMAIL>",
    phone: "******-0902",
    department: "LEGAL",
    description: "Handles contracts, intellectual property, and regulatory matters"
  },

  // Customer Support Department
  {
    name: "Mark Phillips",
    designation: "Customer Success Director",
    email: "<EMAIL>",
    phone: "******-1001",
    department: "CS",
    description: "Customer experience expert focused on satisfaction and retention",
    address: "741 Support Center, Help Desk Building, Phoenix, AZ 85001"
  },
  {
    name: "Laura Evans",
    designation: "Support Manager",
    email: "<EMAIL>",
    phone: "******-1002",
    department: "CS",
    description: "Manages customer support operations and team development"
  },
  {
    name: "Joshua Wright",
    designation: "Technical Support Specialist",
    email: "<EMAIL>",
    phone: "******-1003",
    department: "CS",
    description: "Provides technical assistance and troubleshooting support"
  }
];

async function seedData() {
  try {
    // Connect to MongoDB
    console.log("Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB successfully!");

    // Create or get system user for createdBy field
    console.log("Creating/finding system user...");
    let systemUser = await User.findOne({ email: "<EMAIL>" });
    
    if (!systemUser) {
      const hashedPassword = await bcrypt.hash("systemPassword123", 10);
      systemUser = await User.create({
        email: "<EMAIL>",
        password: hashedPassword,
        name: "System User",
        role: 1 // Deputy Commissioner role
      });
      console.log("Created system user!");
    } else {
      console.log("System user already exists!");
    }

    // Clear existing data
    console.log("Clearing existing departments and directory data...");
    await Department.deleteMany({});
    await Directory.deleteMany({});
    console.log("Existing data cleared!");

    // Create departments
    console.log("Creating sample departments...");
    const createdDepartments = await Department.insertMany(sampleDepartments);
    console.log(`Created ${createdDepartments.length} departments!`);

    // Create a map of department codes to department IDs
    const departmentMap = {};
    createdDepartments.forEach(dept => {
      departmentMap[dept.code] = dept._id;
    });

    // Prepare directory members with department references
    console.log("Creating sample directory members...");
    const directoryMembersWithDepartments = sampleDirectoryMembers.map(member => ({
      ...member,
      department: departmentMap[member.department],
      createdBy: systemUser._id // Use the system user's ObjectId
    }));

    const createdMembers = await Directory.insertMany(directoryMembersWithDepartments);
    console.log(`Created ${createdMembers.length} directory members!`);

    // Update departments with department heads (assign first member of each department as head)
    console.log("Assigning department heads...");
    const departmentUpdates = [];

    for (const dept of createdDepartments) {
      // Find the first member (usually the director/manager) for each department
      const departmentHead = createdMembers.find(member => 
        member.department.toString() === dept._id.toString() && 
        (member.designation.toLowerCase().includes('director') || 
         member.designation.toLowerCase().includes('cto') || 
         member.designation.toLowerCase().includes('cfo') ||
         member.designation.toLowerCase().includes('counsel'))
      );

      if (departmentHead) {
        departmentUpdates.push({
          updateOne: {
            filter: { _id: dept._id },
            update: { head: departmentHead._id }
          }
        });
      }
    }

    if (departmentUpdates.length > 0) {
      await Department.bulkWrite(departmentUpdates);
      console.log(`Updated ${departmentUpdates.length} departments with heads!`);
    }

    // Display summary
    console.log("\n=== DATA SEEDING COMPLETE ===");
    console.log(`Created ${createdDepartments.length} departments:`);
    createdDepartments.forEach(dept => {
      const memberCount = createdMembers.filter(member => 
        member.department.toString() === dept._id.toString()
      ).length;
      console.log(`  - ${dept.name} (${dept.code}): ${memberCount} members`);
    });

    console.log(`\nTotal directory members: ${createdMembers.length}`);
    console.log("\nSample data has been successfully seeded to your database!");

  } catch (error) {
    console.error("Error seeding data:", error);
    process.exit(1);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("Database connection closed.");
    process.exit(0);
  }
}

// Run the seeding script
seedData();

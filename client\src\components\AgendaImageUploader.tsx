import { useState } from "react";
import { apiClient } from "../config/axios";
import {
  Button,
  Group,
  Text,
  Paper,
  FileInput,
  Stack,
  Loader,
  Title,
  Divider,
  Checkbox,
  Card,
  ActionIcon,
  Image,
  Modal,
  TextInput,
  Textarea,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import {
  IconUpload,
  IconPhoto,
  IconCheck,
  IconX,
  IconEdit,
} from "@tabler/icons-react";
import { AgendaImageUploadResponse, SuggestedAgendaItem } from "@/types";

interface AgendaImageUploaderProps {
  meetingId: string;
  onAgendaItemsCreated: () => void;
}

export default function AgendaImageUploader({ meetingId, onAgendaItemsCreated }: AgendaImageUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploadResponse, setUploadResponse] = useState<AgendaImageUploadResponse | null>(null);
  const [selectedAgendaItems, setSelectedAgendaItems] = useState<Record<number, boolean>>({});
  const [editingAgendaItem, setEditingAgendaItem] = useState<{ index: number; item: SuggestedAgendaItem } | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleFileChange = (file: File | null) => {
    setFile(file);
    // Reset states when a new file is selected
    setUploadResponse(null);
    setSelectedAgendaItems({});
  };

  const handleUpload = async () => {
    if (!file) {
      notifications.show({
        title: "Error",
        message: "Please select a PDF or image file to upload",
        color: "red",
        icon: <IconX />
      });
      return;
    }

    setLoading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await apiClient.post<AgendaImageUploadResponse>(
        `/api/meetings/${meetingId}/agenda/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        }
      );

      setUploadResponse(response.data);
      
      // Initialize all agenda items as selected
      const initialSelectedState: Record<number, boolean> = {};
      response.data.suggestedAgendaItems.forEach((_, index) => {
        initialSelectedState[index] = true;
      });
      setSelectedAgendaItems(initialSelectedState);

      notifications.show({
        title: "Success",
        message: "Presentation uploaded and agenda items extracted",
        color: "green",
        icon: <IconCheck />
      });
    } catch (error) {
      console.error("Error uploading presentation:", error);
      notifications.show({
        title: "Error",
        message: "Failed to upload presentation or extract agenda items",
        color: "red",
        icon: <IconX />
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAgendaItemSelection = (index: number, checked: boolean) => {
    setSelectedAgendaItems({
      ...selectedAgendaItems,
      [index]: checked
    });
  };

  const handleEditAgendaItem = (index: number, item: SuggestedAgendaItem) => {
    setEditingAgendaItem({ index, item: { ...item } });
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (editingAgendaItem) {
      const { index, item } = editingAgendaItem;
      if (uploadResponse) {
        const updatedItems = [...uploadResponse.suggestedAgendaItems];
        updatedItems[index] = item;
        setUploadResponse({
          ...uploadResponse,
          suggestedAgendaItems: updatedItems
        });
      }
      setIsEditModalOpen(false);
      setEditingAgendaItem(null);
    }
  };

  const handleSaveAgendaItems = async () => {
    if (!uploadResponse) return;

    setLoading(true);

    try {
      const selectedAgendaItemsArray = uploadResponse.suggestedAgendaItems.filter(
        (_, index) => selectedAgendaItems[index]
      );

      if (selectedAgendaItemsArray.length === 0) {
        notifications.show({
          title: "Warning",
          message: "No agenda items selected to save",
          color: "yellow"
        });
        setLoading(false);
        return;
      }

      await apiClient.post(`/api/meetings/${meetingId}/agenda/from-presentation`, {
        agendaItems: selectedAgendaItemsArray,
        slideImages: uploadResponse.slideImages,
        originalText: uploadResponse.extractedText
      });

      notifications.show({
        title: "Success",
        message: `${selectedAgendaItemsArray.length} agenda items created successfully`,
        color: "green",
        icon: <IconCheck />
      });
      
      // Reset the form
      setFile(null);
      setUploadResponse(null);
      setSelectedAgendaItems({});
      
      // Notify parent component to refresh agenda items if needed
      onAgendaItemsCreated();
    } catch (error) {
      console.error("Error uploading document:", error);
      notifications.show({
        title: "Error",
        message: "Failed to upload document",
        color: "red",
        icon: <IconX />
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Stack>
      <Paper p="md" withBorder>
        <Stack>
          <Title order={4}>Upload Agenda PDF</Title>
          <Text size="sm" c="dimmed">
            Upload a PDF or image file for your meeting
          </Text>
          
          <Group>
            <FileInput
              placeholder="Upload PDF or image"
              accept=".pdf,.jpg,.jpeg,.png"
              value={file}
              onChange={handleFileChange}
              leftSection={<IconPhoto size={16} />}
              clearable
              style={{ flex: 1 }}
            />
            <Button
              onClick={handleUpload}
              leftSection={<IconUpload size={16} />}
              loading={loading}
              disabled={!file}
            >
              Upload
            </Button>
          </Group>
        </Stack>
      </Paper>

      {loading && (
        <Stack align="center" py="xl">
          <Loader />
          <Text>Processing document...</Text>
        </Stack>
      )}

      {uploadResponse && (
        <Stack>
          <Divider label="Extracted Agenda Items" labelPosition="center" />
          
          <Group justify="space-between">
            <Text>Select the agenda items you want to create:</Text>
            <Button 
              onClick={handleSaveAgendaItems} 
              disabled={loading || Object.values(selectedAgendaItems).every(v => !v)}
              loading={loading}
            >
              Save Selected Items
            </Button>
          </Group>
          
          {uploadResponse.suggestedAgendaItems.map((item, index) => (
            <Card key={index} withBorder padding="md" radius="md">
              <Card.Section withBorder inheritPadding py="xs">
                <Group justify="space-between">
                  <Group>
                    <Checkbox
                      checked={!!selectedAgendaItems[index]}
                      onChange={(event) => handleAgendaItemSelection(index, event.currentTarget.checked)}
                      label={<Text fw={500}>{item.title}</Text>}
                    />
                  </Group>
                  <ActionIcon
                    color="blue" 
                    onClick={() => handleEditAgendaItem(index, item)}
                  >
                    <IconEdit size={16} />
                  </ActionIcon>
                </Group>
              </Card.Section>
              
              {selectedAgendaItems[index] && (
                <>
                  <Text mt="md" size="sm" c="dimmed">
                    {item.description || 'No description provided'}
                  </Text>                  {/* Display the slide content if available */}
                  {uploadResponse.slideImages[index] && (
                    <>
                      <Image
                        src={uploadResponse.slideImages[index]} 
                        alt={`Slide for ${item.title}`}
                        mt="md"
                        radius="md"
                        fit="contain"
                        height={300}
                      />
                      <Text size="xs" c="dimmed" ta="center" mt="xs">Slide preview</Text>
                    </>
                  )}
                </>
              )}
            </Card>
          ))}
        </Stack>
      )}

      {/* Edit Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Agenda Item"
        size="lg"
      >
        {editingAgendaItem && (
          <Stack>
            <TextInput
              label="Title"
              value={editingAgendaItem.item.title}
              onChange={(e) => 
                setEditingAgendaItem({
                  ...editingAgendaItem,
                  item: { ...editingAgendaItem.item, title: e.target.value }
                })
              }
              required
            />
            
            <Textarea
              label="Description"
              value={editingAgendaItem.item.description || ''}
              onChange={(e) =>
                setEditingAgendaItem({
                  ...editingAgendaItem,
                  item: { ...editingAgendaItem.item, description: e.target.value }
                })
              }
              autosize
              minRows={3}
            />
            
            <Group justify="flex-end" mt="md">
              <Button variant="default" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveEdit}>
                Save Changes
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}

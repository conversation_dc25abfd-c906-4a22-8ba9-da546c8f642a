/**
 * <PERSON>er <PERSON> for Editor.js
 * Allows to highlight text fragments with a background color
 */
class Marker {
  static get isInline() {
    return true;
  }
  
  get state() {
    return this._state;
  }
  
  set state(state) {
    this._state = state;
  }
  
  constructor({api}) {
    this.api = api;
    this._state = false;
    this.button = null;
    this.tag = 'MARK';
    this.class = 'cdx-marker';
  }
  
  render() {
    this.button = document.createElement('button');
    this.button.type = 'button';
    this.button.innerHTML = '<svg width="20" height="18"><path d="M10.458 12.04l2.919 1.686-.781 1.417-.984-.03-.974 1.687H8.674l1.49-2.583-.508-.775.802-1.401zm.546-8.083l-.254 1.776 1.707 1.776-.753 1.695-1.861-1.016-.753 1.016-2.1-2.54.753-1.695 1.1 1.016.251-1.776 1.91-.252zm-4.628 8.91l-1.161.635-1.47-1.357 1.161-.635.764.41.723-.517.62.318.764-.318-.62-.41.62-.318.764.318.62-.318.764.41-.62.318.764.41z"/></svg>';
    this.button.classList.add(this.api.styles.inlineToolButton);
    
    return this.button;
  }
  
  surround(range) {
    if (this.state) {
      this.unwrap(range);
      return;
    }
    
    this.wrap(range);
  }
  
  wrap(range) {
    const selectedText = range.extractContents();
    const mark = document.createElement(this.tag);
    
    mark.classList.add(this.class);
    mark.appendChild(selectedText);
    range.insertNode(mark);
    
    this.api.selection.expandToTag(mark);
  }
  
  unwrap(range) {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    const text = range.extractContents();
    
    mark.remove();
    
    range.insertNode(text);
  }
  
  checkState() {
    const mark = this.api.selection.findParentTag(this.tag);
    
    this.state = !!mark;
    
    if (this.state) {
      this.button.classList.add(this.api.styles.inlineToolButtonActive);
    } else {
      this.button.classList.remove(this.api.styles.inlineToolButtonActive);
    }
  }
  
  static get sanitize() {
    return {
      mark: {
        class: 'cdx-marker'
      }
    };
  }
}

module.exports = Marker;

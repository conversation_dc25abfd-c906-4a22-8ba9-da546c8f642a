import genai from "@google/genai";
const { GoogleGenAI, createUserContent, createPartFromBase64 } = genai;
import fs from "fs";
import path from 'path';

// You need to set your Gemini API key in the environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

/**
 * Calls Gemini Vision API with the image and prompt, returns extracted tasks
 * @param {string} imagePath - Path to the image file
 * @returns {Promise<{extractedText: string, suggestedTasks: Array}>}
 */
export async function extractTasksFromImageWithGemini(imagePath) {
  try {
    const prompt = `
      Generate content from the image.
      Current Date Is: ${new Date().toISOString()}
      If due date is provided but date or month or year is not provided, use current date.
      If no time is mentioned in the due date default it to 12:00AM.
      For example if due by thursday, means next coming thursday.
    `
    // Read the file as a buffer
    console.log("Image size:", imagePath, fs.statSync(imagePath).size); // should be > 0
    const fileData = fs.readFileSync(imagePath, { encoding: "base64" });

    // Generate content using the uploaded file and prompt
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash-preview-05-20", // or the latest available model
      contents: createUserContent([
        createPartFromBase64(fileData, "image/jpeg"),
        prompt,
      ]),
      config: {
        systemInstruction: [
          {
            text: `You are an assistant that extracts tasks from handwritten meeting notes.`
          }, {
            text: `
              Return a JSON array of objects with fields: title, description, priority (low|medium|high), deadline (ISO 8601 date string or null). Try to determine (deadline) due date from the task's text.
              If you can't determine a field, use defaults: priority=medium, status=pending, deadline=null.
              Example output: [{"title": "Email John", "description": "Send project update", "priority": "high", "deadline": "2025-06-12T17:00:00Z"}]
            `
          },
        ]
      }
    });

    const text = response.text ?? "";
    let suggestedTasks;
    try {
      const parsedText = /** @type {{ title: string; deadline?: string | null }[]} */ (JSON.parse(text.match(/\[.*\]/s)?.[0] || "[]"));
      suggestedTasks = parsedText.map(task => ({
        ...task,
        status: "pending",
        deadline: task.deadline ?? null
      }));
    } catch (e) {
      console.error("⚠️ Failed to parse tasks from response:", e.message);
      suggestedTasks = [];
    }

    return {
      extractedText: suggestedTasks?.map(task => task.title)?.join("\n") ?? "",
      suggestedTasks: suggestedTasks ?? []
    };
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    throw error;
  }
}

export async function extractAgendaFromPresentationWithGemini(filePath) {
  try {
    console.log("Starting extraction from presentation:", filePath);

    // Simplify: Just use the uploaded file directly (like task extraction)
    console.log("Processing the uploaded file directly:", filePath);

    // Read the file as a buffer - matching the working task extraction function
    console.log("File size:", filePath, fs.statSync(filePath).size);
    const fileData = fs.readFileSync(filePath, { encoding: "base64" });

    // Get the original filename to use for the slide path
    const filename = path.basename(filePath);
    const slidePath = `/uploads/${filename}`;

    // The prompt for agenda extraction
    const prompt = `
      Analyze this document.
      Extract the main title and content. If there is data in form of rows or list make object of each item. Try to describe and write description of each item.
      If it's a presentation agenda slide, extract each agenda item.
      Provide output/response in JSON format as an array of agenda item objects.
    `;
    // Let the API auto-detect the image type instead of forcing image/jpeg
    console.log("Sending to Gemini Vision API with auto-detection");
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash-preview-05-20",
      contents: createUserContent([
        createPartFromBase64(fileData, "application/pdf"),
        prompt,
      ]),
      config: {
        systemInstruction: [
          {
            text: `You are an assistant that extracts information from presentation slides and images.`
          }, {
            text: `
              Return a JSON array of objects with fields: title (the main topic/heading of the slide), description (detailed content/key points from the slide).

              Example output: [{"title": "Project Timeline", "description": "The project will be completed in 3 phases over 6 months, with Phase 1 focusing on requirements gathering."}]
            `
          },
        ]
      }
    });

    const text = response.text ?? "";
    console.log("Gemini API Response:", text);

    // Extract the agenda item from the response
    let suggestedAgendaItems;
    let extractedText;
    try {
      // Extract JSON object from the response
      extractedText = text.match(/\[.*\]/s)?.[0];
      if (extractedText !== undefined) {
        suggestedAgendaItems = /** @type {{}[]} */ (JSON.parse(extractedText || "[]"));
      } else {
        extractedText = "";
        // Fallback if no JSON is found
        suggestedAgendaItems = [{
          title: "Extracted Item",
          description: text.substring(0, 200)
        }];
      }
    } catch (e) {
      console.error("Failed to parse agenda item from response:", e.message);
      suggestedAgendaItems = [{
        title: "Slide",
        description: "Failed to extract content from this slide"
      }];
    }

    // Create arrays with a single item for consistency with the existing interface
    const slideImages = [];

    console.log("Extracted agenda items:", JSON.stringify(suggestedAgendaItems, null, 2));
    console.log("Slide image paths:", slideImages);

    return {
      extractedText,
      suggestedAgendaItems,
      slideImages
    };
  } catch (error) {
    console.error("Error extracting agenda from presentation:", error);
    throw error;
  }
}

/**
 * Analyzes an agenda item and its attachments using Gemini AI
 * @param {Object} agendaItem - The agenda item object including description and files
 * @returns {Promise<string>} Analysis result as text
 */
export async function analyzeAgendaItemWithGemini(agendaItem) {
  try {
    console.log("Analyzing agenda item:", agendaItem.title);

    // Prepare prompt with agenda item description
    const prompt = `
      Analyze this agenda item for a meeting:
      Title: ${agendaItem.title}
      Description: ${agendaItem.description || 'No description provided'}
      
      Provide insights on:
      1. What are the key points or objectives?
      2. Any potential challenges or considerations
      3. Recommended actions or preparations
      4. Questions that might come up during discussion
    `;

    let contents = [prompt];
    let imagePromises = [];

    // If the agenda item has attached files, analyze them too
    if (agendaItem.files && agendaItem.files.length > 0) {
      // Process up to 3 files to avoid hitting API limits
      const filesToAnalyze = agendaItem.files.slice(0, 3);

      imagePromises = filesToAnalyze.map(async (file) => {
        try {
          const fullPath = path.join(process.cwd(), file.path.replace(/^\/uploads\//, 'uploads/'));

          if (!fs.existsSync(fullPath)) {
            console.warn(`File not found: ${fullPath}`);
            return null;
          }

          const fileData = fs.readFileSync(fullPath, { encoding: "base64" });
          return createPartFromBase64(fileData);
        } catch (err) {
          console.error(`Error processing file ${file.filename}:`, err);
          return null;
        }
      });

      const imageParts = (await Promise.all(imagePromises)).filter(Boolean);
      contents = [...imageParts, prompt];
    }

    // Call Gemini API
    console.log("Sending to Gemini API for analysis");
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash-preview-05-20",
      contents: createUserContent(contents),
      config: {
        systemInstruction: [
          {
            text: `You are an assistant that analyzes meeting agenda items.`
          }, {
            text: `
              Provide a concise but comprehensive analysis of the agenda item, including:
              - Summary of key points
              - Potential discussion areas
              - Questions to prepare for
              - Recommendations for effective handling of this agenda item
              
              Format your response in Markdown with appropriate headings and bullet points.
            `
          },
        ]
      }
    });

    const analysisText = response.text ?? "Analysis could not be generated.";
    console.log("Analysis complete:", analysisText.substring(0, 100) + "...");

    return analysisText;
  } catch (error) {
    console.error("Error analyzing agenda item:", error);
    return `Error generating analysis: ${error.message}`;
  }
}

/**
 * Analyze attendance sheet using Gemini AI
 * @param {string} filePath - Path to the uploaded attendance sheet
 * @param {string} mimetype - MIME type of the file
 * @param {Array} members - Array of committee members with _id and name
 * @returns {Object} Analysis results with attendance data
 */
export async function analyzeAttendanceSheetWithAI(filePath, mimetype, members) {
  try {
    if (!members || !members.length) {
      return { success: false, message: "No committee members provided for analysis" };
    }
    
    console.log(`Gemini AI analyzing attendance sheet: ${filePath} (${mimetype})`);
    
    try {
      // Read the file as a buffer
      console.log("File size:", filePath, fs.statSync(filePath).size);
      const fileData = fs.readFileSync(filePath, { encoding: "base64" });
      
      // Create list of members for context
      const memberList = members.map((member, index) => 
        `${index + 1}. ${member.name} (${member.designation || 'No designation'})`
      ).join('\n');
      
      // Create the prompt for Gemini
      const prompt = `
        This is an attendance sheet for a committee meeting. 
        
        The sheet has columns for: Sr.No., Designation, Member Information (containing name, email, phone), Signature (Self), and Signature (Correspondent).
        
        Analyze this image and determine:
        1. Who is present based on signatures in either the "Signature (Self)" or "Signature (Correspondent)" columns.
        2. For each person present, determine if they signed themselves ("self") or if someone else signed for them ("correspondent").
        
        The committee members are:
        ${memberList}
        
        For each member, return JSON data with these fields:
        - memberId: use their index number
        - memberName: their full name
        - present: true/false
        - signatureType: "self", "correspondent", or null if not present
        - confidence: a number between 0-100 indicating your confidence
        
        Return ONLY valid JSON as array of objects with those fields.
      `;
      
      // Call Gemini API with the image/PDF and prompt
      const response = await ai.models.generateContent({
        model: "gemini-2.5-flash-preview-05-20",
        contents: createUserContent([
          createPartFromBase64(fileData, mimetype),
          prompt,
        ]),
        config: {
          systemInstruction: [
            {
              text: `You are an assistant that analyzes attendance sheets for committee meetings.`
            }, {
              text: `
                Return a JSON array of objects with fields: 
                - memberId: the index number of the member in the provided list
                - memberName: their name as it appears in the list
                - present: boolean indicating if they have a signature
                - signatureType: "self" if they signed themselves, "correspondent" if someone else signed for them, null if not present
                - confidence: a number between 0-100 indicating your confidence in this determination
              `
            },
          ]
        }
      });
      
      const text = response.text ?? "";
      
      // Extract JSON from the response
      const jsonMatch = text.match(/\[.*\]/s);
      
      if (!jsonMatch) {
        console.error("Invalid JSON response from Gemini:", text);
        throw new Error("Could not parse attendance data from AI response");
      }
      
      // Parse the JSON response
      let attendanceData = JSON.parse(jsonMatch[0]);
      
      // Map the indices back to actual MongoDB IDs
      const attendanceResults = attendanceData.map(item => {
        const memberIndex = parseInt(item.memberId) - 1;
        const member = members[memberIndex] || null;
        
        if (!member) {
          return null;
        }
        
        return {
          memberId: member._id,
          memberName: member.name,
          present: item.present,
          signatureType: item.signatureType,
          confidence: item.confidence
        };
      }).filter(item => item !== null);
      
      return {
        success: true,
        attendanceResults,
        message: "Attendance sheet analyzed successfully with Gemini AI"
      };
      
    } catch (aiError) {
      console.error('Gemini AI analysis error:', aiError);
      throw aiError;
    }
    
  } catch (error) {
    console.error('Error in analyzeAttendanceSheetWithAI:', error);
    throw error;
  }
}

/**
 * Generate simulated attendance results when AI analysis fails
 * @param {Array} members - Array of committee members
 * @returns {Object} Simulated attendance results
 */
export function generateSimulatedAttendanceResults(members) {
  console.log("Generating simulated attendance results as fallback");
  
  const attendanceResults = members.map(member => {
    // Default to 70% present when AI fails
    const isPresent = Math.random() > 0.3;
    const signatureType = isPresent ? (Math.random() > 0.5 ? 'self' : 'correspondent') : null;
    
    return {
      memberId: member._id,
      memberName: member.name,
      present: isPresent,
      signatureType: signatureType,
      confidence: 50 // Lower confidence for fallback
    };
  });
  
  return {
    success: true,
    attendanceResults,
    message: "AI analysis failed, using simulated results",
    fallback: true
  };
}
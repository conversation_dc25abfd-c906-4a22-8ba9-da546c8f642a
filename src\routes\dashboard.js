import express from "express";
import auth from "../middleware/auth.js";
import Notification from "../models/notification.js";
import { getDashboardStats } from "../controllers/dashboardController.js";
import { getUpcomingMeetings } from "../controllers/meetingController.js";


const router = express.Router();

router.get("/stats", auth, getDashboardStats);

router.get("/upcomingMeetings", auth, getUpcomingMeetings);

router.get("/missedMeetings", auth, async (req, res, next) => {
	try {
		const now    = new Date();
		const filter = {
			periodEnd:  { $lte: now }
		};
		if (req.query.committeeId) filter.committeeId = req.query.committeeId;

		const notifications = await Notification.find(filter)
			.sort({ periodEnd: -1 })
			.populate("committeeId")
			.lean();

		const data = notifications.map(ele => ({
			...ele.committeeId,
			startDate: ele.periodEnd,
		}));
		res.json(data);
	} catch (err) {
		next(err);
	}
});

export default router;

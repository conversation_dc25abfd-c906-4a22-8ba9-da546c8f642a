/**
 * Notification system using Shoelace toast alerts
 */

/**
 * Show a notification as a toast
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, info)
 * @returns {HTMLElement} - The alert element
 */
export function showNotification(message, type = 'success') {
  // Map our types to Shoelace variants
  const variantMap = {
    'success': 'success',
    'error': 'danger',
    'info': 'primary'
  };

  const variant = variantMap[type] || 'neutral';
  const icon = type === 'success' ? 'check-circle' :
              type === 'error' ? 'exclamation-triangle' : 'info-circle';

  // Create the alert
  const alert = document.createElement('sl-alert');
  alert.variant = variant;
  alert.closable = true;
  alert.duration = 3000;
  alert.innerHTML = `
    <sl-icon name="${icon}" slot="icon"></sl-icon>
    <strong>${type === 'success' ? 'Success' : type === 'error' ? 'Error' : 'Info'}</strong><br>
    ${message}
  `;

  // Add it to the document body
  document.body.appendChild(alert);

  // Ensure the component is defined before calling toast()
  if (customElements.get('sl-alert')) {
    // If the component is already defined, call toast() directly
    setTimeout(() => {
      try {
        alert.toast();
      } catch (error) {
        console.error('Error showing toast:', error);
        // Fallback: just show the alert
        alert.setAttribute('open', '');
      }
    }, 0);
  } else {
    // Wait for the component to be defined
    customElements.whenDefined('sl-alert').then(() => {
      setTimeout(() => {
        try {
          alert.toast();
        } catch (error) {
          console.error('Error showing toast:', error);
          // Fallback: just show the alert
          alert.setAttribute('open', '');
        }
      }, 0);
    });
  }

  return alert;
}

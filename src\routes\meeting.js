import express from "express";
import multer from "multer";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { fileURLToPath } from "url";
import { dirname } from "path";
import auth from "../middleware/auth.js";
import {
  getMeetings,
  getMeeting,
  createMeeting,
  updateMeeting,
  deleteMeeting,
  updateMeetingAttendees
} from "../controllers/meetingController.js";
import {
  getTasksForMeeting,
  getTask,
  createTask,
  updateTask,
  deleteTask,
  uploadImageForMeeting,
  createTasksFromImageForMeeting
} from "../controllers/taskController.js";
import agendaItemRoutes from "./agendaItem.js";
import presentationUploadRoutes from "./presentationUpload.js";

const router = express.Router();

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, "../../uploads/"));
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// File filter to only allow images
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image/")) {
    cb(null, true);
  } else {
    cb(new Error("Only image files are allowed"), false);
  }
};

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter
});

// Meeting routes
router.get("/", auth, getMeetings);
router.get("/:id", auth, getMeeting);
router.post("/", auth, createMeeting);
router.patch("/:id", auth, updateMeeting);
router.delete("/:id", auth, deleteMeeting);

// Attendee management routes
router.patch("/:id/attendees", auth, updateMeetingAttendees);

// Task routes nested under meetings
router.get("/:meetingId/tasks", auth, getTasksForMeeting);
router.get("/:meetingId/tasks/:taskId", auth, getTask);
router.post("/:meetingId/tasks", auth, createTask);
router.patch("/:meetingId/tasks/:taskId", auth, updateTask);
router.delete("/:meetingId/tasks/:taskId", auth, deleteTask);

// Image upload and task extraction routes for meetings
router.post("/:meetingId/tasks/upload", auth, upload.single("image"), uploadImageForMeeting);
router.post("/:meetingId/tasks/from-image", auth, createTasksFromImageForMeeting);

// Use agenda item routes
router.use("/:meetingId/agenda", agendaItemRoutes);

// Use presentation upload routes for agenda
router.use("/:meetingId/agenda", presentationUploadRoutes);

export default router;

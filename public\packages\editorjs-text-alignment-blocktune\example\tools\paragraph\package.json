{"name": "@editorjs/paragraph", "version": "2.8.0", "keywords": ["codex editor", "paragraph", "tool", "editor.js", "editorjs"], "description": "Paragraph Tool for Editor.js", "license": "MIT", "repository": "https://github.com/editor-js/paragraph", "main": "./dist/bundle.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development --watch"}, "author": {"name": "CodeX", "email": "<EMAIL>"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/preset-env": "^7.10.2", "babel-loader": "^8.1.0", "css-loader": "^3.5.3", "raw-loader": "^4.0.1", "style-loader": "^1.2.1", "webpack": "^4.43.0", "webpack-cli": "^3.3.11"}}
export class EditorUI {
  createInsertButton(onClick) {
    const btn = document.createElement("button");
    btn.className = "insert-btn";
    btn.innerHTML = "+";
    btn.addEventListener("click", onClick);
    return btn;
  }

  createPageContainer() {
    return document.getElementById("page-container");
  }

  createPageElement(id) {
    const div = document.createElement("div");
    div.id = id;
    div.className = "page";
    return div;
  }
}

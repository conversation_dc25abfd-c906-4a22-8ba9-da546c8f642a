import Agenda from "agenda";
import { generateNotificationData } from "../controllers/notification.js";
import dayjs from "dayjs";

const logger = console;

const {
	MONGODB_URI,
	CRON_DAILY = "5 0 * * *",
	CRON_TZ     = "Asia/Kolkata",
} = process.env;

if (!MONGODB_URI) throw new Error("MONGODB_URI missing");

const agenda = new Agenda({
	db: { address: MONGODB_URI, collection: "agendaJobs" },
	defaultConcurrency: 1,
	processEvery: "30 seconds",
});

agenda.define("generate-daily-notifications", {
	priority: "high",
	concurrency: 1,
	lockLifetime: 1000 * 60 * 10,
	waitForLock: true,
}, generateNotificationData);

agenda.on("ready", async () => {
	const exists = await agenda.jobs({ name: "generate-daily-notifications" });
	if (!exists.length) {
		await agenda.every(CRON_DAILY, "generate-daily-notifications", {}, {
			timezone: CRON_TZ,
			skipImmediate: true,
		});
		logger.info(`📆  Job scheduled → ${CRON_DAILY} (${CRON_TZ})`);
	}
});

let startDate = dayjs("2025-06-01");
let endDate = dayjs();
for (let d = startDate; d.isBefore(endDate); d = d.add(1, "day")) {
	console.log(d.format("YYYY-MM-DD"));
	generateNotificationData(d);
}

export default agenda;

/**
 * Bold Tool for Editor.js
 */
class Bold {
  static get isInline() {
    return true;
  }

  constructor({api}) {
    this.api = api;
    this.button = null;
    this._state = false;
    this.tag = 'B';
    this.class = 'cdx-bold';
  }

  render() {
    this.button = document.createElement('button');
    this.button.type = 'button';
    this.button.innerHTML = '<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.10505 12C4.70805 12 4.4236 11.912 4.25171 11.736C4.0839 11.5559 4 11.2715 4 10.8827V4.11733C4 3.72033 4.08595 3.43588 4.25784 3.26398C4.43383 3.08799 4.71623 3 5.10505 3C6.42741 3 8.25591 3 9.02852 3C10.1373 3 11.0539 3.98153 11.0539 5.1846C11.0539 6.08501 10.6037 6.81855 9.70327 7.23602C10.8657 7.44851 11.5176 8.62787 11.5176 9.48128C11.5176 10.5125 10.9902 12 9.27734 12C8.77742 12 6.42626 12 5.10505 12ZM8.37891 8.00341H5.8V10.631H8.37891C8.9 10.631 9.6296 10.1211 9.6296 9.29877C9.6296 8.47643 8.9 8.00341 8.37891 8.00341ZM5.8 4.36903V6.69577H8.17969C8.53906 6.69577 9.27734 6.35939 9.27734 5.50002C9.27734 4.64064 8.48047 4.36903 8.17969 4.36903H5.8Z" fill="currentColor"></path></svg>';
    this.button.classList.add(this.api.styles.inlineToolButton);
    
    return this.button;
  }

  surround(range) {
    if (this.state) {
      this.unwrap(range);
      return;
    }
    
    this.wrap(range);
  }

  wrap(range) {
    const selectedText = range.extractContents();
    const mark = document.createElement(this.tag);
    
    mark.classList.add(this.class);
    mark.appendChild(selectedText);
    range.insertNode(mark);
    
    this.api.selection.expandToTag(mark);
  }

  unwrap(range) {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    const text = range.extractContents();
    
    mark.remove();
    
    range.insertNode(text);
  }

  checkState() {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    
    this.state = !!mark;
    
    if (this.state) {
      this.button.classList.add(this.api.styles.inlineToolButtonActive);
    } else {
      this.button.classList.remove(this.api.styles.inlineToolButtonActive);
    }
  }

  get state() {
    return this._state;
  }

  set state(state) {
    this._state = state;
  }

  static get sanitize() {
    return {
      b: {
        class: 'cdx-bold'
      }
    };
  }
}

export default Bold;

import express from "express";
import auth from "../middleware/auth.js";
import upload, { debugUpload } from "../middleware/upload.js";
import {
  getAgendaItems,
  getAgendaItem,
  createAgendaItem,
  updateAgendaItem,
  deleteAgendaItem,
  reorderAgendaItems,
  uploadFiles,
  downloadFile,
  downloadAllFiles,
  deleteFile
} from "../controllers/agendaItemController.js";
import { analyzeAgendaItem, checkAnalysisStatus } from "../controllers/agendaController.js";

const router = express.Router({ mergeParams: true });

// Base route is /api/meetings/:meetingId/agenda

// Get all agenda items for a meeting
router.get("/", auth, getAgendaItems);

// Get a specific agenda item
router.get("/:id", auth, getAgendaItem);

// Create a new agenda item
router.post("/", auth, createAgendaItem);

// Update an agenda item
router.put("/:id", auth, updateAgendaItem);

// Delete an agenda item
router.delete("/:id", auth, deleteAgendaItem);

// Reorder agenda items
router.put("/reorder", auth, reorderAgendaItems);

// File operations
// Upload files to an agenda item
router.post("/:id/files", auth, debugUpload("files"), uploadFiles);

// Download all files as ZIP (must be before the /:id/files/:fileId route)
router.get("/:id/files/download-zip", auth, downloadAllFiles);

// Download a single file
router.get("/:id/files/:fileId", auth, downloadFile);

// Delete a file
router.delete("/:id/files/:fileId", auth, deleteFile);

// Analyze an agenda item with AI
router.post("/:id/analyze", auth, analyzeAgendaItem);

// Check analysis status
router.get("/:id/analysis/status", auth, checkAnalysisStatus);

export default router;

/* PptxGenJS 3.12.0 @ 2023-03-20T03:12:31.375Z */
!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof require&&require;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof require&&require,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(l,e,r){!function(n){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof l&&l;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof l&&l,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(l,e,r){!function(n){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof l&&l;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof l&&l,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(l,e,r){!function(n){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof l&&l;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof l&&l,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(l,e,r){!function(n){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof l&&l;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof l&&l,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(l,e,r){!function(n){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof l&&l;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof l&&l,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(t,e,n){"use strict";var u=t("./utils"),c=t("./support"),p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";n.encode=function(t){for(var e,n,r,a,o,i,s=[],A=0,l=t.length,c="string"!==u.getTypeOf(t);A<t.length;)i=l-A,r=c?(e=t[A++],n=A<l?t[A++]:0,A<l?t[A++]:0):(e=t.charCodeAt(A++),n=A<l?t.charCodeAt(A++):0,A<l?t.charCodeAt(A++):0),a=(3&e)<<4|n>>4,o=1<i?(15&n)<<2|r>>6:64,i=2<i?63&r:64,s.push(p.charAt(e>>2)+p.charAt(a)+p.charAt(o)+p.charAt(i));return s.join("")},n.decode=function(t){var e,n,r,a,o,i=0,s=0;if("data:"===t.substr(0,"data:".length))throw new Error("Invalid base64 input, it looks like a data url.");var A,l=3*(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"")).length/4;if(t.charAt(t.length-1)===p.charAt(64)&&l--,t.charAt(t.length-2)===p.charAt(64)&&l--,l%1!=0)throw new Error("Invalid base64 input, bad content length.");for(A=new(c.uint8array?Uint8Array:Array)(0|l);i<t.length;)e=p.indexOf(t.charAt(i++))<<2|(a=p.indexOf(t.charAt(i++)))>>4,n=(15&a)<<4|(a=p.indexOf(t.charAt(i++)))>>2,r=(3&a)<<6|(o=p.indexOf(t.charAt(i++))),A[s++]=e,64!==a&&(A[s++]=n),64!==o&&(A[s++]=r);return A}},{"./support":30,"./utils":32}],2:[function(t,e,n){"use strict";var r=t("./external"),a=t("./stream/DataWorker"),o=t("./stream/Crc32Probe"),i=t("./stream/DataLengthProbe");function s(t,e,n,r,a){this.compressedSize=t,this.uncompressedSize=e,this.crc32=n,this.compression=r,this.compressedContent=a}s.prototype={getContentWorker:function(){var t=new a(r.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new i("data_length")),e=this;return t.on("end",function(){if(this.streamInfo.data_length!==e.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),t},getCompressedWorker:function(){return new a(r.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},s.createWorkerFrom=function(t,e,n){return t.pipe(new o).pipe(new i("uncompressedSize")).pipe(e.compressWorker(n)).pipe(new i("compressedSize")).withStreamInfo("compression",e)},e.exports=s},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(t,e,n){"use strict";var r=t("./stream/GenericWorker");n.STORE={magic:"\0\0",compressWorker:function(t){return new r("STORE compression")},uncompressWorker:function(){return new r("STORE decompression")}},n.DEFLATE=t("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(t,e,n){"use strict";var r=t("./utils"),i=function(){for(var t=[],e=0;e<256;e++){for(var n=e,r=0;r<8;r++)n=1&n?3988292384^n>>>1:n>>>1;t[e]=n}return t}();e.exports=function(t,e){return void 0!==t&&t.length?("string"!==r.getTypeOf(t)?function(t,e,n){var r=i,a=0+n;t^=-1;for(var o=0;o<a;o++)t=t>>>8^r[255&(t^e[o])];return-1^t}:function(t,e,n){var r=i,a=0+n;t^=-1;for(var o=0;o<a;o++)t=t>>>8^r[255&(t^e.charCodeAt(o))];return-1^t})(0|e,t,t.length):0}},{"./utils":32}],5:[function(t,e,n){"use strict";n.base64=!1,n.binary=!1,n.dir=!1,n.createFolders=!0,n.date=null,n.compression=null,n.compressionOptions=null,n.comment=null,n.unixPermissions=null,n.dosPermissions=null},{}],6:[function(t,e,n){"use strict";t="undefined"!=typeof Promise?Promise:t("lie");e.exports={Promise:t}},{lie:37}],7:[function(t,e,n){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,a=t("pako"),o=t("./utils"),i=t("./stream/GenericWorker"),s=r?"uint8array":"array";function A(t,e){i.call(this,"FlateWorker/"+t),this._pako=null,this._pakoAction=t,this._pakoOptions=e,this.meta={}}n.magic="\b\0",o.inherits(A,i),A.prototype.processChunk=function(t){this.meta=t.meta,null===this._pako&&this._createPako(),this._pako.push(o.transformTo(s,t.data),!1)},A.prototype.flush=function(){i.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},A.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this._pako=null},A.prototype._createPako=function(){this._pako=new a[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},n.compressWorker=function(t){return new A("Deflate",t)},n.uncompressWorker=function(){return new A("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(t,e,n){"use strict";function v(t,e){for(var n="",r=0;r<e;r++)n+=String.fromCharCode(255&t),t>>>=8;return n}function r(t,e,n,r,a,o){var i=t.file,s=t.compression,A=o!==b.utf8encode,l=y.transformTo("string",o(i.name)),c=y.transformTo("string",b.utf8encode(i.name)),u=i.comment,o=y.transformTo("string",o(u)),p=y.transformTo("string",b.utf8encode(u)),f=c.length!==i.name.length,u=p.length!==u.length,d="",h=i.dir,g=i.date,m={crc32:0,compressedSize:0,uncompressedSize:0},n=(e&&!n||(m.crc32=t.crc32,m.compressedSize=t.compressedSize,m.uncompressedSize=t.uncompressedSize),0);e&&(n|=8),A||!f&&!u||(n|=2048);t=0,e=0,h&&(t|=16),"UNIX"===a?(e=798,t|=(65535&(i.unixPermissions||(h?16893:33204)))<<16):(e=20,t|=63&(i.dosPermissions||0)),A=g.getUTCHours(),A=(A=((A<<=6)|g.getUTCMinutes())<<5)|g.getUTCSeconds()/2,a=g.getUTCFullYear()-1980,a=(a=((a<<=4)|g.getUTCMonth()+1)<<5)|g.getUTCDate(),f&&(d+="up"+v((h=v(1,1)+v(w(l),4)+c).length,2)+h),u&&(d+="uc"+v((i=v(1,1)+v(w(o),4)+p).length,2)+i),g="",g=(g=(g=(g=(g=(g=(g=(g=(g=(g+="\n\0")+v(n,2))+s.magic)+v(A,2))+v(a,2))+v(m.crc32,4))+v(m.compressedSize,4))+v(m.uncompressedSize,4))+v(l.length,2))+v(d.length,2);return{fileRecord:x.LOCAL_FILE_HEADER+g+l+d,dirRecord:x.CENTRAL_FILE_HEADER+v(e,2)+g+v(o.length,2)+"\0\0\0\0"+v(t,4)+v(r,4)+l+d+o}}var y=t("../utils"),a=t("../stream/GenericWorker"),b=t("../utf8"),w=t("../crc32"),x=t("../signature");function o(t,e,n,r){a.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=e,this.zipPlatform=n,this.encodeFileName=r,this.streamFiles=t,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}y.inherits(o,a),o.prototype.push=function(t){var e=t.meta.percent||0,n=this.entriesCount,r=this._sources.length;this.accumulate?this.contentBuffer.push(t):(this.bytesWritten+=t.data.length,a.prototype.push.call(this,{data:t.data,meta:{currentFile:this.currentFile,percent:n?(e+100*(n-r-1))/n:100}}))},o.prototype.openedSource=function(t){this.currentSourceOffset=this.bytesWritten,this.currentFile=t.file.name;var e=this.streamFiles&&!t.file.dir;e?(t=r(t,e,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName),this.push({data:t.fileRecord,meta:{percent:0}})):this.accumulate=!0},o.prototype.closedSource=function(t){this.accumulate=!1;var e=this.streamFiles&&!t.file.dir,n=r(t,e,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(n.dirRecord),e)this.push({data:x.DATA_DESCRIPTOR+v((e=t).crc32,4)+v(e.compressedSize,4)+v(e.uncompressedSize,4),meta:{percent:100}});else for(this.push({data:n.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},o.prototype.flush=function(){for(var t=this.bytesWritten,e=0;e<this.dirRecords.length;e++)this.push({data:this.dirRecords[e],meta:{percent:100}});var n,r,a=this.bytesWritten-t,o=(n=this.dirRecords.length,a=a,t=t,o=this.zipComment,r=this.encodeFileName,r=y.transformTo("string",r(o)),x.CENTRAL_DIRECTORY_END+"\0\0\0\0"+v(n,2)+v(n,2)+v(a,4)+v(t,4)+v(r.length,2)+r);this.push({data:o,meta:{percent:100}})},o.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},o.prototype.registerPrevious=function(t){this._sources.push(t);var e=this;return t.on("data",function(t){e.processChunk(t)}),t.on("end",function(){e.closedSource(e.previous.streamInfo),e._sources.length?e.prepareNextSource():e.end()}),t.on("error",function(t){e.error(t)}),this},o.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},o.prototype.error=function(t){var e=this._sources;if(!a.prototype.error.call(this,t))return!1;for(var n=0;n<e.length;n++)try{e[n].error(t)}catch(t){}return!0},o.prototype.lock=function(){a.prototype.lock.call(this);for(var t=this._sources,e=0;e<t.length;e++)t[e].lock()},e.exports=o},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(t,e,n){"use strict";var l=t("../compressions"),r=t("./ZipFileWorker");n.generateWorker=function(t,i,e){var s=new r(i.streamFiles,e,i.platform,i.encodeFileName),A=0;try{t.forEach(function(t,e){A++;var n=function(t,e){t=t||e,e=l[t];if(e)return e;throw new Error(t+" is not a valid compression method !")}(e.options.compression,i.compression),r=e.options.compressionOptions||i.compressionOptions||{},a=e.dir,o=e.date;e._compressWorker(n,r).withStreamInfo("file",{name:t,dir:a,date:o,comment:e.comment||"",unixPermissions:e.unixPermissions,dosPermissions:e.dosPermissions}).pipe(s)}),s.entriesCount=A}catch(t){s.error(t)}return s}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(t,e,n){"use strict";function r(){if(!(this instanceof r))return new r;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files={},this.comment=null,this.root="",this.clone=function(){var t,e=new r;for(t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}(r.prototype=t("./object")).loadAsync=t("./load"),r.support=t("./support"),r.defaults=t("./defaults"),r.version="3.5.0",r.loadAsync=function(t,e){return(new r).loadAsync(t,e)},r.external=t("./external"),e.exports=r},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(t,e,n){"use strict";var r=t("./utils"),i=t("./external"),s=t("./utf8"),A=t("./zipEntries"),l=t("./stream/Crc32Probe"),c=t("./nodejsUtils");e.exports=function(t,a){var o=this;return a=r.extend(a||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),c.isNode&&c.isStream(t)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):r.prepareContent("the loaded zip file",t,!0,a.optimizedBinaryString,a.base64).then(function(t){var e=new A(a);return e.load(t),e}).then(function(t){var e=[i.Promise.resolve(t)],n=t.files;if(a.checkCRC32)for(var r=0;r<n.length;r++)e.push(function(r){return new i.Promise(function(t,e){var n=r.decompressed.getContentWorker().pipe(new l);n.on("error",function(t){e(t)}).on("end",function(){n.streamInfo.crc32!==r.decompressed.crc32?e(new Error("Corrupted zip : CRC32 mismatch")):t()}).resume()})}(n[r]));return i.Promise.all(e)}).then(function(t){for(var t=t.shift(),e=t.files,n=0;n<e.length;n++){var r=e[n];o.file(r.fileNameStr,r.decompressed,{binary:!0,optimizedBinaryString:!0,date:r.date,dir:r.dir,comment:r.fileCommentStr.length?r.fileCommentStr:null,unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions,createFolders:a.createFolders})}return t.zipComment.length&&(o.comment=t.zipComment),o})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(t,e,n){"use strict";var r=t("../utils"),a=t("../stream/GenericWorker");function o(t,e){a.call(this,"Nodejs stream input adapter for "+t),this._upstreamEnded=!1,this._bindStream(e)}r.inherits(o,a),o.prototype._bindStream=function(t){var e=this;(this._stream=t).pause(),t.on("data",function(t){e.push({data:t,meta:{percent:0}})}).on("error",function(t){e.isPaused?this.generatedError=t:e.error(t)}).on("end",function(){e.isPaused?e._upstreamEnded=!0:e.end()})},o.prototype.pause=function(){return!!a.prototype.pause.call(this)&&(this._stream.pause(),!0)},o.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},e.exports=o},{"../stream/GenericWorker":28,"../utils":32}],13:[function(t,e,n){"use strict";var a=t("readable-stream").Readable;function r(t,e,n){a.call(this,e),this._helper=t;var r=this;t.on("data",function(t,e){r.push(t)||r._helper.pause(),n&&n(e)}).on("error",function(t){r.emit("error",t)}).on("end",function(){r.push(null)})}t("../utils").inherits(r,a),r.prototype._read=function(){this._helper.resume()},e.exports=r},{"../utils":32,"readable-stream":16}],14:[function(t,e,n){"use strict";e.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(t,e){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(t,e);if("number"==typeof t)throw new Error('The "data" argument must not be a number');return new Buffer(t,e)},allocBuffer:function(t){return Buffer.alloc?Buffer.alloc(t):((t=new Buffer(t)).fill(0),t)},isBuffer:function(t){return Buffer.isBuffer(t)},isStream:function(t){return t&&"function"==typeof t.on&&"function"==typeof t.pause&&"function"==typeof t.resume}}},{}],15:[function(t,e,n){"use strict";function a(t,e,n){var r=l.getTypeOf(e),a=l.extend(n||{},u);a.date=a.date||new Date,null!==a.compression&&(a.compression=a.compression.toUpperCase()),"string"==typeof a.unixPermissions&&(a.unixPermissions=parseInt(a.unixPermissions,8)),a.unixPermissions&&16384&a.unixPermissions&&(a.dir=!0),a.dosPermissions&&16&a.dosPermissions&&(a.dir=!0),a.dir&&(t=s(t)),a.createFolders&&(o=0<(i=(o="/"===(o=t).slice(-1)?o.substring(0,o.length-1):o).lastIndexOf("/"))?o.substring(0,i):"")&&A.call(this,o,!0);var o,i="string"===r&&!1===a.binary&&!1===a.base64,r=(n&&void 0!==n.binary||(a.binary=!i),(e instanceof p&&0===e.uncompressedSize||a.dir||!e||0===e.length)&&(a.base64=!1,a.binary=!0,e="",a.compression="STORE"),o=e instanceof p||e instanceof c?e:h.isNode&&h.isStream(e)?new g(t,e):l.prepareContent(t,e,a.binary,a.optimizedBinaryString,a.base64),new f(t,o,a));this.files[t]=r}function s(t){return"/"!==t.slice(-1)&&(t+="/"),t}function A(t,e){return e=void 0!==e?e:u.createFolders,t=s(t),this.files[t]||a.call(this,t,null,{dir:!0,createFolders:e}),this.files[t]}var o=t("./utf8"),l=t("./utils"),c=t("./stream/GenericWorker"),i=t("./stream/StreamHelper"),u=t("./defaults"),p=t("./compressedObject"),f=t("./zipObject"),d=t("./generate"),h=t("./nodejsUtils"),g=t("./nodejs/NodejsStreamInputAdapter");function m(t){return"[object RegExp]"===Object.prototype.toString.call(t)}e.exports={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(t){var e,n,r;for(e in this.files)this.files.hasOwnProperty(e)&&(r=this.files[e],n=e.slice(this.root.length,e.length))&&e.slice(0,this.root.length)===this.root&&t(n,r)},filter:function(n){var r=[];return this.forEach(function(t,e){n(t,e)&&r.push(e)}),r},file:function(t,e,n){var r;return 1!==arguments.length?(t=this.root+t,a.call(this,t,e,n),this):m(t)?(r=t,this.filter(function(t,e){return!e.dir&&r.test(t)})):(e=this.files[this.root+t])&&!e.dir?e:null},folder:function(n){var t,e;return n?m(n)?this.filter(function(t,e){return e.dir&&n.test(t)}):(t=this.root+n,t=A.call(this,t),(e=this.clone()).root=t.name,e):this},remove:function(n){n=this.root+n;var t=this.files[n];if(t||("/"!==n.slice(-1)&&(n+="/"),t=this.files[n]),t&&!t.dir)delete this.files[n];else for(var e=this.filter(function(t,e){return e.name.slice(0,n.length)===n}),r=0;r<e.length;r++)delete this.files[e[r].name];return this},generate:function(t){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(t){var e={};try{if((e=l.extend(t||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:o.utf8encode})).type=e.type.toLowerCase(),e.compression=e.compression.toUpperCase(),"binarystring"===e.type&&(e.type="string"),!e.type)throw new Error("No output type specified.");l.checkSupport(e.type),"darwin"!==e.platform&&"freebsd"!==e.platform&&"linux"!==e.platform&&"sunos"!==e.platform||(e.platform="UNIX"),"win32"===e.platform&&(e.platform="DOS");var n=e.comment||this.comment||"",r=d.generateWorker(this,e,n)}catch(t){(r=new c("error")).error(t)}return new i(r,e.type||"string",e.mimeType)},generateAsync:function(t,e){return this.generateInternalStream(t).accumulate(e)},generateNodeStream:function(t,e){return(t=t||{}).type||(t.type="nodebuffer"),this.generateInternalStream(t).toNodejsStream(e)}}},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(t,e,n){e.exports=t("stream")},{stream:void 0}],17:[function(t,e,n){"use strict";var r=t("./DataReader");function a(t){r.call(this,t);for(var e=0;e<this.data.length;e++)t[e]=255&t[e]}t("../utils").inherits(a,r),a.prototype.byteAt=function(t){return this.data[this.zero+t]},a.prototype.lastIndexOfSignature=function(t){for(var e=t.charCodeAt(0),n=t.charCodeAt(1),r=t.charCodeAt(2),a=t.charCodeAt(3),o=this.length-4;0<=o;--o)if(this.data[o]===e&&this.data[o+1]===n&&this.data[o+2]===r&&this.data[o+3]===a)return o-this.zero;return-1},a.prototype.readAndCheckSignature=function(t){var e=t.charCodeAt(0),n=t.charCodeAt(1),r=t.charCodeAt(2),t=t.charCodeAt(3),a=this.readData(4);return e===a[0]&&n===a[1]&&r===a[2]&&t===a[3]},a.prototype.readData=function(t){var e;return this.checkOffset(t),0===t?[]:(e=this.data.slice(this.zero+this.index,this.zero+this.index+t),this.index+=t,e)},e.exports=a},{"../utils":32,"./DataReader":18}],18:[function(t,e,n){"use strict";var r=t("../utils");function a(t){this.data=t,this.length=t.length,this.index=0,this.zero=0}a.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(t){},readInt:function(t){var e,n=0;for(this.checkOffset(t),e=this.index+t-1;e>=this.index;e--)n=(n<<8)+this.byteAt(e);return this.index+=t,n},readString:function(t){return r.transformTo("string",this.readData(t))},readData:function(t){},lastIndexOfSignature:function(t){},readAndCheckSignature:function(t){},readDate:function(){var t=this.readInt(4);return new Date(Date.UTC(1980+(t>>25&127),(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(31&t)<<1))}},e.exports=a},{"../utils":32}],19:[function(t,e,n){"use strict";var r=t("./Uint8ArrayReader");function a(t){r.call(this,t)}t("../utils").inherits(a,r),a.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=a},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(t,e,n){"use strict";var r=t("./DataReader");function a(t){r.call(this,t)}t("../utils").inherits(a,r),a.prototype.byteAt=function(t){return this.data.charCodeAt(this.zero+t)},a.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},a.prototype.readAndCheckSignature=function(t){return t===this.readData(4)},a.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=a},{"../utils":32,"./DataReader":18}],21:[function(t,e,n){"use strict";var r=t("./ArrayReader");function a(t){r.call(this,t)}t("../utils").inherits(a,r),a.prototype.readData=function(t){var e;return this.checkOffset(t),0===t?new Uint8Array(0):(e=this.data.subarray(this.zero+this.index,this.zero+this.index+t),this.index+=t,e)},e.exports=a},{"../utils":32,"./ArrayReader":17}],22:[function(t,e,n){"use strict";var r=t("../utils"),a=t("../support"),o=t("./ArrayReader"),i=t("./StringReader"),s=t("./NodeBufferReader"),A=t("./Uint8ArrayReader");e.exports=function(t){var e=r.getTypeOf(t);return r.checkSupport(e),"string"!==e||a.uint8array?"nodebuffer"===e?new s(t):a.uint8array?new A(r.transformTo("uint8array",t)):new o(r.transformTo("array",t)):new i(t)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(t,e,n){"use strict";n.LOCAL_FILE_HEADER="PK",n.CENTRAL_FILE_HEADER="PK",n.CENTRAL_DIRECTORY_END="PK",n.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",n.ZIP64_CENTRAL_DIRECTORY_END="PK",n.DATA_DESCRIPTOR="PK\b"},{}],24:[function(t,e,n){"use strict";var r=t("./GenericWorker"),a=t("../utils");function o(t){r.call(this,"ConvertWorker to "+t),this.destType=t}a.inherits(o,r),o.prototype.processChunk=function(t){this.push({data:a.transformTo(this.destType,t.data),meta:t.meta})},e.exports=o},{"../utils":32,"./GenericWorker":28}],25:[function(t,e,n){"use strict";var r=t("./GenericWorker"),a=t("../crc32");function o(){r.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}t("../utils").inherits(o,r),o.prototype.processChunk=function(t){this.streamInfo.crc32=a(t.data,this.streamInfo.crc32||0),this.push(t)},e.exports=o},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(t,e,n){"use strict";var r=t("../utils"),a=t("./GenericWorker");function o(t){a.call(this,"DataLengthProbe for "+t),this.propName=t,this.withStreamInfo(t,0)}r.inherits(o,a),o.prototype.processChunk=function(t){var e;t&&(e=this.streamInfo[this.propName]||0,this.streamInfo[this.propName]=e+t.data.length),a.prototype.processChunk.call(this,t)},e.exports=o},{"../utils":32,"./GenericWorker":28}],27:[function(t,e,n){"use strict";var r=t("../utils"),a=t("./GenericWorker");function o(t){a.call(this,"DataWorker");var e=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,t.then(function(t){e.dataIsReady=!0,e.data=t,e.max=t&&t.length||0,e.type=r.getTypeOf(t),e.isPaused||e._tickAndRepeat()},function(t){e.error(t)})}r.inherits(o,a),o.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this.data=null},o.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,r.delay(this._tickAndRepeat,[],this)),!0)},o.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished)||(r.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0)},o.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var t=null,e=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":t=this.data.substring(this.index,e);break;case"uint8array":t=this.data.subarray(this.index,e);break;case"array":case"nodebuffer":t=this.data.slice(this.index,e)}return this.index=e,this.push({data:t,meta:{percent:this.max?this.index/this.max*100:0}})},e.exports=o},{"../utils":32,"./GenericWorker":28}],28:[function(t,e,n){"use strict";function r(t){this.name=t||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}r.prototype={push:function(t){this.emit("data",t)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(t){this.emit("error",t)}return!0},error:function(t){return!this.isFinished&&(this.isPaused?this.generatedError=t:(this.isFinished=!0,this.emit("error",t),this.previous&&this.previous.error(t),this.cleanUp()),!0)},on:function(t,e){return this._listeners[t].push(e),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(t,e){if(this._listeners[t])for(var n=0;n<this._listeners[t].length;n++)this._listeners[t][n].call(this,e)},pipe:function(t){return t.registerPrevious(this)},registerPrevious:function(t){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=t.streamInfo,this.mergeStreamInfo(),this.previous=t;var e=this;return t.on("data",function(t){e.processChunk(t)}),t.on("end",function(){e.end()}),t.on("error",function(t){e.error(t)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){var t;return!(!this.isPaused||this.isFinished||(t=this.isPaused=!1,this.generatedError&&(this.error(this.generatedError),t=!0),this.previous&&this.previous.resume(),t))},flush:function(){},processChunk:function(t){this.push(t)},withStreamInfo:function(t,e){return this.extraStreamInfo[t]=e,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var t in this.extraStreamInfo)this.extraStreamInfo.hasOwnProperty(t)&&(this.streamInfo[t]=this.extraStreamInfo[t])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var t="Worker "+this.name;return this.previous?this.previous+" -> "+t:t}},e.exports=r},{}],29:[function(t,e,n){"use strict";var l=t("../utils"),a=t("./ConvertWorker"),o=t("./GenericWorker"),c=t("../base64"),r=t("../support"),i=t("../external"),s=null;if(r.nodestream)try{s=t("../nodejs/NodejsStreamOutputAdapter")}catch(t){}function A(t,e,n){var r=e;switch(e){case"blob":case"arraybuffer":r="uint8array";break;case"base64":r="string"}try{this._internalType=r,this._outputType=e,this._mimeType=n,l.checkSupport(r),this._worker=t.pipe(new a(r)),t.lock()}catch(t){this._worker=new o("error"),this._worker.error(t)}}A.prototype={accumulate:function(t){return s=this,A=t,new i.Promise(function(e,n){var r=[],a=s._internalType,o=s._outputType,i=s._mimeType;s.on("data",function(t,e){r.push(t),A&&A(e)}).on("error",function(t){r=[],n(t)}).on("end",function(){try{var t=function(t,e,n){switch(t){case"blob":return l.newBlob(l.transformTo("arraybuffer",e),n);case"base64":return c.encode(e);default:return l.transformTo(t,e)}}(o,function(t,e){for(var n=0,r=null,a=0,o=0;o<e.length;o++)a+=e[o].length;switch(t){case"string":return e.join("");case"array":return Array.prototype.concat.apply([],e);case"uint8array":for(r=new Uint8Array(a),o=0;o<e.length;o++)r.set(e[o],n),n+=e[o].length;return r;case"nodebuffer":return Buffer.concat(e);default:throw new Error("concat : unsupported type '"+t+"'")}}(a,r),i);e(t)}catch(t){n(t)}r=[]}).resume()});var s,A},on:function(t,e){var n=this;return"data"===t?this._worker.on(t,function(t){e.call(n,t.data,t.meta)}):this._worker.on(t,function(){l.delay(e,arguments,n)}),this},resume:function(){return l.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(t){if(l.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new s(this,{objectMode:"nodebuffer"!==this._outputType},t)}},e.exports=A},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(t,e,n){"use strict";if(n.base64=!0,n.array=!0,n.string=!0,n.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,n.nodebuffer="undefined"!=typeof Buffer,n.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)n.blob=!1;else{var r=new ArrayBuffer(0);try{n.blob=0===new Blob([r],{type:"application/zip"}).size}catch(t){try{var a=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);a.append(r),n.blob=0===a.getBlob("application/zip").size}catch(t){n.blob=!1}}}try{n.nodestream=!!t("readable-stream").Readable}catch(t){n.nodestream=!1}},{"readable-stream":16}],31:[function(t,e,a){"use strict";for(var A=t("./utils"),l=t("./support"),c=t("./nodejsUtils"),n=t("./stream/GenericWorker"),u=new Array(256),r=0;r<256;r++)u[r]=252<=r?6:248<=r?5:240<=r?4:224<=r?3:192<=r?2:1;function o(){n.call(this,"utf-8 decode"),this.leftOver=null}function i(){n.call(this,"utf-8 encode")}u[254]=u[254]=1,a.utf8encode=function(t){if(l.nodebuffer)return c.newBufferFrom(t,"utf-8");for(var e,n,r,a,o=t,i=o.length,s=0,A=0;A<i;A++)55296==(64512&(n=o.charCodeAt(A)))&&A+1<i&&56320==(64512&(r=o.charCodeAt(A+1)))&&(n=65536+(n-55296<<10)+(r-56320),A++),s+=n<128?1:n<2048?2:n<65536?3:4;for(e=new(l.uint8array?Uint8Array:Array)(s),A=a=0;a<s;A++)55296==(64512&(n=o.charCodeAt(A)))&&A+1<i&&56320==(64512&(r=o.charCodeAt(A+1)))&&(n=65536+(n-55296<<10)+(r-56320),A++),n<128?e[a++]=n:(n<2048?e[a++]=192|n>>>6:(n<65536?e[a++]=224|n>>>12:(e[a++]=240|n>>>18,e[a++]=128|n>>>12&63),e[a++]=128|n>>>6&63),e[a++]=128|63&n);return e},a.utf8decode=function(t){if(l.nodebuffer)return A.transformTo("nodebuffer",t).toString("utf-8");for(var e,n,r,a=t=A.transformTo(l.uint8array?"uint8array":"array",t),o=a.length,i=new Array(2*o),s=e=0;s<o;)if((n=a[s++])<128)i[e++]=n;else if(4<(r=u[n]))i[e++]=65533,s+=r-1;else{for(n&=2===r?31:3===r?15:7;1<r&&s<o;)n=n<<6|63&a[s++],r--;1<r?i[e++]=65533:n<65536?i[e++]=n:(n-=65536,i[e++]=55296|n>>10&1023,i[e++]=56320|1023&n)}return i.length!==e&&(i.subarray?i=i.subarray(0,e):i.length=e),A.applyFromCharCode(i)},A.inherits(o,n),o.prototype.processChunk=function(t){var e=A.transformTo(l.uint8array?"uint8array":"array",t.data),n=(this.leftOver&&this.leftOver.length&&(l.uint8array?(n=e,(e=new Uint8Array(n.length+this.leftOver.length)).set(this.leftOver,0),e.set(n,this.leftOver.length)):e=this.leftOver.concat(e),this.leftOver=null),function(t,e){for(var n=(e=(e=e||t.length)>t.length?t.length:e)-1;0<=n&&128==(192&t[n]);)n--;return!(n<0)&&0!==n&&n+u[t[n]]>e?n:e}(e)),r=e;n!==e.length&&(l.uint8array?(r=e.subarray(0,n),this.leftOver=e.subarray(n,e.length)):(r=e.slice(0,n),this.leftOver=e.slice(n,e.length))),this.push({data:a.utf8decode(r),meta:t.meta})},o.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:a.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},a.Utf8DecodeWorker=o,A.inherits(i,n),i.prototype.processChunk=function(t){this.push({data:a.utf8encode(t.data),meta:t.meta})},a.Utf8EncodeWorker=i},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(t,e,i){"use strict";var s=t("./support"),A=t("./base64"),n=t("./nodejsUtils"),r=t("set-immediate-shim"),l=t("./external");function a(t){return t}function c(t,e){for(var n=0;n<t.length;++n)e[n]=255&t.charCodeAt(n);return e}i.newBlob=function(e,n){i.checkSupport("blob");try{return new Blob([e],{type:n})}catch(t){try{var r=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return r.append(e),r.getBlob(n)}catch(t){throw new Error("Bug : can't construct the Blob.")}}};var o={stringifyByChunk:function(t,e,n){var r=[],a=0,o=t.length;if(o<=n)return String.fromCharCode.apply(null,t);for(;a<o;)r.push("array"===e||"nodebuffer"===e?String.fromCharCode.apply(null,t.slice(a,Math.min(a+n,o))):String.fromCharCode.apply(null,t.subarray(a,Math.min(a+n,o)))),a+=n;return r.join("")},stringifyByChar:function(t){for(var e="",n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return e},applyCanBeUsed:{uint8array:function(){try{return s.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(t){return!1}}(),nodebuffer:function(){try{return s.nodebuffer&&1===String.fromCharCode.apply(null,n.allocBuffer(1)).length}catch(t){return!1}}()}};function u(t){var e=65536,n=i.getTypeOf(t),r=!0;if("uint8array"===n?r=o.applyCanBeUsed.uint8array:"nodebuffer"===n&&(r=o.applyCanBeUsed.nodebuffer),r)for(;1<e;)try{return o.stringifyByChunk(t,n,e)}catch(t){e=Math.floor(e/2)}return o.stringifyByChar(t)}function p(t,e){for(var n=0;n<t.length;n++)e[n]=t[n];return e}i.applyFromCharCode=u;var f={};f.string={string:a,array:function(t){return c(t,new Array(t.length))},arraybuffer:function(t){return f.string.uint8array(t).buffer},uint8array:function(t){return c(t,new Uint8Array(t.length))},nodebuffer:function(t){return c(t,n.allocBuffer(t.length))}},f.array={string:u,array:a,arraybuffer:function(t){return new Uint8Array(t).buffer},uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return n.newBufferFrom(t)}},f.arraybuffer={string:function(t){return u(new Uint8Array(t))},array:function(t){return p(new Uint8Array(t),new Array(t.byteLength))},arraybuffer:a,uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return n.newBufferFrom(new Uint8Array(t))}},f.uint8array={string:u,array:function(t){return p(t,new Array(t.length))},arraybuffer:function(t){return t.buffer},uint8array:a,nodebuffer:function(t){return n.newBufferFrom(t)}},f.nodebuffer={string:u,array:function(t){return p(t,new Array(t.length))},arraybuffer:function(t){return f.nodebuffer.uint8array(t).buffer},uint8array:function(t){return p(t,new Uint8Array(t.length))},nodebuffer:a},i.transformTo=function(t,e){if(e=e||"",!t)return e;i.checkSupport(t);var n=i.getTypeOf(e);return f[n][t](e)},i.getTypeOf=function(t){return"string"==typeof t?"string":"[object Array]"===Object.prototype.toString.call(t)?"array":s.nodebuffer&&n.isBuffer(t)?"nodebuffer":s.uint8array&&t instanceof Uint8Array?"uint8array":s.arraybuffer&&t instanceof ArrayBuffer?"arraybuffer":void 0},i.checkSupport=function(t){if(!s[t.toLowerCase()])throw new Error(t+" is not supported by this platform")},i.MAX_VALUE_16BITS=65535,i.MAX_VALUE_32BITS=-1,i.pretty=function(t){for(var e,n="",r=0;r<(t||"").length;r++)n+="\\x"+((e=t.charCodeAt(r))<16?"0":"")+e.toString(16).toUpperCase();return n},i.delay=function(t,e,n){r(function(){t.apply(n||null,e||[])})},i.inherits=function(t,e){function n(){}n.prototype=e.prototype,t.prototype=new n},i.extend=function(){for(var t,e={},n=0;n<arguments.length;n++)for(t in arguments[n])arguments[n].hasOwnProperty(t)&&void 0===e[t]&&(e[t]=arguments[n][t]);return e},i.prepareContent=function(n,t,r,a,o){return l.Promise.resolve(t).then(function(r){return s.blob&&(r instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(r)))&&"undefined"!=typeof FileReader?new l.Promise(function(e,n){var t=new FileReader;t.onload=function(t){e(t.target.result)},t.onerror=function(t){n(t.target.error)},t.readAsArrayBuffer(r)}):r}).then(function(t){var e=i.getTypeOf(t);return e?("arraybuffer"===e?t=i.transformTo("uint8array",t):"string"===e&&(o?t=A.decode(t):r&&!0!==a&&(t=c(e=t,new(s.uint8array?Uint8Array:Array)(e.length)))),t):l.Promise.reject(new Error("Can't read the data of '"+n+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,"set-immediate-shim":54}],33:[function(t,e,n){"use strict";var r=t("./reader/readerFor"),a=t("./utils"),o=t("./signature"),i=t("./zipEntry"),s=(t("./utf8"),t("./support"));function A(t){this.files=[],this.loadOptions=t}A.prototype={checkSignature:function(t){var e;if(!this.reader.readAndCheckSignature(t))throw this.reader.index-=4,e=this.reader.readString(4),new Error("Corrupted zip or bug: unexpected signature ("+a.pretty(e)+", expected "+a.pretty(t)+")")},isSignature:function(t,e){var n=this.reader.index,t=(this.reader.setIndex(t),this.reader.readString(4)===e);return this.reader.setIndex(n),t},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),e=s.uint8array?"uint8array":"array",e=a.transformTo(e,t);this.zipComment=this.loadOptions.decodeFileName(e)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t,e,n,r=this.zip64EndOfCentralSize-44;0<r;)t=this.reader.readInt(2),e=this.reader.readInt(4),n=this.reader.readData(e),this.zip64ExtensibleData[t]={id:t,length:e,value:n}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){for(var t,e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(o.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(o.CENTRAL_FILE_HEADER);)(t=new i({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(o.CENTRAL_DIRECTORY_END);if(t<0)throw this.isSignature(0,o.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(t);var e=t;if(this.checkSignature(o.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===a.MAX_VALUE_16BITS||this.diskWithCentralDirStart===a.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===a.MAX_VALUE_16BITS||this.centralDirRecords===a.MAX_VALUE_16BITS||this.centralDirSize===a.MAX_VALUE_32BITS||this.centralDirOffset===a.MAX_VALUE_32BITS){if(this.zip64=!0,(t=this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,o.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}t=this.centralDirOffset+this.centralDirSize,t=e-(t=this.zip64?t+20+(12+this.zip64EndOfCentralSize):t);if(0<t)this.isSignature(e,o.CENTRAL_FILE_HEADER)||(this.reader.zero=t);else if(t<0)throw new Error("Corrupted zip: missing "+Math.abs(t)+" bytes.")},prepareReader:function(t){this.reader=r(t)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=A},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utf8":31,"./utils":32,"./zipEntry":34}],34:[function(t,e,n){"use strict";var r=t("./reader/readerFor"),a=t("./utils"),o=t("./compressedObject"),i=t("./crc32"),s=t("./utf8"),A=t("./compressions"),l=t("./support");function c(t,e){this.options=t,this.loadOptions=e}c.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(t){var e;if(t.skip(22),this.fileNameLength=t.readInt(2),e=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(e),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(e=function(t){for(var e in A)if(A.hasOwnProperty(e)&&A[e].magic===t)return A[e];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+a.pretty(this.compressionMethod)+" unknown (inner file : "+a.transformTo("string",this.fileName)+")");this.decompressed=new o(this.compressedSize,this.uncompressedSize,this.crc32,e,t.readData(this.compressedSize))},readCentralPart:function(t){this.versionMadeBy=t.readInt(2),t.skip(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4);var e=t.readInt(2);if(this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");t.skip(e),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==t&&(this.dosPermissions=63&this.externalFileAttributes),3==t&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(t){var e;this.extraFields[1]&&(e=r(this.extraFields[1].value),this.uncompressedSize===a.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===a.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===a.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===a.MAX_VALUE_32BITS)&&(this.diskNumberStart=e.readInt(4))},readExtraFields:function(t){var e,n,r,a=t.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});t.index+4<a;)e=t.readInt(2),n=t.readInt(2),r=t.readData(n),this.extraFields[e]={id:e,length:n,value:r};t.setIndex(a)},handleUTF8:function(){var t,e=l.uint8array?"uint8array":"array";this.useUTF8()?(this.fileNameStr=s.utf8decode(this.fileName),this.fileCommentStr=s.utf8decode(this.fileComment)):(null!==(t=this.findExtraFieldUnicodePath())?this.fileNameStr=t:(t=a.transformTo(e,this.fileName),this.fileNameStr=this.loadOptions.decodeFileName(t)),null!==(t=this.findExtraFieldUnicodeComment())?this.fileCommentStr=t:(t=a.transformTo(e,this.fileComment),this.fileCommentStr=this.loadOptions.decodeFileName(t)))},findExtraFieldUnicodePath:function(){var t,e=this.extraFields[28789];return!e||1!==(t=r(e.value)).readInt(1)||i(this.fileName)!==t.readInt(4)?null:s.utf8decode(t.readData(e.length-5))},findExtraFieldUnicodeComment:function(){var t,e=this.extraFields[25461];return!e||1!==(t=r(e.value)).readInt(1)||i(this.fileComment)!==t.readInt(4)?null:s.utf8decode(t.readData(e.length-5))}},e.exports=c},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(t,e,n){"use strict";function r(t,e,n){this.name=t,this.dir=n.dir,this.date=n.date,this.comment=n.comment,this.unixPermissions=n.unixPermissions,this.dosPermissions=n.dosPermissions,this._data=e,this._dataBinary=n.binary,this.options={compression:n.compression,compressionOptions:n.compressionOptions}}var o=t("./stream/StreamHelper"),a=t("./stream/DataWorker"),i=t("./utf8"),s=t("./compressedObject"),A=t("./stream/GenericWorker");r.prototype={internalStream:function(t){var e=null,n="string";try{if(!t)throw new Error("No output type specified.");var r="string"===(n=t.toLowerCase())||"text"===n,a=("binarystring"!==n&&"text"!==n||(n="string"),e=this._decompressWorker(),!this._dataBinary);a&&!r&&(e=e.pipe(new i.Utf8EncodeWorker)),!a&&r&&(e=e.pipe(new i.Utf8DecodeWorker))}catch(t){(e=new A("error")).error(t)}return new o(e,n,"")},async:function(t,e){return this.internalStream(t).accumulate(e)},nodeStream:function(t,e){return this.internalStream(t||"nodebuffer").toNodejsStream(e)},_compressWorker:function(t,e){var n;return this._data instanceof s&&this._data.compression.magic===t.magic?this._data.getCompressedWorker():(n=this._decompressWorker(),this._dataBinary||(n=n.pipe(new i.Utf8EncodeWorker)),s.createWorkerFrom(n,t,e))},_decompressWorker:function(){return this._data instanceof s?this._data.getContentWorker():this._data instanceof A?this._data:new a(this._data)}};for(var l=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],c=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},u=0;u<l.length;u++)r.prototype[l[u]]=c;e.exports=r},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(t,l,e){!function(e){"use strict";var r,t,n,a,o=e.MutationObserver||e.WebKitMutationObserver,i=o?(t=0,o=new o(A),n=e.document.createTextNode(""),o.observe(n,{characterData:!0}),function(){n.data=t=++t%2}):e.setImmediate||void 0===e.MessageChannel?"document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){A(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(A,0)}:((a=new e.MessageChannel).port1.onmessage=A,function(){a.port2.postMessage(0)}),s=[];function A(){var t,e;r=!0;for(var n=s.length;n;){for(e=s,s=[],t=-1;++t<n;)e[t]();n=s.length}r=!1}l.exports=function(t){1!==s.push(t)||r||i()}}.call(this,void 0!==n?n:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(t,e,n){"use strict";var a=t("immediate");function A(){}var l={},o=["REJECTED"],i=["FULFILLED"],r=["PENDING"];function s(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=r,this.queue=[],this.outcome=void 0,t!==A&&f(this,t)}function c(t,e,n){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof n&&(this.onRejected=n,this.callRejected=this.otherCallRejected)}function u(e,n,r){a(function(){var t;try{t=n(r)}catch(t){return l.reject(e,t)}t===e?l.reject(e,new TypeError("Cannot resolve promise with itself")):l.resolve(e,t)})}function p(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function f(e,t){var n=!1;function r(t){n||(n=!0,l.reject(e,t))}function a(t){n||(n=!0,l.resolve(e,t))}var o=d(function(){t(a,r)});"error"===o.status&&r(o.value)}function d(t,e){var n={};try{n.value=t(e),n.status="success"}catch(t){n.status="error",n.value=t}return n}(e.exports=s).prototype.finally=function(e){var n;return"function"!=typeof e?this:(n=this.constructor,this.then(function(t){return n.resolve(e()).then(function(){return t})},function(t){return n.resolve(e()).then(function(){throw t})}))},s.prototype.catch=function(t){return this.then(null,t)},s.prototype.then=function(t,e){var n;return"function"!=typeof t&&this.state===i||"function"!=typeof e&&this.state===o?this:(n=new this.constructor(A),this.state!==r?u(n,this.state===i?t:e,this.outcome):this.queue.push(new c(n,t,e)),n)},c.prototype.callFulfilled=function(t){l.resolve(this.promise,t)},c.prototype.otherCallFulfilled=function(t){u(this.promise,this.onFulfilled,t)},c.prototype.callRejected=function(t){l.reject(this.promise,t)},c.prototype.otherCallRejected=function(t){u(this.promise,this.onRejected,t)},l.resolve=function(t,e){var n=d(p,e);if("error"===n.status)return l.reject(t,n.value);n=n.value;if(n)f(t,n);else{t.state=i,t.outcome=e;for(var r=-1,a=t.queue.length;++r<a;)t.queue[r].callFulfilled(e)}return t},l.reject=function(t,e){t.state=o,t.outcome=e;for(var n=-1,r=t.queue.length;++n<r;)t.queue[n].callRejected(e);return t},s.resolve=function(t){return t instanceof this?t:l.resolve(new this(A),t)},s.reject=function(t){var e=new this(A);return l.reject(e,t)},s.all=function(t){var n=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var r=t.length,a=!1;if(!r)return this.resolve([]);for(var o=new Array(r),i=0,e=-1,s=new this(A);++e<r;)!function(t,e){n.resolve(t).then(function(t){o[e]=t,++i!==r||a||(a=!0,l.resolve(s,o))},function(t){a||(a=!0,l.reject(s,t))})}(t[e],e);return s},s.race=function(t){if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var e=t.length,n=!1;if(!e)return this.resolve([]);for(var r,a=-1,o=new this(A);++a<e;)r=t[a],this.resolve(r).then(function(t){n||(n=!0,l.resolve(o,t))},function(t){n||(n=!0,l.reject(o,t))});return o}},{immediate:36}],38:[function(t,e,n){"use strict";var r={};(0,t("./lib/utils/common").assign)(r,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),e.exports=r},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(t,e,n){"use strict";var i=t("./zlib/deflate"),s=t("./utils/common"),A=t("./utils/strings"),r=t("./zlib/messages"),a=t("./zlib/zstream"),l=Object.prototype.toString;function o(t){if(!(this instanceof o))return new o(t);this.options=s.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var t=this.options,e=(t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0,i.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy));if(0!==e)throw new Error(r[e]);if(t.header&&i.deflateSetHeader(this.strm,t.header),t.dictionary){t="string"==typeof t.dictionary?A.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary;if(0!==(e=i.deflateSetDictionary(this.strm,t)))throw new Error(r[e]);this._dict_set=!0}}function c(t,e){e=new o(e);if(e.push(t,!0),e.err)throw e.msg||r[e.err];return e.result}o.prototype.push=function(t,e){var n,r,a=this.strm,o=this.options.chunkSize;if(this.ended)return!1;r=e===~~e?e:!0===e?4:0,"string"==typeof t?a.input=A.string2buf(t):"[object ArrayBuffer]"===l.call(t)?a.input=new Uint8Array(t):a.input=t,a.next_in=0,a.avail_in=a.input.length;do{if(0===a.avail_out&&(a.output=new s.Buf8(o),a.next_out=0,a.avail_out=o),1!==(n=i.deflate(a,r))&&0!==n)return this.onEnd(n),!(this.ended=!0)}while(0!==a.avail_out&&(0!==a.avail_in||4!==r&&2!==r)||("string"===this.options.to?this.onData(A.buf2binstring(s.shrinkBuf(a.output,a.next_out))):this.onData(s.shrinkBuf(a.output,a.next_out))),(0<a.avail_in||0===a.avail_out)&&1!==n);return 4===r?(n=i.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,0===n):2!==r||(this.onEnd(0),!(a.avail_out=0))},o.prototype.onData=function(t){this.chunks.push(t)},o.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=s.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},n.Deflate=o,n.deflate=c,n.deflateRaw=function(t,e){return(e=e||{}).raw=!0,c(t,e)},n.gzip=function(t,e){return(e=e||{}).gzip=!0,c(t,e)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(t,e,n){"use strict";var u=t("./zlib/inflate"),p=t("./utils/common"),f=t("./utils/strings"),d=t("./zlib/constants"),r=t("./zlib/messages"),a=t("./zlib/zstream"),o=t("./zlib/gzheader"),h=Object.prototype.toString;function i(t){if(!(this instanceof i))return new i(t);this.options=p.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options,t=(e.raw&&0<=e.windowBits&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits)&&(e.windowBits=-15),!(0<=e.windowBits&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),15<e.windowBits&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0,u.inflateInit2(this.strm,e.windowBits));if(t!==d.Z_OK)throw new Error(r[t]);this.header=new o,u.inflateGetHeader(this.strm,this.header)}function s(t,e){e=new i(e);if(e.push(t,!0),e.err)throw e.msg||r[e.err];return e.result}i.prototype.push=function(t,e){var n,r,a,o,i,s=this.strm,A=this.options.chunkSize,l=this.options.dictionary,c=!1;if(this.ended)return!1;r=e===~~e?e:!0===e?d.Z_FINISH:d.Z_NO_FLUSH,"string"==typeof t?s.input=f.binstring2buf(t):"[object ArrayBuffer]"===h.call(t)?s.input=new Uint8Array(t):s.input=t,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new p.Buf8(A),s.next_out=0,s.avail_out=A),(n=u.inflate(s,d.Z_NO_FLUSH))===d.Z_NEED_DICT&&l&&(i="string"==typeof l?f.string2buf(l):"[object ArrayBuffer]"===h.call(l)?new Uint8Array(l):l,n=u.inflateSetDictionary(this.strm,i)),n===d.Z_BUF_ERROR&&!0===c&&(n=d.Z_OK,c=!1),n!==d.Z_STREAM_END&&n!==d.Z_OK)return this.onEnd(n),!(this.ended=!0)}while(!s.next_out||0!==s.avail_out&&n!==d.Z_STREAM_END&&(0!==s.avail_in||r!==d.Z_FINISH&&r!==d.Z_SYNC_FLUSH)||("string"===this.options.to?(i=f.utf8border(s.output,s.next_out),a=s.next_out-i,o=f.buf2string(s.output,i),s.next_out=a,s.avail_out=A-a,a&&p.arraySet(s.output,s.output,i,a,0),this.onData(o)):this.onData(p.shrinkBuf(s.output,s.next_out))),0===s.avail_in&&0===s.avail_out&&(c=!0),(0<s.avail_in||0===s.avail_out)&&n!==d.Z_STREAM_END);return(r=n===d.Z_STREAM_END?d.Z_FINISH:r)===d.Z_FINISH?(n=u.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===d.Z_OK):r!==d.Z_SYNC_FLUSH||(this.onEnd(d.Z_OK),!(s.avail_out=0))},i.prototype.onData=function(t){this.chunks.push(t)},i.prototype.onEnd=function(t){t===d.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=p.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},n.Inflate=i,n.inflate=s,n.inflateRaw=function(t,e){return(e=e||{}).raw=!0,s(t,e)},n.ungzip=s},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(t,e,n){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array,a=(n.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var n=e.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(var r in n)n.hasOwnProperty(r)&&(t[r]=n[r])}}return t},n.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)},{arraySet:function(t,e,n,r,a){if(e.subarray&&t.subarray)t.set(e.subarray(n,n+r),a);else for(var o=0;o<r;o++)t[a+o]=e[n+o]},flattenChunks:function(t){for(var e,n,r,a,o=e=0,i=t.length;o<i;o++)e+=t[o].length;for(a=new Uint8Array(e),o=n=0,i=t.length;o<i;o++)r=t[o],a.set(r,n),n+=r.length;return a}}),o={arraySet:function(t,e,n,r,a){for(var o=0;o<r;o++)t[a+o]=e[n+o]},flattenChunks:function(t){return[].concat.apply([],t)}};n.setTyped=function(t){t?(n.Buf8=Uint8Array,n.Buf16=Uint16Array,n.Buf32=Int32Array,n.assign(n,a)):(n.Buf8=Array,n.Buf16=Array,n.Buf32=Array,n.assign(n,o))},n.setTyped(r)},{}],42:[function(t,e,n){"use strict";var A=t("./common"),a=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(t){a=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){o=!1}for(var l=new A.Buf8(256),r=0;r<256;r++)l[r]=252<=r?6:248<=r?5:240<=r?4:224<=r?3:192<=r?2:1;function c(t,e){if(e<65537&&(t.subarray&&o||!t.subarray&&a))return String.fromCharCode.apply(null,A.shrinkBuf(t,e));for(var n="",r=0;r<e;r++)n+=String.fromCharCode(t[r]);return n}l[254]=l[254]=1,n.string2buf=function(t){for(var e,n,r,a,o=t.length,i=0,s=0;s<o;s++)55296==(64512&(n=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=t.charCodeAt(s+1)))&&(n=65536+(n-55296<<10)+(r-56320),s++),i+=n<128?1:n<2048?2:n<65536?3:4;for(e=new A.Buf8(i),s=a=0;a<i;s++)55296==(64512&(n=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=t.charCodeAt(s+1)))&&(n=65536+(n-55296<<10)+(r-56320),s++),n<128?e[a++]=n:(n<2048?e[a++]=192|n>>>6:(n<65536?e[a++]=224|n>>>12:(e[a++]=240|n>>>18,e[a++]=128|n>>>12&63),e[a++]=128|n>>>6&63),e[a++]=128|63&n);return e},n.buf2binstring=function(t){return c(t,t.length)},n.binstring2buf=function(t){for(var e=new A.Buf8(t.length),n=0,r=e.length;n<r;n++)e[n]=t.charCodeAt(n);return e},n.buf2string=function(t,e){for(var n,r,a,o=e||t.length,i=new Array(2*o),s=n=0;s<o;)if((r=t[s++])<128)i[n++]=r;else if(4<(a=l[r]))i[n++]=65533,s+=a-1;else{for(r&=2===a?31:3===a?15:7;1<a&&s<o;)r=r<<6|63&t[s++],a--;1<a?i[n++]=65533:r<65536?i[n++]=r:(r-=65536,i[n++]=55296|r>>10&1023,i[n++]=56320|1023&r)}return c(i,n)},n.utf8border=function(t,e){for(var n=(e=(e=e||t.length)>t.length?t.length:e)-1;0<=n&&128==(192&t[n]);)n--;return!(n<0)&&0!==n&&n+l[t[n]]>e?n:e}},{"./common":41}],43:[function(t,e,n){"use strict";e.exports=function(t,e,n,r){for(var a=65535&t|0,o=t>>>16&65535|0,i=0;0!==n;){for(n-=i=2e3<n?2e3:n;o=o+(a=a+e[r++]|0)|0,--i;);a%=65521,o%=65521}return a|o<<16|0}},{}],44:[function(t,e,n){"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(t,e,n){"use strict";var s=function(){for(var t=[],e=0;e<256;e++){for(var n=e,r=0;r<8;r++)n=1&n?3988292384^n>>>1:n>>>1;t[e]=n}return t}();e.exports=function(t,e,n,r){var a=s,o=r+n;t^=-1;for(var i=r;i<o;i++)t=t>>>8^a[255&(t^e[i])];return-1^t}},{}],46:[function(t,R,e){"use strict";var s,u=t("../utils/common"),A=t("./trees"),p=t("./adler32"),f=t("./crc32"),n=t("./messages"),l=0,c=0,d=-2,r=2,h=8,a=286,o=30,i=19,O=2*a+1,M=15,g=3,m=258,v=m+g+1,y=42,b=113;function w(t,e){return t.msg=n[e],e}function x(t){return(t<<1)-(4<t?9:0)}function C(t){for(var e=t.length;0<=--e;)t[e]=0}function P(t){var e=t.state,n=e.pending;0!==(n=n>t.avail_out?t.avail_out:n)&&(u.arraySet(t.output,e.pending_buf,e.pending_out,n,t.next_out),t.next_out+=n,e.pending_out+=n,t.total_out+=n,t.avail_out-=n,e.pending-=n,0===e.pending)&&(e.pending_out=0)}function S(t,e){A._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,P(t.strm)}function L(t,e){t.pending_buf[t.pending++]=e}function E(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function T(t,e){var n,r,a=t.max_chain_length,o=t.strstart,i=t.prev_length,s=t.nice_match,A=t.strstart>t.w_size-v?t.strstart-(t.w_size-v):0,l=t.window,c=t.w_mask,u=t.prev,p=t.strstart+m,f=l[o+i-1],d=l[o+i];t.prev_length>=t.good_match&&(a>>=2),s>t.lookahead&&(s=t.lookahead);do{if(l[(n=e)+i]===d&&l[n+i-1]===f&&l[n]===l[o]&&l[++n]===l[o+1]){for(o+=2,n++;l[++o]===l[++n]&&l[++o]===l[++n]&&l[++o]===l[++n]&&l[++o]===l[++n]&&l[++o]===l[++n]&&l[++o]===l[++n]&&l[++o]===l[++n]&&l[++o]===l[++n]&&o<p;);if(r=m-(p-o),o=p-m,i<r){if(t.match_start=e,s<=(i=r))break;f=l[o+i-1],d=l[o+i]}}}while((e=u[e&c])>A&&0!=--a);return i<=t.lookahead?i:t.lookahead}function B(t){var e,n,r,a,o,i,s,A,l,c=t.w_size;do{if(A=t.window_size-t.lookahead-t.strstart,t.strstart>=c+(c-v)){for(u.arraySet(t.window,t.window,c,c,0),t.match_start-=c,t.strstart-=c,t.block_start-=c,e=n=t.hash_size;r=t.head[--e],t.head[e]=c<=r?r-c:0,--n;);for(e=n=c;r=t.prev[--e],t.prev[e]=c<=r?r-c:0,--n;);A+=c}if(0===t.strm.avail_in)break;if(o=t.strm,i=t.window,s=t.strstart+t.lookahead,l=void 0,n=0===(l=(A=A)<(l=o.avail_in)?A:l)?0:(o.avail_in-=l,u.arraySet(i,o.input,o.next_in,l,s),1===o.state.wrap?o.adler=p(o.adler,i,l,s):2===o.state.wrap&&(o.adler=f(o.adler,i,l,s)),o.next_in+=l,o.total_in+=l,l),t.lookahead+=n,t.lookahead+t.insert>=g)for(a=t.strstart-t.insert,t.ins_h=t.window[a],t.ins_h=(t.ins_h<<t.hash_shift^t.window[a+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[a+g-1])&t.hash_mask,t.prev[a&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=a,a++,t.insert--,!(t.lookahead+t.insert<g)););}while(t.lookahead<v&&0!==t.strm.avail_in)}function D(t,e){for(var n,r;;){if(t.lookahead<v){if(B(t),t.lookahead<v&&e===l)return 1;if(0===t.lookahead)break}if(n=0,t.lookahead>=g&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+g-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==n&&t.strstart-n<=t.w_size-v&&(t.match_length=T(t,n)),t.match_length>=g)if(r=A._tr_tally(t,t.strstart-t.match_start,t.match_length-g),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=g){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+g-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else r=A._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(r&&(S(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<g-1?t.strstart:g-1,4===e?(S(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(S(t,!1),0===t.strm.avail_out)?1:2}function _(t,e){for(var n,r,a;;){if(t.lookahead<v){if(B(t),t.lookahead<v&&e===l)return 1;if(0===t.lookahead)break}if(n=0,t.lookahead>=g&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+g-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=g-1,0!==n&&t.prev_length<t.max_lazy_match&&t.strstart-n<=t.w_size-v&&(t.match_length=T(t,n),t.match_length<=5)&&(1===t.strategy||t.match_length===g&&4096<t.strstart-t.match_start)&&(t.match_length=g-1),t.prev_length>=g&&t.match_length<=t.prev_length){for(a=t.strstart+t.lookahead-g,r=A._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-g),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=a&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+g-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=g-1,t.strstart++,r&&(S(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((r=A._tr_tally(t,0,t.window[t.strstart-1]))&&S(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(r=A._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<g-1?t.strstart:g-1,4===e?(S(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(S(t,!1),0===t.strm.avail_out)?1:2}function k(t,e,n,r,a){this.good_length=t,this.max_lazy=e,this.nice_length=n,this.max_chain=r,this.func=a}function z(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=h,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new u.Buf16(2*O),this.dyn_dtree=new u.Buf16(2*(2*o+1)),this.bl_tree=new u.Buf16(2*(2*i+1)),C(this.dyn_ltree),C(this.dyn_dtree),C(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new u.Buf16(M+1),this.heap=new u.Buf16(2*a+1),C(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new u.Buf16(2*a+1),C(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function N(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=r,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?y:b,t.adler=2===e.wrap?0:1,e.last_flush=l,A._tr_init(e),c):w(t,d)}function F(t){var e=N(t);return e===c&&((t=t.state).window_size=2*t.w_size,C(t.head),t.max_lazy_match=s[t.level].max_lazy,t.good_match=s[t.level].good_length,t.nice_match=s[t.level].nice_length,t.max_chain_length=s[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=g-1,t.match_available=0,t.ins_h=0),e}function I(t,e,n,r,a,o){if(!t)return d;var i=1;if(-1===e&&(e=6),r<0?(i=0,r=-r):15<r&&(i=2,r-=16),a<1||9<a||n!==h||r<8||15<r||e<0||9<e||o<0||4<o)return w(t,d);8===r&&(r=9);var s=new z;return(t.state=s).strm=t,s.wrap=i,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=a+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+g-1)/g),s.window=new u.Buf8(2*s.w_size),s.head=new u.Buf16(s.hash_size),s.prev=new u.Buf16(s.w_size),s.lit_bufsize=1<<a+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new u.Buf8(s.pending_buf_size),s.d_buf=+s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=e,s.strategy=o,s.method=n,F(t)}s=[new k(0,0,0,0,function(t,e){var n=65535;for(n>t.pending_buf_size-5&&(n=t.pending_buf_size-5);;){if(t.lookahead<=1){if(B(t),0===t.lookahead&&e===l)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var r=t.block_start+n;if((0===t.strstart||t.strstart>=r)&&(t.lookahead=t.strstart-r,t.strstart=r,S(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-v&&(S(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(S(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(S(t,!1),t.strm.avail_out),1)}),new k(4,4,8,4,D),new k(4,5,16,8,D),new k(4,6,32,32,D),new k(4,4,16,16,_),new k(8,16,32,32,_),new k(8,16,128,128,_),new k(8,32,128,256,_),new k(32,128,258,1024,_),new k(32,258,258,4096,_)],e.deflateInit=function(t,e){return I(t,e,h,15,8,0)},e.deflateInit2=I,e.deflateReset=F,e.deflateResetKeep=N,e.deflateSetHeader=function(t,e){return!t||!t.state||2!==t.state.wrap?d:(t.state.gzhead=e,c)},e.deflate=function(t,e){var n,r,a,o;if(!t||!t.state||5<e||e<0)return t?w(t,d):d;if(r=t.state,!t.output||!t.input&&0!==t.avail_in||666===r.status&&4!==e)return w(t,0===t.avail_out?-5:d);if(r.strm=t,n=r.last_flush,r.last_flush=e,r.status===y&&(2===r.wrap?(t.adler=0,L(r,31),L(r,139),L(r,8),r.gzhead?(L(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),L(r,255&r.gzhead.time),L(r,r.gzhead.time>>8&255),L(r,r.gzhead.time>>16&255),L(r,r.gzhead.time>>24&255),L(r,9===r.level?2:2<=r.strategy||r.level<2?4:0),L(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(L(r,255&r.gzhead.extra.length),L(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(t.adler=f(t.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(L(r,0),L(r,0),L(r,0),L(r,0),L(r,0),L(r,9===r.level?2:2<=r.strategy||r.level<2?4:0),L(r,3),r.status=b)):(i=h+(r.w_bits-8<<4)<<8,i|=(2<=r.strategy||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(i|=32),i+=31-i%31,r.status=b,E(r,i),0!==r.strstart&&(E(r,t.adler>>>16),E(r,65535&t.adler)),t.adler=1)),69===r.status)if(r.gzhead.extra){for(a=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>a&&(t.adler=f(t.adler,r.pending_buf,r.pending-a,a)),P(t),a=r.pending,r.pending!==r.pending_buf_size));)L(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>a&&(t.adler=f(t.adler,r.pending_buf,r.pending-a,a)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(t.adler=f(t.adler,r.pending_buf,r.pending-a,a)),P(t),a=r.pending,r.pending===r.pending_buf_size)){o=1;break}}while(o=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,L(r,o),0!==o);r.gzhead.hcrc&&r.pending>a&&(t.adler=f(t.adler,r.pending_buf,r.pending-a,a)),0===o&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){a=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>a&&(t.adler=f(t.adler,r.pending_buf,r.pending-a,a)),P(t),a=r.pending,r.pending===r.pending_buf_size)){o=1;break}}while(o=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,L(r,o),0!==o);r.gzhead.hcrc&&r.pending>a&&(t.adler=f(t.adler,r.pending_buf,r.pending-a,a)),0===o&&(r.status=103)}else r.status=103;if(103===r.status&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&P(t),r.pending+2<=r.pending_buf_size&&(L(r,255&t.adler),L(r,t.adler>>8&255),t.adler=0,r.status=b)):r.status=b),0!==r.pending){if(P(t),0===t.avail_out)return r.last_flush=-1,c}else if(0===t.avail_in&&x(e)<=x(n)&&4!==e)return w(t,-5);if(666===r.status&&0!==t.avail_in)return w(t,-5);if(0!==t.avail_in||0!==r.lookahead||e!==l&&666!==r.status){var i=2===r.strategy?function(t,e){for(var n;;){if(0===t.lookahead&&(B(t),0===t.lookahead)){if(e===l)return 1;break}if(t.match_length=0,n=A._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,n&&(S(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(S(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(S(t,!1),0===t.strm.avail_out)?1:2}(r,e):3===r.strategy?function(t,e){for(var n,r,a,o,i=t.window;;){if(t.lookahead<=m){if(B(t),t.lookahead<=m&&e===l)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=g&&0<t.strstart&&(r=i[a=t.strstart-1])===i[++a]&&r===i[++a]&&r===i[++a]){for(o=t.strstart+m;r===i[++a]&&r===i[++a]&&r===i[++a]&&r===i[++a]&&r===i[++a]&&r===i[++a]&&r===i[++a]&&r===i[++a]&&a<o;);t.match_length=m-(o-a),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=g?(n=A._tr_tally(t,1,t.match_length-g),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(n=A._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),n&&(S(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(S(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(S(t,!1),0===t.strm.avail_out)?1:2}(r,e):s[r.level].func(r,e);if(3!==i&&4!==i||(r.status=666),1===i||3===i)return 0===t.avail_out&&(r.last_flush=-1),c;if(2===i&&(1===e?A._tr_align(r):5!==e&&(A._tr_stored_block(r,0,0,!1),3===e)&&(C(r.head),0===r.lookahead)&&(r.strstart=0,r.block_start=0,r.insert=0),P(t),0===t.avail_out))return r.last_flush=-1,c}return 4!==e||!(r.wrap<=0)&&(2===r.wrap?(L(r,255&t.adler),L(r,t.adler>>8&255),L(r,t.adler>>16&255),L(r,t.adler>>24&255),L(r,255&t.total_in),L(r,t.total_in>>8&255),L(r,t.total_in>>16&255),L(r,t.total_in>>24&255)):(E(r,t.adler>>>16),E(r,65535&t.adler)),P(t),0<r.wrap&&(r.wrap=-r.wrap),0!==r.pending)?c:1},e.deflateEnd=function(t){var e;return t&&t.state?(e=t.state.status)!==y&&69!==e&&73!==e&&91!==e&&103!==e&&e!==b&&666!==e?w(t,d):(t.state=null,e===b?w(t,-3):c):d},e.deflateSetDictionary=function(t,e){var n,r,a,o,i,s,A,l=e.length;if(!t||!t.state)return d;if(2===(o=(n=t.state).wrap)||1===o&&n.status!==y||n.lookahead)return d;for(1===o&&(t.adler=p(t.adler,e,l,0)),n.wrap=0,l>=n.w_size&&(0===o&&(C(n.head),n.strstart=0,n.block_start=0,n.insert=0),A=new u.Buf8(n.w_size),u.arraySet(A,e,l-n.w_size,n.w_size,0),e=A,l=n.w_size),A=t.avail_in,i=t.next_in,s=t.input,t.avail_in=l,t.next_in=0,t.input=e,B(n);n.lookahead>=g;){for(r=n.strstart,a=n.lookahead-(g-1);n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+g-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++,--a;);n.strstart=r,n.lookahead=g-1,B(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=g-1,n.match_available=0,t.next_in=i,t.input=s,t.avail_in=A,n.wrap=o,c},e.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(t,e,n){"use strict";e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(t,e,n){"use strict";e.exports=function(t,e){var n,r,a,o,i,s,A=t.state,l=t.next_in,c=t.input,u=l+(t.avail_in-5),p=t.next_out,f=t.output,d=p-(e-t.avail_out),h=p+(t.avail_out-257),g=A.dmax,m=A.wsize,v=A.whave,y=A.wnext,b=A.window,w=A.hold,x=A.bits,C=A.lencode,P=A.distcode,S=(1<<A.lenbits)-1,L=(1<<A.distbits)-1;t:do{for(x<15&&(w+=c[l++]<<x,x+=8,w+=c[l++]<<x,x+=8),n=C[w&S];;){if(w>>>=r=n>>>24,x-=r,0==(r=n>>>16&255))f[p++]=65535&n;else{if(!(16&r)){if(0==(64&r)){n=C[(65535&n)+(w&(1<<r)-1)];continue}if(32&r){A.mode=12;break t}t.msg="invalid literal/length code",A.mode=30;break t}for(a=65535&n,(r&=15)&&(x<r&&(w+=c[l++]<<x,x+=8),a+=w&(1<<r)-1,w>>>=r,x-=r),x<15&&(w+=c[l++]<<x,x+=8,w+=c[l++]<<x,x+=8),n=P[w&L];;){if(w>>>=r=n>>>24,x-=r,!(16&(r=n>>>16&255))){if(0==(64&r)){n=P[(65535&n)+(w&(1<<r)-1)];continue}t.msg="invalid distance code",A.mode=30;break t}if(o=65535&n,x<(r&=15)&&(w+=c[l++]<<x,(x+=8)<r)&&(w+=c[l++]<<x,x+=8),g<(o+=w&(1<<r)-1)){t.msg="invalid distance too far back",A.mode=30;break t}if(w>>>=r,x-=r,(r=p-d)<o){if(v<(r=o-r)&&A.sane){t.msg="invalid distance too far back",A.mode=30;break t}if(s=b,(i=0)===y){if(i+=m-r,r<a){for(a-=r;f[p++]=b[i++],--r;);i=p-o,s=f}}else if(y<r){if(i+=m+y-r,(r-=y)<a){for(a-=r;f[p++]=b[i++],--r;);if(i=0,y<a){for(a-=r=y;f[p++]=b[i++],--r;);i=p-o,s=f}}}else if(i+=y-r,r<a){for(a-=r;f[p++]=b[i++],--r;);i=p-o,s=f}for(;2<a;)f[p++]=s[i++],f[p++]=s[i++],f[p++]=s[i++],a-=3;a&&(f[p++]=s[i++],1<a)&&(f[p++]=s[i++])}else{for(i=p-o;f[p++]=f[i++],f[p++]=f[i++],f[p++]=f[i++],2<(a-=3););a&&(f[p++]=f[i++],1<a)&&(f[p++]=f[i++])}break}}break}}while(l<u&&p<h);l-=a=x>>3,w&=(1<<(x-=a<<3))-1,t.next_in=l,t.next_out=p,t.avail_in=l<u?u-l+5:5-(l-u),t.avail_out=p<h?h-p+257:257-(p-h),A.hold=w,A.bits=x}},{}],49:[function(t,e,n){"use strict";var _=t("../utils/common"),k=t("./adler32"),N=t("./crc32"),F=t("./inffast"),I=t("./inftrees"),R=0,O=-2,M=1,r=852,a=592;function z(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function o(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new _.Buf16(320),this.work=new _.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function i(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=M,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new _.Buf32(r),e.distcode=e.distdyn=new _.Buf32(a),e.sane=1,e.back=-1,R):O}function s(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,i(t)):O}function A(t,e){var n,r;return!t||!t.state||(r=t.state,e<0?(n=0,e=-e):(n=1+(e>>4),e<48&&(e&=15)),e&&(e<8||15<e))?O:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=n,r.wbits=e,s(t))}function l(t,e){var n;return t?(n=new o,(t.state=n).window=null,(n=A(t,e))!==R&&(t.state=null),n):O}var U,j,G=!0;function Q(t,e,n,r){var a,t=t.state;return null===t.window&&(t.wsize=1<<t.wbits,t.wnext=0,t.whave=0,t.window=new _.Buf8(t.wsize)),r>=t.wsize?(_.arraySet(t.window,e,n-t.wsize,t.wsize,0),t.wnext=0,t.whave=t.wsize):(r<(a=t.wsize-t.wnext)&&(a=r),_.arraySet(t.window,e,n-r,a,t.wnext),(r-=a)?(_.arraySet(t.window,e,n-r,r,0),t.wnext=r,t.whave=t.wsize):(t.wnext+=a,t.wnext===t.wsize&&(t.wnext=0),t.whave<t.wsize&&(t.whave+=a))),0}n.inflateReset=s,n.inflateReset2=A,n.inflateResetKeep=i,n.inflateInit=function(t){return l(t,15)},n.inflateInit2=l,n.inflate=function(t,e){var n,r,a,o,i,s,A,l,c,u,p,f,d,h,g,m,v,y,b,w,x,C,P,S,L=0,E=new _.Buf8(4),T=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return O;12===(n=t.state).mode&&(n.mode=13),i=t.next_out,a=t.output,A=t.avail_out,o=t.next_in,r=t.input,s=t.avail_in,l=n.hold,c=n.bits,u=s,p=A,C=R;t:for(;;)switch(n.mode){case M:if(0===n.wrap)n.mode=13;else{for(;c<16;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(2&n.wrap&&35615===l)E[n.check=0]=255&l,E[1]=l>>>8&255,n.check=N(n.check,E,2,0),c=l=0,n.mode=2;else if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&l)<<8)+(l>>8))%31)t.msg="incorrect header check",n.mode=30;else if(8!=(15&l))t.msg="unknown compression method",n.mode=30;else{if(c-=4,x=8+(15&(l>>>=4)),0===n.wbits)n.wbits=x;else if(x>n.wbits){t.msg="invalid window size",n.mode=30;break}n.dmax=1<<x,t.adler=n.check=1,n.mode=512&l?10:12,c=l=0}}break;case 2:for(;c<16;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(n.flags=l,8!=(255&n.flags)){t.msg="unknown compression method",n.mode=30;break}if(57344&n.flags){t.msg="unknown header flags set",n.mode=30;break}n.head&&(n.head.text=l>>8&1),512&n.flags&&(E[0]=255&l,E[1]=l>>>8&255,n.check=N(n.check,E,2,0)),c=l=0,n.mode=3;case 3:for(;c<32;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}n.head&&(n.head.time=l),512&n.flags&&(E[0]=255&l,E[1]=l>>>8&255,E[2]=l>>>16&255,E[3]=l>>>24&255,n.check=N(n.check,E,4,0)),c=l=0,n.mode=4;case 4:for(;c<16;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}n.head&&(n.head.xflags=255&l,n.head.os=l>>8),512&n.flags&&(E[0]=255&l,E[1]=l>>>8&255,n.check=N(n.check,E,2,0)),c=l=0,n.mode=5;case 5:if(1024&n.flags){for(;c<16;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}n.length=l,n.head&&(n.head.extra_len=l),512&n.flags&&(E[0]=255&l,E[1]=l>>>8&255,n.check=N(n.check,E,2,0)),c=l=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((f=s<(f=n.length)?s:f)&&(n.head&&(x=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),_.arraySet(n.head.extra,r,o,f,x)),512&n.flags&&(n.check=N(n.check,r,f,o)),s-=f,o+=f,n.length-=f),n.length))break t;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===s)break t;for(f=0;x=r[o+f++],n.head&&x&&n.length<65536&&(n.head.name+=String.fromCharCode(x)),x&&f<s;);if(512&n.flags&&(n.check=N(n.check,r,f,o)),s-=f,o+=f,x)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===s)break t;for(f=0;x=r[o+f++],n.head&&x&&n.length<65536&&(n.head.comment+=String.fromCharCode(x)),x&&f<s;);if(512&n.flags&&(n.check=N(n.check,r,f,o)),s-=f,o+=f,x)break t}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;c<16;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(l!==(65535&n.check)){t.msg="header crc mismatch",n.mode=30;break}c=l=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),t.adler=n.check=0,n.mode=12;break;case 10:for(;c<32;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}t.adler=n.check=z(l),c=l=0,n.mode=11;case 11:if(0===n.havedict)return t.next_out=i,t.avail_out=A,t.next_in=o,t.avail_in=s,n.hold=l,n.bits=c,2;t.adler=n.check=1,n.mode=12;case 12:if(5===e||6===e)break t;case 13:if(n.last)l>>>=7&c,c-=7&c,n.mode=27;else{for(;c<3;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}switch(n.last=1&l,--c,3&(l>>>=1)){case 0:n.mode=14;break;case 1:B=D=void 0;var B,D=n;if(G){for(U=new _.Buf32(512),j=new _.Buf32(32),B=0;B<144;)D.lens[B++]=8;for(;B<256;)D.lens[B++]=9;for(;B<280;)D.lens[B++]=7;for(;B<288;)D.lens[B++]=8;for(I(1,D.lens,0,288,U,0,D.work,{bits:9}),B=0;B<32;)D.lens[B++]=5;I(2,D.lens,0,32,j,0,D.work,{bits:5}),G=!1}if(D.lencode=U,D.lenbits=9,D.distcode=j,D.distbits=5,n.mode=20,6!==e)break;l>>>=2,c-=2;break t;case 2:n.mode=17;break;case 3:t.msg="invalid block type",n.mode=30}l>>>=2,c-=2}break;case 14:for(l>>>=7&c,c-=7&c;c<32;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if((65535&l)!=(l>>>16^65535)){t.msg="invalid stored block lengths",n.mode=30;break}if(n.length=65535&l,c=l=0,n.mode=15,6===e)break t;case 15:n.mode=16;case 16:if(f=n.length){if(0===(f=A<(f=s<f?s:f)?A:f))break t;_.arraySet(a,r,o,f,i),s-=f,o+=f,A-=f,i+=f,n.length-=f}else n.mode=12;break;case 17:for(;c<14;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(n.nlen=257+(31&l),l>>>=5,c-=5,n.ndist=1+(31&l),l>>>=5,c-=5,n.ncode=4+(15&l),l>>>=4,c-=4,286<n.nlen||30<n.ndist){t.msg="too many length or distance symbols",n.mode=30;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;c<3;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}n.lens[T[n.have++]]=7&l,l>>>=3,c-=3}for(;n.have<19;)n.lens[T[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,P={bits:n.lenbits},C=I(0,n.lens,0,19,n.lencode,0,n.work,P),n.lenbits=P.bits,C){t.msg="invalid code lengths set",n.mode=30;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;m=(L=n.lencode[l&(1<<n.lenbits)-1])>>>16&255,v=65535&L,!((g=L>>>24)<=c);){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(v<16)l>>>=g,c-=g,n.lens[n.have++]=v;else{if(16===v){for(S=g+2;c<S;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(l>>>=g,c-=g,0===n.have){t.msg="invalid bit length repeat",n.mode=30;break}x=n.lens[n.have-1],f=3+(3&l),l>>>=2,c-=2}else if(17===v){for(S=g+3;c<S;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}x=0,f=3+(7&(l>>>=g)),l>>>=3,c=c-g-3}else{for(S=g+7;c<S;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}x=0,f=11+(127&(l>>>=g)),l>>>=7,c=c-g-7}if(n.have+f>n.nlen+n.ndist){t.msg="invalid bit length repeat",n.mode=30;break}for(;f--;)n.lens[n.have++]=x}}if(30===n.mode)break;if(0===n.lens[256]){t.msg="invalid code -- missing end-of-block",n.mode=30;break}if(n.lenbits=9,P={bits:n.lenbits},C=I(1,n.lens,0,n.nlen,n.lencode,0,n.work,P),n.lenbits=P.bits,C){t.msg="invalid literal/lengths set",n.mode=30;break}if(n.distbits=6,n.distcode=n.distdyn,P={bits:n.distbits},C=I(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,P),n.distbits=P.bits,C){t.msg="invalid distances set",n.mode=30;break}if(n.mode=20,6===e)break t;case 20:n.mode=21;case 21:if(6<=s&&258<=A){t.next_out=i,t.avail_out=A,t.next_in=o,t.avail_in=s,n.hold=l,n.bits=c,F(t,p),i=t.next_out,a=t.output,A=t.avail_out,o=t.next_in,r=t.input,s=t.avail_in,l=n.hold,c=n.bits,12===n.mode&&(n.back=-1);break}for(n.back=0;m=(L=n.lencode[l&(1<<n.lenbits)-1])>>>16&255,v=65535&L,!((g=L>>>24)<=c);){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(m&&0==(240&m)){for(y=g,b=m,w=v;m=(L=n.lencode[w+((l&(1<<y+b)-1)>>y)])>>>16&255,v=65535&L,!(y+(g=L>>>24)<=c);){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}l>>>=y,c-=y,n.back+=y}if(l>>>=g,c-=g,n.back+=g,n.length=v,0===m){n.mode=26;break}if(32&m){n.back=-1,n.mode=12;break}if(64&m){t.msg="invalid literal/length code",n.mode=30;break}n.extra=15&m,n.mode=22;case 22:if(n.extra){for(S=n.extra;c<S;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}n.length+=l&(1<<n.extra)-1,l>>>=n.extra,c-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;m=(L=n.distcode[l&(1<<n.distbits)-1])>>>16&255,v=65535&L,!((g=L>>>24)<=c);){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(0==(240&m)){for(y=g,b=m,w=v;m=(L=n.distcode[w+((l&(1<<y+b)-1)>>y)])>>>16&255,v=65535&L,!(y+(g=L>>>24)<=c);){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}l>>>=y,c-=y,n.back+=y}if(l>>>=g,c-=g,n.back+=g,64&m){t.msg="invalid distance code",n.mode=30;break}n.offset=v,n.extra=15&m,n.mode=24;case 24:if(n.extra){for(S=n.extra;c<S;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}n.offset+=l&(1<<n.extra)-1,l>>>=n.extra,c-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){t.msg="invalid distance too far back",n.mode=30;break}n.mode=25;case 25:if(0===A)break t;if(n.offset>(f=p-A)){if((f=n.offset-f)>n.whave&&n.sane){t.msg="invalid distance too far back",n.mode=30;break}d=f>n.wnext?(f-=n.wnext,n.wsize-f):n.wnext-f,f>n.length&&(f=n.length),h=n.window}else h=a,d=i-n.offset,f=n.length;for(A-=f=A<f?A:f,n.length-=f;a[i++]=h[d++],--f;);0===n.length&&(n.mode=21);break;case 26:if(0===A)break t;a[i++]=n.length,A--,n.mode=21;break;case 27:if(n.wrap){for(;c<32;){if(0===s)break t;s--,l|=r[o++]<<c,c+=8}if(p-=A,t.total_out+=p,n.total+=p,p&&(t.adler=n.check=(n.flags?N:k)(n.check,a,p,i-p)),p=A,(n.flags?l:z(l))!==n.check){t.msg="incorrect data check",n.mode=30;break}c=l=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;c<32;){if(0===s)break t;s--,l+=r[o++]<<c,c+=8}if(l!==(4294967295&n.total)){t.msg="incorrect length check",n.mode=30;break}c=l=0}n.mode=29;case 29:C=1;break t;case 30:C=-3;break t;case 31:return-4;default:return O}return t.next_out=i,t.avail_out=A,t.next_in=o,t.avail_in=s,n.hold=l,n.bits=c,(n.wsize||p!==t.avail_out&&n.mode<30&&(n.mode<27||4!==e))&&Q(t,t.output,t.next_out,p-t.avail_out)?(n.mode=31,-4):(u-=t.avail_in,p-=t.avail_out,t.total_in+=u,t.total_out+=p,n.total+=p,n.wrap&&p&&(t.adler=n.check=(n.flags?N:k)(n.check,a,p,t.next_out-p)),t.data_type=n.bits+(n.last?64:0)+(12===n.mode?128:0)+(20===n.mode||15===n.mode?256:0),C=(0==u&&0===p||4===e)&&C===R?-5:C)},n.inflateEnd=function(t){var e;return t&&t.state?((e=t.state).window&&(e.window=null),t.state=null,R):O},n.inflateGetHeader=function(t,e){return!t||!t.state||0==(2&(t=t.state).wrap)?O:((t.head=e).done=!1,R)},n.inflateSetDictionary=function(t,e){var n,r=e.length;return!t||!t.state||0!==(n=t.state).wrap&&11!==n.mode?O:11===n.mode&&k(1,e,r,0)!==n.check?-3:Q(t,e,r,r)?(n.mode=31,-4):(n.havedict=1,R)},n.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(t,e,n){"use strict";var F=t("../utils/common"),I=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],R=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],O=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],M=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(t,e,n,r,a,o,i,s){for(var A,l,c,u,p,f,d,h,g,m=s.bits,v=0,y=0,b=0,w=0,x=0,C=0,P=0,S=0,L=0,E=0,T=null,B=0,D=new F.Buf16(16),_=new F.Buf16(16),k=null,N=0,v=0;v<=15;v++)D[v]=0;for(y=0;y<r;y++)D[e[n+y]]++;for(x=m,w=15;1<=w&&0===D[w];w--);if(w<x&&(x=w),0===w)a[o++]=20971520,a[o++]=20971520,s.bits=1;else{for(b=1;b<w&&0===D[b];b++);for(x<b&&(x=b),v=S=1;v<=15;v++)if((S=(S<<1)-D[v])<0)return-1;if(0<S&&(0===t||1!==w))return-1;for(_[1]=0,v=1;v<15;v++)_[v+1]=_[v]+D[v];for(y=0;y<r;y++)0!==e[n+y]&&(i[_[e[n+y]]++]=y);if(f=0===t?(T=k=i,19):1===t?(T=I,B-=257,k=R,N-=257,256):(T=O,k=M,-1),v=b,p=o,P=y=E=0,c=-1,u=(L=1<<(C=x))-1,1===t&&852<L||2===t&&592<L)return 1;for(;;){for(g=i[y]<f?(h=0,i[y]):i[y]>f?(h=k[N+i[y]],T[B+i[y]]):(h=96,0),A=1<<(d=v-P),b=l=1<<C;a[p+(E>>P)+(l-=A)]=d<<24|h<<16|g|0,0!==l;);for(A=1<<v-1;E&A;)A>>=1;if(0!==A?E=(E&A-1)+A:E=0,y++,0==--D[v]){if(v===w)break;v=e[n+i[y]]}if(x<v&&(E&u)!==c){for(p+=b,S=1<<(C=v-(P=0===P?x:P));C+P<w&&!((S-=D[C+P])<=0);)C++,S<<=1;if(L+=1<<C,1===t&&852<L||2===t&&592<L)return 1;a[c=E&u]=x<<24|C<<16|p-o|0}}0!==E&&(a[p+E]=v-P<<24|64<<16|0),s.bits=x}return 0}},{"../utils/common":41}],51:[function(t,e,n){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(t,R,e){"use strict";var a=t("../utils/common");function n(t){for(var e=t.length;0<=--e;)t[e]=0}var r=16,A=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],l=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],s=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],c=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],u=new Array(576),p=(n(u),new Array(60)),f=(n(p),new Array(512)),d=(n(f),new Array(256)),h=(n(d),new Array(29));n(h);var g,m,v,y=new Array(30);function b(t,e,n,r,a){this.static_tree=t,this.extra_bits=e,this.extra_base=n,this.elems=r,this.max_length=a,this.has_stree=t&&t.length}function w(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function x(t){return t<256?f[t]:f[256+(t>>>7)]}function o(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function C(t,e,n){t.bi_valid>r-n?(t.bi_buf|=e<<t.bi_valid&65535,o(t,t.bi_buf),t.bi_buf=e>>r-t.bi_valid,t.bi_valid+=n-r):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=n)}function P(t,e,n){C(t,n[2*e],n[2*e+1])}function S(t,e){for(var n=0;n|=1&t,t>>>=1,n<<=1,0<--e;);return n>>>1}function L(t,e,n){for(var r,a=new Array(16),o=0,i=1;i<=15;i++)a[i]=o=o+n[i-1]<<1;for(r=0;r<=e;r++){var s=t[2*r+1];0!==s&&(t[2*r]=S(a[s]++,s))}}function E(t){for(var e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function T(t){8<t.bi_valid?o(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function i(t,e,n,r){var a=2*e,o=2*n;return t[a]<t[o]||t[a]===t[o]&&r[e]<=r[n]}function B(t,e,n){for(var r=t.heap[n],a=n<<1;a<=t.heap_len&&(a<t.heap_len&&i(e,t.heap[a+1],t.heap[a],t.depth)&&a++,!i(e,r,t.heap[a],t.depth));)t.heap[n]=t.heap[a],n=a,a<<=1;t.heap[n]=r}function D(t,e,n){var r,a,o,i,s=0;if(0!==t.last_lit)for(;r=t.pending_buf[t.d_buf+2*s]<<8|t.pending_buf[t.d_buf+2*s+1],a=t.pending_buf[t.l_buf+s],s++,0==r?P(t,a,e):(P(t,(o=d[a])+256+1,e),0!==(i=A[o])&&C(t,a-=h[o],i),P(t,o=x(--r),n),0!==(i=l[o])&&C(t,r-=y[o],i)),s<t.last_lit;);P(t,256,e)}function _(t,e){var n,r,a,o=e.dyn_tree,i=e.stat_desc.static_tree,s=e.stat_desc.has_stree,A=e.stat_desc.elems,l=-1;for(t.heap_len=0,t.heap_max=573,n=0;n<A;n++)0!==o[2*n]?(t.heap[++t.heap_len]=l=n,t.depth[n]=0):o[2*n+1]=0;for(;t.heap_len<2;)o[2*(a=t.heap[++t.heap_len]=l<2?++l:0)]=1,t.depth[a]=0,t.opt_len--,s&&(t.static_len-=i[2*a+1]);for(e.max_code=l,n=t.heap_len>>1;1<=n;n--)B(t,o,n);for(a=A;n=t.heap[1],t.heap[1]=t.heap[t.heap_len--],B(t,o,1),r=t.heap[1],t.heap[--t.heap_max]=n,t.heap[--t.heap_max]=r,o[2*a]=o[2*n]+o[2*r],t.depth[a]=(t.depth[n]>=t.depth[r]?t.depth[n]:t.depth[r])+1,o[2*n+1]=o[2*r+1]=a,t.heap[1]=a++,B(t,o,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1];for(var c,u,p,f,d,h=t,g=e.dyn_tree,m=e.max_code,v=e.stat_desc.static_tree,y=e.stat_desc.has_stree,b=e.stat_desc.extra_bits,w=e.stat_desc.extra_base,x=e.stat_desc.max_length,C=0,P=0;P<=15;P++)h.bl_count[P]=0;for(g[2*h.heap[h.heap_max]+1]=0,c=h.heap_max+1;c<573;c++)x<(P=g[2*g[2*(u=h.heap[c])+1]+1]+1)&&(P=x,C++),g[2*u+1]=P,m<u||(h.bl_count[P]++,f=0,w<=u&&(f=b[u-w]),d=g[2*u],h.opt_len+=d*(P+f),y&&(h.static_len+=d*(v[2*u+1]+f)));if(0!==C){do{for(P=x-1;0===h.bl_count[P];)P--}while(h.bl_count[P]--,h.bl_count[P+1]+=2,h.bl_count[x]--,0<(C-=2));for(P=x;0!==P;P--)for(u=h.bl_count[P];0!==u;)m<(p=h.heap[--c])||(g[2*p+1]!==P&&(h.opt_len+=(P-g[2*p+1])*g[2*p],g[2*p+1]=P),u--)}L(o,l,t.bl_count)}function k(t,e,n){var r,a,o=-1,i=e[1],s=0,A=7,l=4;for(0===i&&(A=138,l=3),e[2*(n+1)+1]=65535,r=0;r<=n;r++)a=i,i=e[2*(r+1)+1],++s<A&&a===i||(s<l?t.bl_tree[2*a]+=s:0!==a?(a!==o&&t.bl_tree[2*a]++,t.bl_tree[32]++):s<=10?t.bl_tree[34]++:t.bl_tree[36]++,o=a,l=(s=0)===i?(A=138,3):a===i?(A=6,3):(A=7,4))}function N(t,e,n){var r,a,o=-1,i=e[1],s=0,A=7,l=4;for(0===i&&(A=138,l=3),r=0;r<=n;r++)if(a=i,i=e[2*(r+1)+1],!(++s<A&&a===i)){if(s<l)for(;P(t,a,t.bl_tree),0!=--s;);else 0!==a?(a!==o&&(P(t,a,t.bl_tree),s--),P(t,16,t.bl_tree),C(t,s-3,2)):s<=10?(P(t,17,t.bl_tree),C(t,s-3,3)):(P(t,18,t.bl_tree),C(t,s-11,7));o=a,l=(s=0)===i?(A=138,3):a===i?(A=6,3):(A=7,4)}}n(y);var F=!1;function I(t,e,n,r){C(t,0+(r?1:0),3),r=e,e=n,T(n=t),o(n,e),o(n,~e),a.arraySet(n.pending_buf,n.window,r,e,n.pending),n.pending+=e}e._tr_init=function(t){if(!F){for(var e,n,r,a,o=new Array(16),i=r=0;i<28;i++)for(h[i]=r,e=0;e<1<<A[i];e++)d[r++]=i;for(d[r-1]=i,i=a=0;i<16;i++)for(y[i]=a,e=0;e<1<<l[i];e++)f[a++]=i;for(a>>=7;i<30;i++)for(y[i]=a<<7,e=0;e<1<<l[i]-7;e++)f[256+a++]=i;for(n=0;n<=15;n++)o[n]=0;for(e=0;e<=143;)u[2*e+1]=8,e++,o[8]++;for(;e<=255;)u[2*e+1]=9,e++,o[9]++;for(;e<=279;)u[2*e+1]=7,e++,o[7]++;for(;e<=287;)u[2*e+1]=8,e++,o[8]++;for(L(u,287,o),e=0;e<30;e++)p[2*e+1]=5,p[2*e]=S(e,5);g=new b(u,A,257,286,15),m=new b(p,l,0,30,15),v=new b(new Array(0),s,0,19,7),F=!0}t.l_desc=new w(t.dyn_ltree,g),t.d_desc=new w(t.dyn_dtree,m),t.bl_desc=new w(t.bl_tree,v),t.bi_buf=0,t.bi_valid=0,E(t)},e._tr_stored_block=I,e._tr_flush_block=function(t,e,n,r){var a,o,i,s=0;if(0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){for(var e=4093624447,n=0;n<=31;n++,e>>>=1)if(1&e&&0!==t.dyn_ltree[2*n])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(n=32;n<256;n++)if(0!==t.dyn_ltree[2*n])return 1;return 0}(t)),_(t,t.l_desc),_(t,t.d_desc),s=function(t){var e;for(k(t,t.dyn_ltree,t.l_desc.max_code),k(t,t.dyn_dtree,t.d_desc.max_code),_(t,t.bl_desc),e=18;3<=e&&0===t.bl_tree[2*c[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),a=t.opt_len+3+7>>>3,(o=t.static_len+3+7>>>3)<=a&&(a=o)):a=o=n+5,n+4<=a&&-1!==e)I(t,e,n,r);else if(4===t.strategy||o===a)C(t,2+(r?1:0),3),D(t,u,p);else{C(t,4+(r?1:0),3);var A=t,l=(e=t.l_desc.max_code+1,n=t.d_desc.max_code+1,s+1);for(C(A,e-257,5),C(A,n-1,5),C(A,l-4,4),i=0;i<l;i++)C(A,A.bl_tree[2*c[i]+1],3);N(A,A.dyn_ltree,e-1),N(A,A.dyn_dtree,n-1),D(t,t.dyn_ltree,t.dyn_dtree)}E(t),r&&T(t)},e._tr_tally=function(t,e,n){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&n,t.last_lit++,0===e?t.dyn_ltree[2*n]++:(t.matches++,e--,t.dyn_ltree[2*(d[n]+256+1)]++,t.dyn_dtree[2*x(e)]++),t.last_lit===t.lit_bufsize-1},e._tr_align=function(t){C(t,2,3),P(t,256,u),16===(t=t).bi_valid?(o(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):8<=t.bi_valid&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},{"../utils/common":41}],53:[function(t,e,n){"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(t,e,n){"use strict";e.exports="function"==typeof setImmediate?setImmediate:function(){var t=[].slice.apply(arguments);t.splice(1,0,0),setTimeout.apply(null,t)}},{}]},{},[10])(10)})}.call(this,void 0!==n?n:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)})}.call(this,void 0!==n?n:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)})}.call(this,void 0!==n?n:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)})}.call(this,void 0!==n?n:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)})}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}),function r(a,o,i){function s(e,t){if(!o[e]){if(!a[e]){var n="function"==typeof require&&require;if(!t&&n)return n(e,!0);if(A)return A(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};a[e][0].call(n.exports,function(t){return s(a[e][1][t]||t)},n,n.exports,r,a,o,i)}return o[e].exports}for(var A="function"==typeof require&&require,t=0;t<i.length;t++)s(i[t]);return s}({1:[function(t,e,n){"use strict";t(2);t=(t=t(15))&&t.__esModule?t:{default:t};t.default._babelPolyfill&&"undefined"!=typeof console&&console.warn&&console.warn("@babel/polyfill is loaded more than once on this page. This is probably not desirable/intended and may have consequences if different versions of the polyfills are applied sequentially. If you do need to load the polyfill more than once, use @babel/polyfill/noConflict instead to bypass the warning."),t.default._babelPolyfill=!0},{15:15,2:2}],2:[function(t,e,n){"use strict";t(3),t(5),t(4),t(11),t(10),t(13),t(12),t(14),t(7),t(8),t(6),t(9),t(306),t(307)},{10:10,11:11,12:12,13:13,14:14,3:3,306:306,307:307,4:4,5:5,6:6,7:7,8:8,9:9}],3:[function(t,e,n){t(278),t(214),t(216),t(215),t(218),t(220),t(225),t(219),t(217),t(227),t(226),t(222),t(223),t(221),t(213),t(224),t(228),t(229),t(180),t(182),t(181),t(231),t(230),t(201),t(211),t(212),t(202),t(203),t(204),t(205),t(206),t(207),t(208),t(209),t(210),t(184),t(185),t(186),t(187),t(188),t(189),t(190),t(191),t(192),t(193),t(194),t(195),t(196),t(197),t(198),t(199),t(200),t(265),t(270),t(277),t(268),t(260),t(261),t(266),t(271),t(273),t(256),t(257),t(258),t(259),t(262),t(263),t(264),t(267),t(269),t(272),t(274),t(275),t(276),t(175),t(177),t(176),t(179),t(178),t(163),t(161),t(168),t(165),t(171),t(173),t(160),t(167),t(157),t(172),t(155),t(170),t(169),t(162),t(166),t(154),t(156),t(159),t(158),t(174),t(164),t(247),t(248),t(254),t(249),t(250),t(251),t(252),t(253),t(232),t(183),t(255),t(290),t(291),t(279),t(280),t(285),t(288),t(289),t(283),t(286),t(284),t(287),t(281),t(282),t(233),t(234),t(235),t(236),t(237),t(240),t(238),t(239),t(241),t(242),t(243),t(244),t(246),t(245),e.exports=t(52)},{154:154,155:155,156:156,157:157,158:158,159:159,160:160,161:161,162:162,163:163,164:164,165:165,166:166,167:167,168:168,169:169,170:170,171:171,172:172,173:173,174:174,175:175,176:176,177:177,178:178,179:179,180:180,181:181,182:182,183:183,184:184,185:185,186:186,187:187,188:188,189:189,190:190,191:191,192:192,193:193,194:194,195:195,196:196,197:197,198:198,199:199,200:200,201:201,202:202,203:203,204:204,205:205,206:206,207:207,208:208,209:209,210:210,211:211,212:212,213:213,214:214,215:215,216:216,217:217,218:218,219:219,220:220,221:221,222:222,223:223,224:224,225:225,226:226,227:227,228:228,229:229,230:230,231:231,232:232,233:233,234:234,235:235,236:236,237:237,238:238,239:239,240:240,241:241,242:242,243:243,244:244,245:245,246:246,247:247,248:248,249:249,250:250,251:251,252:252,253:253,254:254,255:255,256:256,257:257,258:258,259:259,260:260,261:261,262:262,263:263,264:264,265:265,266:266,267:267,268:268,269:269,270:270,271:271,272:272,273:273,274:274,275:275,276:276,277:277,278:278,279:279,280:280,281:281,282:282,283:283,284:284,285:285,286:286,287:287,288:288,289:289,290:290,291:291,52:52}],4:[function(t,e,n){t(292),e.exports=t(52).Array.flatMap},{292:292,52:52}],5:[function(t,e,n){t(293),e.exports=t(52).Array.includes},{293:293,52:52}],6:[function(t,e,n){t(294),e.exports=t(52).Object.entries},{294:294,52:52}],7:[function(t,e,n){t(295),e.exports=t(52).Object.getOwnPropertyDescriptors},{295:295,52:52}],8:[function(t,e,n){t(296),e.exports=t(52).Object.values},{296:296,52:52}],9:[function(t,e,n){"use strict";t(232),t(297),e.exports=t(52).Promise.finally},{232:232,297:297,52:52}],10:[function(t,e,n){t(298),e.exports=t(52).String.padEnd},{298:298,52:52}],11:[function(t,e,n){t(299),e.exports=t(52).String.padStart},{299:299,52:52}],12:[function(t,e,n){t(301),e.exports=t(52).String.trimRight},{301:301,52:52}],13:[function(t,e,n){t(300),e.exports=t(52).String.trimLeft},{300:300,52:52}],14:[function(t,e,n){t(302),e.exports=t(151).f("asyncIterator")},{151:151,302:302}],15:[function(t,e,n){t(32),e.exports=t(18).global},{18:18,32:32}],16:[function(t,e,n){e.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},{}],17:[function(t,e,n){var r=t(28);e.exports=function(t){if(r(t))return t;throw TypeError(t+" is not an object!")}},{28:28}],18:[function(t,e,n){e=e.exports={version:"2.6.11"};"number"==typeof __e&&(__e=e)},{}],19:[function(t,e,n){var o=t(16);e.exports=function(r,a,t){if(o(r),void 0===a)return r;switch(t){case 1:return function(t){return r.call(a,t)};case 2:return function(t,e){return r.call(a,t,e)};case 3:return function(t,e,n){return r.call(a,t,e,n)}}return function(){return r.apply(a,arguments)}}},{16:16}],20:[function(t,e,n){e.exports=!t(23)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},{23:23}],21:[function(t,e,n){var r=t(28),a=t(24).document,o=r(a)&&r(a.createElement);e.exports=function(t){return o?a.createElement(t):{}}},{24:24,28:28}],22:[function(t,e,n){function h(t,e,n){var r,a,o,i=t&h.F,s=t&h.G,A=t&h.S,l=t&h.P,c=t&h.B,u=t&h.W,p=s?m:m[e]||(m[e]={}),f=p[w],d=s?g:A?g[e]:(g[e]||{})[w];for(r in n=s?e:n)(a=!i&&d&&void 0!==d[r])&&b(p,r)||(o=(a?d:n)[r],p[r]=s&&"function"!=typeof d[r]?n[r]:c&&a?v(o,g):u&&d[r]==o?function(r){function t(t,e,n){if(this instanceof r){switch(arguments.length){case 0:return new r;case 1:return new r(t);case 2:return new r(t,e)}return new r(t,e,n)}return r.apply(this,arguments)}return t[w]=r[w],t}(o):l&&"function"==typeof o?v(Function.call,o):o,l&&((p.virtual||(p.virtual={}))[r]=o,t&h.R)&&f&&!f[r]&&y(f,r,o))}var g=t(24),m=t(18),v=t(19),y=t(26),b=t(25),w="prototype";h.F=1,h.G=2,h.S=4,h.P=8,h.B=16,h.W=32,h.U=64,h.R=128,e.exports=h},{18:18,19:19,24:24,25:25,26:26}],23:[function(t,e,n){e.exports=function(t){try{return!!t()}catch(t){return!0}}},{}],24:[function(t,e,n){e=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)},{}],25:[function(t,e,n){var r={}.hasOwnProperty;e.exports=function(t,e){return r.call(t,e)}},{}],26:[function(t,e,n){var r=t(29),a=t(30);e.exports=t(20)?function(t,e,n){return r.f(t,e,a(1,n))}:function(t,e,n){return t[e]=n,t}},{20:20,29:29,30:30}],27:[function(t,e,n){e.exports=!t(20)&&!t(23)(function(){return 7!=Object.defineProperty(t(21)("div"),"a",{get:function(){return 7}}).a})},{20:20,21:21,23:23}],28:[function(t,e,n){e.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},{}],29:[function(t,e,n){var r=t(17),a=t(27),o=t(31),i=Object.defineProperty;n.f=t(20)?Object.defineProperty:function(t,e,n){if(r(t),e=o(e,!0),r(n),a)try{return i(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},{17:17,20:20,27:27,31:31}],30:[function(t,e,n){e.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},{}],31:[function(t,e,n){var a=t(28);e.exports=function(t,e){if(!a(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!a(r=n.call(t))||"function"==typeof(n=t.valueOf)&&!a(r=n.call(t))||!e&&"function"==typeof(n=t.toString)&&!a(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},{28:28}],32:[function(t,e,n){var r=t(22);r(r.G,{global:t(24)})},{22:22,24:24}],33:[function(t,e,n){arguments[4][16][0].apply(n,arguments)},{16:16}],34:[function(t,e,n){var r=t(48);e.exports=function(t,e){if("number"!=typeof t&&"Number"!=r(t))throw TypeError(e);return+t}},{48:48}],35:[function(t,e,n){var r=t(152)("unscopables"),a=Array.prototype;null==a[r]&&t(72)(a,r,{}),e.exports=function(t){a[r][t]=!0}},{152:152,72:72}],36:[function(t,e,n){"use strict";var r=t(129)(!0);e.exports=function(t,e,n){return e+(n?r(t,e).length:1)}},{129:129}],37:[function(t,e,n){e.exports=function(t,e,n,r){if(!(t instanceof e)||void 0!==r&&r in t)throw TypeError(n+": incorrect invocation!");return t}},{}],38:[function(t,e,n){arguments[4][17][0].apply(n,arguments)},{17:17,81:81}],39:[function(t,e,n){"use strict";var A=t(142),l=t(137),c=t(141);e.exports=[].copyWithin||function(t,e){var n=A(this),r=c(n.length),a=l(t,r),o=l(e,r),t=2<arguments.length?arguments[2]:void 0,i=Math.min((void 0===t?r:l(t,r))-o,r-a),s=1;for(o<a&&a<o+i&&(s=-1,o+=i-1,a+=i-1);0<i--;)o in n?n[a]=n[o]:delete n[a],a+=s,o+=s;return n}},{137:137,141:141,142:142}],40:[function(t,e,n){"use strict";var i=t(142),s=t(137),A=t(141);e.exports=function(t){for(var e=i(this),n=A(e.length),r=arguments.length,a=s(1<r?arguments[1]:void 0,n),r=2<r?arguments[2]:void 0,o=void 0===r?n:s(r,n);a<o;)e[a++]=t;return e}},{137:137,141:141,142:142}],41:[function(t,e,n){var A=t(140),l=t(141),c=t(137);e.exports=function(s){return function(t,e,n){var r,a=A(t),o=l(a.length),i=c(n,o);if(s&&e!=e){for(;i<o;)if((r=a[i++])!=r)return!0}else for(;i<o;i++)if((s||i in a)&&a[i]===e)return s||i||0;return!s&&-1}}},{137:137,140:140,141:141}],42:[function(t,e,n){var y=t(54),b=t(77),w=t(142),x=t(141),r=t(45);e.exports=function(u,t){var p=1==u,f=2==u,d=3==u,h=4==u,g=6==u,m=5==u||g,v=t||r;return function(t,e,n){for(var r,a,o=w(t),i=b(o),s=y(e,n,3),A=x(i.length),l=0,c=p?v(t,A):f?v(t,0):void 0;l<A;l++)if((m||l in i)&&(a=s(r=i[l],l,o),u))if(p)c[l]=a;else if(a)switch(u){case 3:return!0;case 5:return r;case 6:return l;case 2:c.push(r)}else if(h)return!1;return g?-1:d||h?h:c}}},{141:141,142:142,45:45,54:54,77:77}],43:[function(t,e,n){var c=t(33),u=t(142),p=t(77),f=t(141);e.exports=function(t,e,n,r,a){c(e);var o=u(t),i=p(o),s=f(o.length),A=a?s-1:0,l=a?-1:1;if(n<2)for(;;){if(A in i){r=i[A],A+=l;break}if(A+=l,a?A<0:s<=A)throw TypeError("Reduce of empty array with no initial value")}for(;a?0<=A:A<s;A+=l)A in i&&(r=e(r,i[A],A,o));return r}},{141:141,142:142,33:33,77:77}],44:[function(t,e,n){var r=t(81),a=t(79),o=t(152)("species");e.exports=function(t){var e;return void 0===(e=a(t)&&("function"!=typeof(e=t.constructor)||e!==Array&&!a(e.prototype)||(e=void 0),r(e))&&null===(e=e[o])?void 0:e)?Array:e}},{152:152,79:79,81:81}],45:[function(t,e,n){var r=t(44);e.exports=function(t,e){return new(r(t))(e)}},{44:44}],46:[function(t,e,n){"use strict";var r=t(33),a=t(81),c=t(76),u=[].slice,p={};e.exports=Function.bind||function(i){var s=r(this),A=u.call(arguments,1),l=function(){var t=A.concat(u.call(arguments));if(this instanceof l){var e=s,n=t.length,r=t;if(!(n in p)){for(var a=[],o=0;o<n;o++)a[o]="a["+o+"]";p[n]=Function("F,a","return new F("+a.join(",")+")")}return p[n](e,r)}return c(s,t,i)};return a(s.prototype)&&(l.prototype=s.prototype),l}},{33:33,76:76,81:81}],47:[function(t,e,n){var r=t(48),a=t(152)("toStringTag"),o="Arguments"==r(function(){return arguments}());e.exports=function(t){var e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,e){try{return t[e]}catch(t){}}(t=Object(t),a))?e:o?r(t):"Object"==(e=r(t))&&"function"==typeof t.callee?"Arguments":e}},{152:152,48:48}],48:[function(t,e,n){var r={}.toString;e.exports=function(t){return r.call(t).slice(8,-1)}},{}],49:[function(t,e,n){"use strict";function i(t,e){var n,r=d(e);if("F"!==r)return t._i[r];for(n=t._f;n;n=n.n)if(n.k==e)return n}var s=t(99).f,A=t(98),l=t(117),c=t(54),u=t(37),p=t(68),r=t(85),a=t(87),o=t(123),f=t(58),d=t(94).fastKey,h=t(149),g=f?"_s":"size";e.exports={getConstructor:function(t,a,n,r){var o=t(function(t,e){u(t,o,a,"_i"),t._t=a,t._i=A(null),t._f=void 0,t._l=void 0,t[g]=0,null!=e&&p(e,n,t[r],t)});return l(o.prototype,{clear:function(){for(var t=h(this,a),e=t._i,n=t._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete e[n.i];t._f=t._l=void 0,t[g]=0},delete:function(t){var e,n,r=h(this,a),t=i(r,t);return t&&(e=t.n,n=t.p,delete r._i[t.i],t.r=!0,n&&(n.n=e),e&&(e.p=n),r._f==t&&(r._f=e),r._l==t&&(r._l=n),r[g]--),!!t},forEach:function(t){h(this,a);for(var e,n=c(t,1<arguments.length?arguments[1]:void 0,3);e=e?e.n:this._f;)for(n(e.v,e.k,this);e&&e.r;)e=e.p},has:function(t){return!!i(h(this,a),t)}}),f&&s(o.prototype,"size",{get:function(){return h(this,a)[g]}}),o},def:function(t,e,n){var r,a=i(t,e);return a?a.v=n:(t._l=a={i:r=d(e,!0),k:e,v:n,p:e=t._l,n:void 0,r:!1},t._f||(t._f=a),e&&(e.n=a),t[g]++,"F"!==r&&(t._i[r]=a)),t},getEntry:i,setStrong:function(t,n,e){r(t,n,function(t,e){this._t=h(t,n),this._k=e,this._l=void 0},function(){for(var t=this,e=t._k,n=t._l;n&&n.r;)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?a(0,"keys"==e?n.k:"values"==e?n.v:[n.k,n.v]):(t._t=void 0,a(1))},e?"entries":"values",!e,!0),o(n)}}},{117:117,123:123,149:149,37:37,54:54,58:58,68:68,85:85,87:87,94:94,98:98,99:99}],50:[function(t,e,n){"use strict";function i(t){return t._l||(t._l=new a)}function r(t,e){return h(t.a,function(t){return t[0]===e})}function a(){this.a=[]}var s=t(117),A=t(94).getWeak,o=t(38),l=t(81),c=t(37),u=t(68),p=t(42),f=t(71),d=t(149),h=p(5),g=p(6),m=0;a.prototype={get:function(t){t=r(this,t);if(t)return t[1]},has:function(t){return!!r(this,t)},set:function(t,e){var n=r(this,t);n?n[1]=e:this.a.push([t,e])},delete:function(e){var t=g(this.a,function(t){return t[0]===e});return~t&&this.a.splice(t,1),!!~t}},e.exports={getConstructor:function(t,n,r,a){var o=t(function(t,e){c(t,o,n,"_i"),t._t=n,t._i=m++,t._l=void 0,null!=e&&u(e,r,t[a],t)});return s(o.prototype,{delete:function(t){var e;return!!l(t)&&(!0===(e=A(t))?i(d(this,n)).delete(t):e&&f(e,this._i)&&delete e[this._i])},has:function(t){var e;return!!l(t)&&(!0===(e=A(t))?i(d(this,n)).has(t):e&&f(e,this._i))}}),o},def:function(t,e,n){var r=A(o(e),!0);return!0===r?i(t).set(e,n):r[t._i]=n,t},ufstore:i}},{117:117,149:149,37:37,38:38,42:42,68:68,71:71,81:81,94:94}],51:[function(t,e,n){"use strict";var m=t(70),v=t(62),y=t(118),b=t(117),w=t(94),x=t(68),C=t(37),P=t(81),S=t(64),L=t(86),E=t(124),T=t(75);e.exports=function(n,t,e,r,a,o){function i(t){var n=h[t];y(h,t,"delete"==t?function(t){return!(o&&!P(t))&&n.call(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!P(t))&&n.call(this,0===t?0:t)}:"get"==t?function(t){return o&&!P(t)?void 0:n.call(this,0===t?0:t)}:"add"==t?function(t){return n.call(this,0===t?0:t),this}:function(t,e){return n.call(this,0===t?0:t,e),this})}var s,A,l,c,u,p=m[n],f=p,d=a?"set":"add",h=f&&f.prototype,g={};return"function"==typeof f&&(o||h.forEach&&!S(function(){(new f).entries().next()}))?(A=(s=new f)[d](o?{}:-0,1)!=s,l=S(function(){s.has(1)}),c=L(function(t){new f(t)}),u=!o&&S(function(){for(var t=new f,e=5;e--;)t[d](e,e);return!t.has(-0)}),c||(((f=t(function(t,e){C(t,f,n);t=T(new p,t,f);return null!=e&&x(e,a,t[d],t),t})).prototype=h).constructor=f),(l||u)&&(i("delete"),i("has"),a)&&i("get"),(u||A)&&i(d),o&&h.clear&&delete h.clear):(f=r.getConstructor(t,n,a,d),b(f.prototype,e),w.NEED=!0),E(f,n),g[n]=f,v(v.G+v.W+v.F*(f!=p),g),o||r.setStrong(f,n,a),f}},{117:117,118:118,124:124,37:37,62:62,64:64,68:68,70:70,75:75,81:81,86:86,94:94}],52:[function(t,e,n){arguments[4][18][0].apply(n,arguments)},{18:18}],53:[function(t,e,n){"use strict";var r=t(99),a=t(116);e.exports=function(t,e,n){e in t?r.f(t,e,a(0,n)):t[e]=n}},{116:116,99:99}],54:[function(t,e,n){arguments[4][19][0].apply(n,arguments)},{19:19,33:33}],55:[function(t,e,n){"use strict";function a(t){return 9<t?t:"0"+t}var t=t(64),o=Date.prototype.getTime,r=Date.prototype.toISOString;e.exports=t(function(){return"0385-07-25T07:06:39.999Z"!=r.call(new Date(-5e13-1))})||!t(function(){r.call(new Date(NaN))})?function(){var t,e,n,r;if(isFinite(o.call(this)))return e=(t=this).getUTCFullYear(),n=t.getUTCMilliseconds(),(r=e<0?"-":9999<e?"+":"")+("00000"+Math.abs(e)).slice(r?-6:-4)+"-"+a(t.getUTCMonth()+1)+"-"+a(t.getUTCDate())+"T"+a(t.getUTCHours())+":"+a(t.getUTCMinutes())+":"+a(t.getUTCSeconds())+"."+(99<n?n:"0"+a(n))+"Z";throw RangeError("Invalid time value")}:r},{64:64}],56:[function(t,e,n){"use strict";var r=t(38),a=t(143);e.exports=function(t){if("string"!==t&&"number"!==t&&"default"!==t)throw TypeError("Incorrect hint");return a(r(this),"number"!=t)}},{143:143,38:38}],57:[function(t,e,n){e.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},{}],58:[function(t,e,n){arguments[4][20][0].apply(n,arguments)},{20:20,64:64}],59:[function(t,e,n){arguments[4][21][0].apply(n,arguments)},{21:21,70:70,81:81}],60:[function(t,e,n){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},{}],61:[function(t,e,n){var s=t(107),A=t(104),l=t(108);e.exports=function(t){var e=s(t),n=A.f;if(n)for(var r,a=n(t),o=l.f,i=0;a.length>i;)o.call(t,r=a[i++])&&e.push(r);return e}},{104:104,107:107,108:108}],62:[function(t,e,n){function f(t,e,n){var r,a,o,i=t&f.F,s=t&f.G,A=t&f.P,l=t&f.B,c=s?d:t&f.S?d[e]||(d[e]={}):(d[e]||{})[y],u=s?h:h[e]||(h[e]={}),p=u[y]||(u[y]={});for(r in n=s?e:n)a=((o=!i&&c&&void 0!==c[r])?c:n)[r],o=l&&o?v(a,d):A&&"function"==typeof a?v(Function.call,a):a,c&&m(c,r,a,t&f.U),u[r]!=a&&g(u,r,o),A&&p[r]!=a&&(p[r]=a)}var d=t(70),h=t(52),g=t(72),m=t(118),v=t(54),y="prototype";d.core=h,f.F=1,f.G=2,f.S=4,f.P=8,f.B=16,f.W=32,f.U=64,f.R=128,e.exports=f},{118:118,52:52,54:54,70:70,72:72}],63:[function(t,e,n){var r=t(152)("match");e.exports=function(e){var n=/./;try{"/./"[e](n)}catch(t){try{return n[r]=!1,!"/./"[e](n)}catch(t){}}return!0}},{152:152}],64:[function(t,e,n){arguments[4][23][0].apply(n,arguments)},{23:23}],65:[function(t,e,n){"use strict";t(248);var r,A=t(118),l=t(72),c=t(64),u=t(57),p=t(152),f=t(120),d=p("species"),h=!c(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}),g=(r=(t=/(?:)/).exec,t.exec=function(){return r.apply(this,arguments)},2===(t="ab".split(t)).length&&"a"===t[0]&&"b"===t[1]);e.exports=function(n,t,e){var o,r,a=p(n),i=!c(function(){var t={};return t[a]=function(){return 7},7!=""[n](t)}),s=i?!c(function(){var t=!1,e=/a/;return e.exec=function(){return t=!0,null},"split"===n&&(e.constructor={},e.constructor[d]=function(){return e}),e[a](""),!t}):void 0;i&&s&&("replace"!==n||h)&&("split"!==n||g)||(o=/./[a],e=(s=e(u,a,""[n],function(t,e,n,r,a){return e.exec===f?i&&!a?{done:!0,value:o.call(e,n,r)}:{done:!0,value:t.call(n,e,r)}:{done:!1}}))[0],r=s[1],A(String.prototype,n,e),l(RegExp.prototype,a,2==t?function(t,e){return r.call(t,this,e)}:function(t){return r.call(t,this)}))}},{118:118,120:120,152:152,248:248,57:57,64:64,72:72}],66:[function(t,e,n){"use strict";var r=t(38);e.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},{38:38}],67:[function(t,e,n){"use strict";var d=t(79),h=t(81),g=t(141),m=t(54),v=t(152)("isConcatSpreadable");e.exports=function t(e,n,r,a,o,i,s,A){for(var l,c,u=o,p=0,f=!!s&&m(s,A,3);p<a;){if(p in r){if(l=f?f(r[p],p,n):r[p],c=!1,(c=h(l)?void 0!==(c=l[v])?!!c:d(l):c)&&0<i)u=t(e,n,l,g(l.length),u,i-1)-1;else{if(9007199254740991<=u)throw TypeError();e[u]=l}u++}p++}return u}},{141:141,152:152,54:54,79:79,81:81}],68:[function(t,e,n){var u=t(54),p=t(83),f=t(78),d=t(38),h=t(141),g=t(153),m={},v={};(n=e.exports=function(t,e,n,r,a){var o,i,s,A,a=a?function(){return t}:g(t),l=u(n,r,e?2:1),c=0;if("function"!=typeof a)throw TypeError(t+" is not iterable!");if(f(a)){for(o=h(t.length);c<o;c++)if((A=e?l(d(i=t[c])[0],i[1]):l(t[c]))===m||A===v)return A}else for(s=a.call(t);!(i=s.next()).done;)if((A=p(s,l,i.value,e))===m||A===v)return A}).BREAK=m,n.RETURN=v},{141:141,153:153,38:38,54:54,78:78,83:83}],69:[function(t,e,n){e.exports=t(126)("native-function-to-string",Function.toString)},{126:126}],70:[function(t,e,n){arguments[4][24][0].apply(n,arguments)},{24:24}],71:[function(t,e,n){arguments[4][25][0].apply(n,arguments)},{25:25}],72:[function(t,e,n){arguments[4][26][0].apply(n,arguments)},{116:116,26:26,58:58,99:99}],73:[function(t,e,n){t=t(70).document;e.exports=t&&t.documentElement},{70:70}],74:[function(t,e,n){arguments[4][27][0].apply(n,arguments)},{27:27,58:58,59:59,64:64}],75:[function(t,e,n){var r=t(81),a=t(122).set;e.exports=function(t,e,n){var e=e.constructor;return e!==n&&"function"==typeof e&&(e=e.prototype)!==n.prototype&&r(e)&&a&&a(t,e),t}},{122:122,81:81}],76:[function(t,e,n){e.exports=function(t,e,n){var r=void 0===n;switch(e.length){case 0:return r?t():t.call(n);case 1:return r?t(e[0]):t.call(n,e[0]);case 2:return r?t(e[0],e[1]):t.call(n,e[0],e[1]);case 3:return r?t(e[0],e[1],e[2]):t.call(n,e[0],e[1],e[2]);case 4:return r?t(e[0],e[1],e[2],e[3]):t.call(n,e[0],e[1],e[2],e[3])}return t.apply(n,e)}},{}],77:[function(t,e,n){var r=t(48);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},{48:48}],78:[function(t,e,n){var r=t(88),a=t(152)("iterator"),o=Array.prototype;e.exports=function(t){return void 0!==t&&(r.Array===t||o[a]===t)}},{152:152,88:88}],79:[function(t,e,n){var r=t(48);e.exports=Array.isArray||function(t){return"Array"==r(t)}},{48:48}],80:[function(t,e,n){var r=t(81),a=Math.floor;e.exports=function(t){return!r(t)&&isFinite(t)&&a(t)===t}},{81:81}],81:[function(t,e,n){arguments[4][28][0].apply(n,arguments)},{28:28}],82:[function(t,e,n){var r=t(81),a=t(48),o=t(152)("match");e.exports=function(t){var e;return r(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==a(t))}},{152:152,48:48,81:81}],83:[function(t,e,n){var a=t(38);e.exports=function(t,e,n,r){try{return r?e(a(n)[0],n[1]):e(n)}catch(e){r=t.return;throw void 0!==r&&a(r.call(t)),e}}},{38:38}],84:[function(t,e,n){"use strict";var r=t(98),a=t(116),o=t(124),i={};t(72)(i,t(152)("iterator"),function(){return this}),e.exports=function(t,e,n){t.prototype=r(i,{next:a(1,n)}),o(t,e+" Iterator")}},{116:116,124:124,152:152,72:72,98:98}],85:[function(t,e,n){"use strict";function m(){return this}var v=t(89),y=t(62),b=t(118),w=t(72),x=t(88),C=t(84),P=t(124),S=t(105),L=t(152)("iterator"),E=!([].keys&&"next"in[].keys()),T="values";e.exports=function(t,e,n,r,a,o,i){function s(t){if(!E&&t in p)return p[t];switch(t){case"keys":case T:return function(){return new n(this,t)}}return function(){return new n(this,t)}}C(n,e,r);var A,l,r=e+" Iterator",c=a==T,u=!1,p=t.prototype,f=p[L]||p["@@iterator"]||a&&p[a],d=f||s(a),h=a?c?s("entries"):d:void 0,g="Array"==e&&p.entries||f;if(g&&(g=S(g.call(new t)))!==Object.prototype&&g.next&&(P(g,r,!0),v||"function"==typeof g[L]||w(g,L,m)),c&&f&&f.name!==T&&(u=!0,d=function(){return f.call(this)}),v&&!i||!E&&!u&&p[L]||w(p,L,d),x[e]=d,x[r]=m,a)if(A={values:c?d:s(T),keys:o?d:s("keys"),entries:h},i)for(l in A)l in p||b(p,l,A[l]);else y(y.P+y.F*(E||u),e,A);return A}},{105:105,118:118,124:124,152:152,62:62,72:72,84:84,88:88,89:89}],86:[function(t,e,n){var o=t(152)("iterator"),i=!1;try{var r=[7][o]();r.return=function(){i=!0},Array.from(r,function(){throw 2})}catch(t){}e.exports=function(t,e){if(!e&&!i)return!1;var n=!1;try{var r=[7],a=r[o]();a.next=function(){return{done:n=!0}},r[o]=function(){return a},t(r)}catch(t){}return n}},{152:152}],87:[function(t,e,n){e.exports=function(t,e){return{value:e,done:!!t}}},{}],88:[function(t,e,n){e.exports={}},{}],89:[function(t,e,n){e.exports=!1},{}],90:[function(t,e,n){var r=Math.expm1;e.exports=!r||22025.465794806718<r(10)||r(10)<22025.465794806718||-2e-17!=r(-2e-17)?function(t){return 0==(t=+t)?t:-1e-6<t&&t<1e-6?t+t*t/2:Math.exp(t)-1}:r},{}],91:[function(t,e,n){var r=t(93),t=Math.pow,a=t(2,-52),o=t(2,-23),i=t(2,127)*(2-o),s=t(2,-126);e.exports=Math.fround||function(t){var e,n=Math.abs(t),t=r(t);return n<s?t*(n/s/o+1/a-1/a)*s*o:i<(e=(e=(1+o/a)*n)-(e-n))||e!=e?t*(1/0):t*e}},{93:93}],92:[function(t,e,n){e.exports=Math.log1p||function(t){return-1e-8<(t=+t)&&t<1e-8?t-t*t/2:Math.log(1+t)}},{}],93:[function(t,e,n){e.exports=Math.sign||function(t){return 0==(t=+t)||t!=t?t:t<0?-1:1}},{}],94:[function(t,e,n){function r(t){s(t,a,{value:{i:"O"+ ++A,w:{}}})}var a=t(147)("meta"),o=t(81),i=t(71),s=t(99).f,A=0,l=Object.isExtensible||function(){return!0},c=!t(64)(function(){return l(Object.preventExtensions({}))}),u=e.exports={KEY:a,NEED:!1,fastKey:function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!i(t,a)){if(!l(t))return"F";if(!e)return"E";r(t)}return t[a].i},getWeak:function(t,e){if(!i(t,a)){if(!l(t))return!0;if(!e)return!1;r(t)}return t[a].w},onFreeze:function(t){return c&&u.NEED&&l(t)&&!i(t,a)&&r(t),t}}},{147:147,64:64,71:71,81:81,99:99}],95:[function(t,e,n){var s=t(70),A=t(136).set,l=s.MutationObserver||s.WebKitMutationObserver,c=s.process,u=s.Promise,p="process"==t(48)(c);e.exports=function(){function t(){var t,e;for(p&&(t=c.domain)&&t.exit();n;){e=n.fn,n=n.next;try{e()}catch(t){throw n?a():r=void 0,t}}r=void 0,t&&t.enter()}var n,r,e,a,o,i;return a=p?function(){c.nextTick(t)}:!l||s.navigator&&s.navigator.standalone?u&&u.resolve?(e=u.resolve(void 0),function(){e.then(t)}):function(){A.call(s,t)}:(o=!0,i=document.createTextNode(""),new l(t).observe(i,{characterData:!0}),function(){i.data=o=!o}),function(t){t={fn:t,next:void 0};r&&(r.next=t),n||(n=t,a()),r=t}}},{136:136,48:48,70:70}],96:[function(t,e,n){"use strict";var a=t(33);function r(t){var n,r;this.promise=new t(function(t,e){if(void 0!==n||void 0!==r)throw TypeError("Bad Promise constructor");n=t,r=e}),this.resolve=a(n),this.reject=a(r)}e.exports.f=function(t){return new r(t)}},{33:33}],97:[function(t,e,n){"use strict";var p=t(58),f=t(107),d=t(104),h=t(108),g=t(142),m=t(77),a=Object.assign;e.exports=!a||t(64)(function(){var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach(function(t){e[t]=t}),7!=a({},t)[n]||Object.keys(a({},e)).join("")!=r})?function(t,e){for(var n=g(t),r=arguments.length,a=1,o=d.f,i=h.f;a<r;)for(var s,A=m(arguments[a++]),l=o?f(A).concat(o(A)):f(A),c=l.length,u=0;u<c;)s=l[u++],p&&!i.call(A,s)||(n[s]=A[s]);return n}:a},{104:104,107:107,108:108,142:142,58:58,64:64,77:77}],98:[function(n,t,e){function r(){}var a=n(38),o=n(100),i=n(60),s=n(125)("IE_PROTO"),A="prototype",l=function(){var t=n(59)("iframe"),e=i.length;for(t.style.display="none",n(73).appendChild(t),t.src="javascript:",(t=t.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),l=t.F;e--;)delete l[A][i[e]];return l()};t.exports=Object.create||function(t,e){var n;return null!==t?(r[A]=a(t),n=new r,r[A]=null,n[s]=t):n=l(),void 0===e?n:o(n,e)}},{100:100,125:125,38:38,59:59,60:60,73:73}],99:[function(t,e,n){arguments[4][29][0].apply(n,arguments)},{143:143,29:29,38:38,58:58,74:74}],100:[function(t,e,n){var i=t(99),s=t(38),A=t(107);e.exports=t(58)?Object.defineProperties:function(t,e){s(t);for(var n,r=A(e),a=r.length,o=0;o<a;)i.f(t,n=r[o++],e[n]);return t}},{107:107,38:38,58:58,99:99}],101:[function(t,e,n){var r=t(108),a=t(116),o=t(140),i=t(143),s=t(71),A=t(74),l=Object.getOwnPropertyDescriptor;n.f=t(58)?l:function(t,e){if(t=o(t),e=i(e,!0),A)try{return l(t,e)}catch(t){}if(s(t,e))return a(!r.f.call(t,e),t[e])}},{108:108,116:116,140:140,143:143,58:58,71:71,74:74}],102:[function(t,e,n){var r=t(140),a=t(103).f,o={}.toString,i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(t){if(!i||"[object Window]"!=o.call(t))return a(r(t));var e=t;try{return a(e)}catch(e){return i.slice()}}},{103:103,140:140}],103:[function(t,e,n){var r=t(106),a=t(60).concat("length","prototype");n.f=Object.getOwnPropertyNames||function(t){return r(t,a)}},{106:106,60:60}],104:[function(t,e,n){n.f=Object.getOwnPropertySymbols},{}],105:[function(t,e,n){var r=t(71),a=t(142),o=t(125)("IE_PROTO"),i=Object.prototype;e.exports=Object.getPrototypeOf||function(t){return t=a(t),r(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?i:null}},{125:125,142:142,71:71}],106:[function(t,e,n){var i=t(71),s=t(140),A=t(41)(!1),l=t(125)("IE_PROTO");e.exports=function(t,e){var n,r=s(t),a=0,o=[];for(n in r)n!=l&&i(r,n)&&o.push(n);for(;e.length>a;)!i(r,n=e[a++])||~A(o,n)||o.push(n);return o}},{125:125,140:140,41:41,71:71}],107:[function(t,e,n){var r=t(106),a=t(60);e.exports=Object.keys||function(t){return r(t,a)}},{106:106,60:60}],108:[function(t,e,n){n.f={}.propertyIsEnumerable},{}],109:[function(t,e,n){var a=t(62),o=t(52),i=t(64);e.exports=function(t,e){var n=(o.Object||{})[t]||Object[t],r={};r[t]=e(n),a(a.S+a.F*i(function(){n(1)}),"Object",r)}},{52:52,62:62,64:64}],110:[function(t,e,n){var A=t(58),l=t(107),c=t(140),u=t(108).f;e.exports=function(s){return function(t){for(var e,n=c(t),r=l(n),a=r.length,o=0,i=[];o<a;)e=r[o++],A&&!u.call(n,e)||i.push(s?[e,n[e]]:n[e]);return i}}},{107:107,108:108,140:140,58:58}],111:[function(t,e,n){var r=t(103),a=t(104),o=t(38),t=t(70).Reflect;e.exports=t&&t.ownKeys||function(t){var e=r.f(o(t)),n=a.f;return n?e.concat(n(t)):e}},{103:103,104:104,38:38,70:70}],112:[function(t,e,n){var r=t(70).parseFloat,a=t(134).trim;e.exports=1/r(t(135)+"-0")!=-1/0?function(t){var t=a(String(t),3),e=r(t);return 0===e&&"-"==t.charAt(0)?-0:e}:r},{134:134,135:135,70:70}],113:[function(t,e,n){var r=t(70).parseInt,a=t(134).trim,t=t(135),o=/^[-+]?0[xX]/;e.exports=8!==r(t+"08")||22!==r(t+"0x16")?function(t,e){t=a(String(t),3);return r(t,e>>>0||(o.test(t)?16:10))}:r},{134:134,135:135,70:70}],114:[function(t,e,n){e.exports=function(t){try{return{e:!1,v:t()}}catch(t){return{e:!0,v:t}}}},{}],115:[function(t,e,n){var r=t(38),a=t(81),o=t(96);e.exports=function(t,e){return r(t),a(e)&&e.constructor===t?e:((0,(t=o.f(t)).resolve)(e),t.promise)}},{38:38,81:81,96:96}],116:[function(t,e,n){arguments[4][30][0].apply(n,arguments)},{30:30}],117:[function(t,e,n){var a=t(118);e.exports=function(t,e,n){for(var r in e)a(t,r,e[r],n);return t}},{118:118}],118:[function(t,e,n){var o=t(70),i=t(72),s=t(71),A=t(147)("src"),r=t(69),a="toString",l=(""+r).split(a);t(52).inspectSource=function(t){return r.call(t)},(e.exports=function(t,e,n,r){var a="function"==typeof n;a&&!s(n,"name")&&i(n,"name",e),t[e]!==n&&(a&&!s(n,A)&&i(n,A,t[e]?""+t[e]:l.join(String(e))),t===o?t[e]=n:r?t[e]?t[e]=n:i(t,e,n):(delete t[e],i(t,e,n)))})(Function.prototype,a,function(){return"function"==typeof this&&this[A]||r.call(this)})},{147:147,52:52,69:69,70:70,71:71,72:72}],119:[function(t,e,n){"use strict";var r=t(47),a=RegExp.prototype.exec;e.exports=function(t,e){var n=t.exec;if("function"==typeof n){n=n.call(t,e);if("object"!=typeof n)throw new TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==r(t))throw new TypeError("RegExp#exec called on incompatible receiver");return a.call(t,e)}},{47:47}],120:[function(t,e,n){"use strict";var r,a,i=t(66),s=RegExp.prototype.exec,A=String.prototype.replace,t=s,l="lastIndex",c=(a=/b*/g,s.call(r=/a/,"a"),s.call(a,"a"),0!==r[l]||0!==a[l]),u=void 0!==/()??/.exec("")[1];e.exports=t=c||u?function(t){var e,n,r,a,o=this;return u&&(n=new RegExp("^"+o.source+"$(?!\\s)",i.call(o))),c&&(e=o[l]),r=s.call(o,t),c&&r&&(o[l]=o.global?r.index+r[0].length:e),u&&r&&1<r.length&&A.call(r[0],n,function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(r[a]=void 0)}),r}:t},{66:66}],121:[function(t,e,n){e.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},{}],122:[function(e,t,n){function a(t,e){if(o(t),!r(e)&&null!==e)throw TypeError(e+": can't set as prototype!")}var r=e(81),o=e(38);t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,n,r){try{(r=e(54)(Function.call,e(101).f(Object.prototype,"__proto__").set,2))(t,[]),n=!(t instanceof Array)}catch(t){n=!0}return function(t,e){return a(t,e),n?t.__proto__=e:r(t,e),t}}({},!1):void 0),check:a}},{101:101,38:38,54:54,81:81}],123:[function(t,e,n){"use strict";var r=t(70),a=t(99),o=t(58),i=t(152)("species");e.exports=function(t){t=r[t];o&&t&&!t[i]&&a.f(t,i,{configurable:!0,get:function(){return this}})}},{152:152,58:58,70:70,99:99}],124:[function(t,e,n){var r=t(99).f,a=t(71),o=t(152)("toStringTag");e.exports=function(t,e,n){t&&!a(t=n?t:t.prototype,o)&&r(t,o,{configurable:!0,value:e})}},{152:152,71:71,99:99}],125:[function(t,e,n){var r=t(126)("keys"),a=t(147);e.exports=function(t){return r[t]||(r[t]=a(t))}},{126:126,147:147}],126:[function(t,e,n){var r=t(52),a=t(70),o="__core-js_shared__",i=a[o]||(a[o]={});(e.exports=function(t,e){return i[t]||(i[t]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:t(89)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},{52:52,70:70,89:89}],127:[function(t,e,n){var r=t(38),a=t(33),o=t(152)("species");e.exports=function(t,e){var t=r(t).constructor;return void 0===t||null==(t=r(t)[o])?e:a(t)}},{152:152,33:33,38:38}],128:[function(t,e,n){"use strict";var r=t(64);e.exports=function(t,e){return!!t&&r(function(){e?t.call(null,function(){},1):t.call(null)})}},{64:64}],129:[function(t,e,n){var o=t(139),i=t(57);e.exports=function(a){return function(t,e){var n,t=String(i(t)),e=o(e),r=t.length;return e<0||r<=e?a?"":void 0:(n=t.charCodeAt(e))<55296||56319<n||e+1===r||(r=t.charCodeAt(e+1))<56320||57343<r?a?t.charAt(e):n:a?t.slice(e,e+2):r-56320+(n-55296<<10)+65536}}},{139:139,57:57}],130:[function(t,e,n){var r=t(82),a=t(57);e.exports=function(t,e,n){if(r(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(a(t))}},{57:57,82:82}],131:[function(t,e,n){function r(t,e,n,r){var t=String(i(t)),a="<"+e;return""!==n&&(a+=" "+n+'="'+String(r).replace(s,"&quot;")+'"'),a+">"+t+"</"+e+">"}var a=t(62),o=t(64),i=t(57),s=/"/g;e.exports=function(e,t){var n={};n[e]=t(r),a(a.P+a.F*o(function(){var t=""[e]('"');return t!==t.toLowerCase()||3<t.split('"').length}),"String",n)}},{57:57,62:62,64:64}],132:[function(t,e,n){var o=t(141),i=t(133),s=t(57);e.exports=function(t,e,n,r){var t=String(s(t)),a=t.length,n=void 0===n?" ":String(n),e=o(e);return e<=a||""==n?t:(e=e-a,(a=i.call(n,Math.ceil(e/n.length))).length>e&&(a=a.slice(0,e)),r?a+t:t+a)}},{133:133,141:141,57:57}],133:[function(t,e,n){"use strict";var a=t(139),o=t(57);e.exports=function(t){var e=String(o(this)),n="",r=a(t);if(r<0||r==1/0)throw RangeError("Count can't be negative");for(;0<r;(r>>>=1)&&(e+=e))1&r&&(n+=e);return n}},{139:139,57:57}],134:[function(t,e,n){function r(t,e,n){var r={},a=i(function(){return!!s[t]()||"​"!="​"[t]()}),e=r[t]=a?e(c):s[t];n&&(r[n]=e),o(o.P+o.F*a,"String",r)}var o=t(62),a=t(57),i=t(64),s=t(135),t="["+s+"]",A=RegExp("^"+t+t+"*"),l=RegExp(t+t+"*$"),c=r.trim=function(t,e){return t=String(a(t)),1&e&&(t=t.replace(A,"")),t=2&e?t.replace(l,""):t};e.exports=r},{135:135,57:57,62:62,64:64}],135:[function(t,e,n){e.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},{}],136:[function(t,e,n){function r(){var t,e=+this;m.hasOwnProperty(e)&&(t=m[e],delete m[e],t())}function a(t){r.call(t.data)}var o,i=t(54),s=t(76),A=t(73),l=t(59),c=t(70),u=c.process,p=c.setImmediate,f=c.clearImmediate,d=c.MessageChannel,h=c.Dispatch,g=0,m={},v="onreadystatechange";p&&f||(p=function(t){for(var e=[],n=1;n<arguments.length;)e.push(arguments[n++]);return m[++g]=function(){s("function"==typeof t?t:Function(t),e)},o(g),g},f=function(t){delete m[t]},"process"==t(48)(u)?o=function(t){u.nextTick(i(r,t,1))}:h&&h.now?o=function(t){h.now(i(r,t,1))}:d?(d=(t=new d).port2,t.port1.onmessage=a,o=i(d.postMessage,d,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(o=function(t){c.postMessage(t+"","*")},c.addEventListener("message",a,!1)):o=v in l("script")?function(t){A.appendChild(l("script"))[v]=function(){A.removeChild(this),r.call(t)}}:function(t){setTimeout(i(r,t,1),0)}),e.exports={set:p,clear:f}},{48:48,54:54,59:59,70:70,73:73,76:76}],137:[function(t,e,n){var r=t(139),a=Math.max,o=Math.min;e.exports=function(t,e){return(t=r(t))<0?a(t+e,0):o(t,e)}},{139:139}],138:[function(t,e,n){var r=t(139),a=t(141);e.exports=function(t){if(void 0===t)return 0;var t=r(t),e=a(t);if(t!==e)throw RangeError("Wrong length!");return e}},{139:139,141:141}],139:[function(t,e,n){var r=Math.ceil,a=Math.floor;e.exports=function(t){return isNaN(t=+t)?0:(0<t?a:r)(t)}},{}],140:[function(t,e,n){var r=t(77),a=t(57);e.exports=function(t){return r(a(t))}},{57:57,77:77}],141:[function(t,e,n){var r=t(139),a=Math.min;e.exports=function(t){return 0<t?a(r(t),9007199254740991):0}},{139:139}],142:[function(t,e,n){var r=t(57);e.exports=function(t){return Object(r(t))}},{57:57}],143:[function(t,e,n){arguments[4][31][0].apply(n,arguments)},{31:31,81:81}],144:[function(t,R,O){"use strict";var i,s,A,h,g,M,z,U,m,e,j,v,G,Q,W,r,Y,y,X,H,V,q,b,J,K,Z,n,a,$,tt,et,nt,rt,at,w,ot,x,it,o,st,At,lt,C,P,ct,ut,pt,ft,dt,ht,gt,mt,vt,yt,bt,wt,xt,Ct,Pt,St,Lt,Et,S,l,L,E,Tt,T,c,B,Bt,D,Dt,_t,kt,_,u,p,Nt,k,f,Ft,It,Rt,Ot,Mt,zt,Ut,N,jt,d,F,I;t(58)?(i=t(89),s=t(70),A=t(64),h=t(62),g=t(146),c=t(145),M=t(54),z=t(37),U=t(116),m=t(72),e=t(117),j=t(139),v=t(141),G=t(138),Q=t(137),W=t(143),r=t(71),Y=t(47),y=t(81),X=t(142),H=t(78),V=t(98),q=t(105),b=t(103).f,J=t(153),K=t(147),Z=t(152),n=t(42),f=t(41),a=t(127),d=t(164),$=t(88),tt=t(86),et=t(123),nt=t(40),rt=t(39),at=t(99),t=t(101),w=at.f,ot=t.f,x=s.RangeError,it=s.TypeError,o=s.Uint8Array,At="Shared"+(st="ArrayBuffer"),lt="BYTES_PER_ELEMENT",C="prototype",F=Array[C],P=c.ArrayBuffer,ct=c.DataView,ut=n(0),pt=n(2),ft=n(3),dt=n(4),ht=n(5),gt=n(6),mt=f(!0),vt=f(!1),yt=d.values,bt=d.keys,wt=d.entries,xt=F.lastIndexOf,Ct=F.reduce,Pt=F.reduceRight,St=F.join,Lt=F.sort,Et=F.slice,S=F.toString,l=F.toLocaleString,L=Z("iterator"),E=Z("toStringTag"),Tt=K("typed_constructor"),T=K("def_constructor"),c=g.CONSTR,B=g.TYPED,Bt=g.VIEW,D="Wrong length!",Dt=n(1,function(t,e){return p(a(t,t[T]),e)}),_t=A(function(){return 1===new o(new Uint16Array([1]).buffer)[0]}),kt=!!o&&!!o[C].set&&A(function(){new o(1).set({})}),_=function(t,e){t=j(t);if(t<0||t%e)throw x("Wrong offset!");return t},u=function(t){if(y(t)&&B in t)return t;throw it(t+" is not a typed array!")},p=function(t,e){if(y(t)&&Tt in t)return new t(e);throw it("It is not a typed array constructor!")},Nt=function(t,e){return k(a(t,t[T]),e)},k=function(t,e){for(var n=0,r=e.length,a=p(t,r);n<r;)a[n]=e[n++];return a},f=function(t,e,n){w(t,e,{get:function(){return this._d[n]}})},Ft=function(t){var e,n,r,a,o,i,s=X(t),t=arguments.length,A=1<t?arguments[1]:void 0,l=void 0!==A,c=J(s);if(null!=c&&!H(c)){for(i=c.call(s),r=[],e=0;!(o=i.next()).done;e++)r.push(o.value);s=r}for(l&&2<t&&(A=M(A,arguments[2],2)),e=0,n=v(s.length),a=p(this,n);e<n;e++)a[e]=l?A(s[e],e):s[e];return a},It=function(){for(var t=0,e=arguments.length,n=p(this,e);t<e;)n[t]=arguments[t++];return n},Rt=!!o&&A(function(){l.call(new o(1))}),Ot=function(){return l.apply(Rt?Et.call(u(this)):u(this),arguments)},Mt={copyWithin:function(t,e){return rt.call(u(this),t,e,2<arguments.length?arguments[2]:void 0)},every:function(t){return dt(u(this),t,1<arguments.length?arguments[1]:void 0)},fill:function(t){return nt.apply(u(this),arguments)},filter:function(t){return Nt(this,pt(u(this),t,1<arguments.length?arguments[1]:void 0))},find:function(t){return ht(u(this),t,1<arguments.length?arguments[1]:void 0)},findIndex:function(t){return gt(u(this),t,1<arguments.length?arguments[1]:void 0)},forEach:function(t){ut(u(this),t,1<arguments.length?arguments[1]:void 0)},indexOf:function(t){return vt(u(this),t,1<arguments.length?arguments[1]:void 0)},includes:function(t){return mt(u(this),t,1<arguments.length?arguments[1]:void 0)},join:function(t){return St.apply(u(this),arguments)},lastIndexOf:function(t){return xt.apply(u(this),arguments)},map:function(t){return Dt(u(this),t,1<arguments.length?arguments[1]:void 0)},reduce:function(t){return Ct.apply(u(this),arguments)},reduceRight:function(t){return Pt.apply(u(this),arguments)},reverse:function(){for(var t,e=this,n=u(e).length,r=Math.floor(n/2),a=0;a<r;)t=e[a],e[a++]=e[--n],e[n]=t;return e},some:function(t){return ft(u(this),t,1<arguments.length?arguments[1]:void 0)},sort:function(t){return Lt.call(u(this),t)},subarray:function(t,e){var n=u(this),r=n.length,t=Q(t,r);return new(a(n,n[T]))(n.buffer,n.byteOffset+t*n.BYTES_PER_ELEMENT,v((void 0===e?r:Q(e,r))-t))}},zt=function(t,e){return Nt(this,Et.call(u(this),t,e))},Ut=function(t){u(this);var e=_(arguments[1],1),n=this.length,r=X(t),a=v(r.length),o=0;if(n<a+e)throw x(D);for(;o<a;)this[e+o]=r[o++]},N={entries:function(){return wt.call(u(this))},keys:function(){return bt.call(u(this))},values:function(){return yt.call(u(this))}},jt=function(t,e){return y(t)&&t[B]&&"symbol"!=typeof e&&e in t&&String(+e)==String(e)},d=function(t,e){return jt(t,e=W(e,!0))?U(2,t[e]):ot(t,e)},F=function(t,e,n){return!(jt(t,e=W(e,!0))&&y(n)&&r(n,"value"))||r(n,"get")||r(n,"set")||n.configurable||r(n,"writable")&&!n.writable||r(n,"enumerable")&&!n.enumerable?w(t,e,n):(t[e]=n.value,t)},c||(t.f=d,at.f=F),h(h.S+h.F*!c,"Object",{getOwnPropertyDescriptor:d,defineProperty:F}),A(function(){S.call({})})&&(S=l=function(){return St.call(this)}),I=e({},Mt),e(I,N),m(I,L,N.values),e(I,{slice:zt,set:Ut,constructor:function(){},toString:S,toLocaleString:Ot}),f(I,"buffer","b"),f(I,"byteOffset","o"),f(I,"byteLength","l"),f(I,"length","e"),w(I,E,{get:function(){return this[B]}}),R.exports=function(t,l,e,c){var u=t+((c=!!c)?"Clamped":"")+"Array",p="get"+t,f="set"+t,d=s[u],a=d||{},t=d&&q(d),n=!d||!g.ABV,r={},o=d&&d[C],n=(n?(d=e(function(t,e,n,r){z(t,d,u,"_d");var a,o,i=0,s=0;if(y(e)){if(!(e instanceof P||(A=Y(e))==st||A==At))return B in e?k(d,e):Ft.call(d,e);var A=e,s=_(n,l),n=e.byteLength;if(void 0===r){if(n%l)throw x(D);if((a=n-s)<0)throw x(D)}else if(n<(a=v(r)*l)+s)throw x(D);o=a/l}else o=G(e),A=new P(a=o*l);for(m(t,"_d",{b:A,o:s,l:a,e:o,v:new ct(A)});i<o;)!function(t,r){w(t,r,{get:function(){return(t=this._d).v[p](r*l+t.o,_t);var t},set:function(t){var e=r,n=this._d;c&&(t=(t=Math.round(t))<0?0:255<t?255:255&t),n.v[f](e*l+n.o,t,_t)},enumerable:!0})}(t,i++)}),o=d[C]=V(I),m(o,"constructor",d)):A(function(){d(1)})&&A(function(){new d(-1)})&&tt(function(t){new d,new d(null),new d(1.5),new d(t)},!0)||(d=e(function(t,e,n,r){return z(t,d,u),y(e)?e instanceof P||(t=Y(e))==st||t==At?void 0!==r?new a(e,_(n,l),r):void 0!==n?new a(e,_(n,l)):new a(e):B in e?k(d,e):Ft.call(d,e):new a(G(e))}),ut(t!==Function.prototype?b(a).concat(b(t)):b(a),function(t){t in d||m(d,t,a[t])}),d[C]=o,i)||(o.constructor=d),o[L]),e=!!n&&("values"==n.name||null==n.name),t=N.values;m(d,Tt,!0),m(o,B,u),m(o,Bt,!0),m(o,T,d),(c?new d(1)[E]==u:E in o)||w(o,E,{get:function(){return u}}),r[u]=d,h(h.G+h.W+h.F*(d!=a),r),h(h.S,u,{BYTES_PER_ELEMENT:l}),h(h.S+h.F*A(function(){a.of.call(d,1)}),u,{from:Ft,of:It}),lt in o||m(o,lt,l),h(h.P,u,Mt),et(u),h(h.P+h.F*kt,u,{set:Ut}),h(h.P+h.F*!e,u,N),i||o.toString==S||(o.toString=S),h(h.P+h.F*A(function(){new d(1).slice()}),u,{slice:zt}),h(h.P+h.F*(A(function(){return[1,2].toLocaleString()!=new d([1,2]).toLocaleString()})||!A(function(){o.toLocaleString.call([1,2])})),u,{toLocaleString:Ot}),$[u]=e?n:t,i||e||m(o,L,t)}):R.exports=function(){}},{101:101,103:103,105:105,116:116,117:117,123:123,127:127,137:137,138:138,139:139,141:141,142:142,143:143,145:145,146:146,147:147,152:152,153:153,164:164,37:37,39:39,40:40,41:41,42:42,47:47,54:54,58:58,62:62,64:64,70:70,71:71,72:72,78:78,81:81,86:86,88:88,89:89,98:98,99:99}],145:[function(t,R,e){"use strict";var n=t(70),r=t(58),O=t(89),a=t(146),o=t(72),i=t(117),s=t(64),A=t(37),M=t(139),z=t(141),c=t(138),l=t(103).f,U=t(99).f,j=t(40),t=t(124),u="ArrayBuffer",p="DataView",f="prototype",d="Wrong index!",h=n[u],g=n[p],m=n.Math,v=n.RangeError,y=n.Infinity,b=h,G=m.abs,w=m.pow,Q=m.floor,W=m.log,Y=m.LN2,n="byteLength",m="byteOffset",x=r?"_b":"buffer",C=r?"_l":n,P=r?"_o":m;function S(t,e,n){var r,a,o,i=new Array(n),s=8*n-e-1,n=(1<<s)-1,A=n>>1,l=23===e?w(2,-24)-w(2,-77):0,c=0,u=t<0||0===t&&1/t<0?1:0;for((t=G(t))!=t||t===y?(a=t!=t?1:0,r=n):(r=Q(W(t)/Y),t*(o=w(2,-r))<1&&(r--,o*=2),2<=(t+=1<=r+A?l/o:l*w(2,1-A))*o&&(r++,o/=2),n<=r+A?(a=0,r=n):1<=r+A?(a=(t*o-1)*w(2,e),r+=A):(a=t*w(2,A-1)*w(2,e),r=0));8<=e;i[c++]=255&a,a/=256,e-=8);for(r=r<<e|a,s+=e;0<s;i[c++]=255&r,r/=256,s-=8);return i[--c]|=128*u,i}function L(t,e,n){var r,a=8*n-e-1,o=(1<<a)-1,i=o>>1,s=a-7,A=n-1,a=t[A--],l=127&a;for(a>>=7;0<s;l=256*l+t[A],A--,s-=8);for(r=l&(1<<-s)-1,l>>=-s,s+=e;0<s;r=256*r+t[A],A--,s-=8);if(0===l)l=1-i;else{if(l===o)return r?NaN:a?-y:y;r+=w(2,e),l-=i}return(a?-1:1)*r*w(2,l-e)}function E(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]}function T(t){return[255&t]}function B(t){return[255&t,t>>8&255]}function D(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function X(t){return S(t,52,8)}function H(t){return S(t,23,4)}function _(t,e,n){U(t[f],e,{get:function(){return this[n]}})}function k(t,e,n,r){n=c(+n);if(n+e>t[C])throw v(d);var a=t[x]._b,n=n+t[P],t=a.slice(n,n+e);return r?t:t.reverse()}function N(t,e,n,r,a,o){n=c(+n);if(n+e>t[C])throw v(d);for(var i=t[x]._b,s=n+t[P],A=r(+a),l=0;l<e;l++)i[s+l]=A[o?l:e-l-1]}if(a.ABV){if(!s(function(){h(1)})||!s(function(){new h(-1)})||s(function(){return new h,new h(1.5),new h(NaN),h.name!=u})){for(var F,s=(h=function(t){return A(this,h),new b(c(t))})[f]=b[f],I=l(b),V=0;I.length>V;)(F=I[V++])in h||o(h,F,b[F]);O||(s.constructor=h)}var l=new g(new h(2)),q=g[f].setInt8;l.setInt8(0,2147483648),l.setInt8(1,2147483649),!l.getInt8(0)&&l.getInt8(1)||i(g[f],{setInt8:function(t,e){q.call(this,t,e<<24>>24)},setUint8:function(t,e){q.call(this,t,e<<24>>24)}},!0)}else h=function(t){A(this,h,u);t=c(t);this._b=j.call(new Array(t),0),this[C]=t},g=function(t,e,n){A(this,g,p),A(t,h,p);var r=t[C],e=M(e);if(e<0||r<e)throw v("Wrong offset!");if(r<e+(n=void 0===n?r-e:z(n)))throw v("Wrong length!");this[x]=t,this[P]=e,this[C]=n},r&&(_(h,n,"_l"),_(g,"buffer","_b"),_(g,n,"_l"),_(g,m,"_o")),i(g[f],{getInt8:function(t){return k(this,1,t)[0]<<24>>24},getUint8:function(t){return k(this,1,t)[0]},getInt16:function(t){t=k(this,2,t,arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(t){t=k(this,2,t,arguments[1]);return t[1]<<8|t[0]},getInt32:function(t){return E(k(this,4,t,arguments[1]))},getUint32:function(t){return E(k(this,4,t,arguments[1]))>>>0},getFloat32:function(t){return L(k(this,4,t,arguments[1]),23,4)},getFloat64:function(t){return L(k(this,8,t,arguments[1]),52,8)},setInt8:function(t,e){N(this,1,t,T,e)},setUint8:function(t,e){N(this,1,t,T,e)},setInt16:function(t,e){N(this,2,t,B,e,arguments[2])},setUint16:function(t,e){N(this,2,t,B,e,arguments[2])},setInt32:function(t,e){N(this,4,t,D,e,arguments[2])},setUint32:function(t,e){N(this,4,t,D,e,arguments[2])},setFloat32:function(t,e){N(this,4,t,H,e,arguments[2])},setFloat64:function(t,e){N(this,8,t,X,e,arguments[2])}});t(h,u),t(g,p),o(g[f],a.VIEW,!0),e[u]=h,e[p]=g},{103:103,117:117,124:124,138:138,139:139,141:141,146:146,37:37,40:40,58:58,64:64,70:70,72:72,89:89,99:99}],146:[function(t,e,n){for(var r,a=t(70),o=t(72),t=t(147),i=t("typed_array"),s=t("view"),t=!(!a.ArrayBuffer||!a.DataView),A=t,l=0,c="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");l<9;)(r=a[c[l++]])?(o(r.prototype,i,!0),o(r.prototype,s,!0)):A=!1;e.exports={ABV:t,CONSTR:A,TYPED:i,VIEW:s}},{147:147,70:70,72:72}],147:[function(t,e,n){var r=0,a=Math.random();e.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++r+a).toString(36))}},{}],148:[function(t,e,n){t=t(70).navigator;e.exports=t&&t.userAgent||""},{70:70}],149:[function(t,e,n){var r=t(81);e.exports=function(t,e){if(r(t)&&t._t===e)return t;throw TypeError("Incompatible receiver, "+e+" required!")}},{81:81}],150:[function(t,e,n){var r=t(70),a=t(52),o=t(89),i=t(151),s=t(99).f;e.exports=function(t){var e=a.Symbol||(a.Symbol=!o&&r.Symbol||{});"_"==t.charAt(0)||t in e||s(e,t,{value:i.f(t)})}},{151:151,52:52,70:70,89:89,99:99}],151:[function(t,e,n){n.f=t(152)},{152:152}],152:[function(t,e,n){var r=t(126)("wks"),a=t(147),o=t(70).Symbol,i="function"==typeof o;(e.exports=function(t){return r[t]||(r[t]=i&&o[t]||(i?o:a)("Symbol."+t))}).store=r},{126:126,147:147,70:70}],153:[function(t,e,n){var r=t(47),a=t(152)("iterator"),o=t(88);e.exports=t(52).getIteratorMethod=function(t){if(null!=t)return t[a]||t["@@iterator"]||o[r(t)]}},{152:152,47:47,52:52,88:88}],154:[function(t,e,n){var r=t(62);r(r.P,"Array",{copyWithin:t(39)}),t(35)("copyWithin")},{35:35,39:39,62:62}],155:[function(t,e,n){"use strict";var r=t(62),a=t(42)(4);r(r.P+r.F*!t(128)([].every,!0),"Array",{every:function(t){return a(this,t,arguments[1])}})},{128:128,42:42,62:62}],156:[function(t,e,n){var r=t(62);r(r.P,"Array",{fill:t(40)}),t(35)("fill")},{35:35,40:40,62:62}],157:[function(t,e,n){"use strict";var r=t(62),a=t(42)(2);r(r.P+r.F*!t(128)([].filter,!0),"Array",{filter:function(t){return a(this,t,arguments[1])}})},{128:128,42:42,62:62}],158:[function(t,e,n){"use strict";var r=t(62),a=t(42)(6),o="findIndex",i=!0;o in[]&&Array(1)[o](function(){i=!1}),r(r.P+r.F*i,"Array",{findIndex:function(t){return a(this,t,1<arguments.length?arguments[1]:void 0)}}),t(35)(o)},{35:35,42:42,62:62}],159:[function(t,e,n){"use strict";var r=t(62),a=t(42)(5),o=!0;"find"in[]&&Array(1).find(function(){o=!1}),r(r.P+r.F*o,"Array",{find:function(t){return a(this,t,1<arguments.length?arguments[1]:void 0)}}),t(35)("find")},{35:35,42:42,62:62}],160:[function(t,e,n){"use strict";var r=t(62),a=t(42)(0),t=t(128)([].forEach,!0);r(r.P+r.F*!t,"Array",{forEach:function(t){return a(this,t,arguments[1])}})},{128:128,42:42,62:62}],161:[function(t,e,n){"use strict";var u=t(54),r=t(62),p=t(142),f=t(83),d=t(78),h=t(141),g=t(53),m=t(153);r(r.S+r.F*!t(86)(function(t){Array.from(t)}),"Array",{from:function(t){var e,n,r,a,o=p(t),t="function"==typeof this?this:Array,i=arguments.length,s=1<i?arguments[1]:void 0,A=void 0!==s,l=0,c=m(o);if(A&&(s=u(s,2<i?arguments[2]:void 0,2)),null==c||t==Array&&d(c))for(n=new t(e=h(o.length));l<e;l++)g(n,l,A?s(o[l],l):o[l]);else for(a=c.call(o),n=new t;!(r=a.next()).done;l++)g(n,l,A?f(a,s,[r.value,l],!0):r.value);return n.length=l,n}})},{141:141,142:142,153:153,53:53,54:54,62:62,78:78,83:83,86:86}],162:[function(t,e,n){"use strict";var r=t(62),a=t(41)(!1),o=[].indexOf,i=!!o&&1/[1].indexOf(1,-0)<0;r(r.P+r.F*(i||!t(128)(o)),"Array",{indexOf:function(t){return i?o.apply(this,arguments)||0:a(this,t,arguments[1])}})},{128:128,41:41,62:62}],163:[function(t,e,n){var r=t(62);r(r.S,"Array",{isArray:t(79)})},{62:62,79:79}],164:[function(t,e,n){"use strict";var r=t(35),a=t(87),o=t(88),i=t(140);e.exports=t(85)(Array,"Array",function(t,e){this._t=i(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,a(1)):a(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])},"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},{140:140,35:35,85:85,87:87,88:88}],165:[function(t,e,n){"use strict";var r=t(62),a=t(140),o=[].join;r(r.P+r.F*(t(77)!=Object||!t(128)(o)),"Array",{join:function(t){return o.call(a(this),void 0===t?",":t)}})},{128:128,140:140,62:62,77:77}],166:[function(t,e,n){"use strict";var r=t(62),a=t(140),o=t(139),i=t(141),s=[].lastIndexOf,A=!!s&&1/[1].lastIndexOf(1,-0)<0;r(r.P+r.F*(A||!t(128)(s)),"Array",{lastIndexOf:function(t){if(A)return s.apply(this,arguments)||0;var e=a(this),n=i(e.length),r=n-1;for((r=1<arguments.length?Math.min(r,o(arguments[1])):r)<0&&(r=n+r);0<=r;r--)if(r in e&&e[r]===t)return r||0;return-1}})},{128:128,139:139,140:140,141:141,62:62}],167:[function(t,e,n){"use strict";var r=t(62),a=t(42)(1);r(r.P+r.F*!t(128)([].map,!0),"Array",{map:function(t){return a(this,t,arguments[1])}})},{128:128,42:42,62:62}],168:[function(t,e,n){"use strict";var r=t(62),a=t(53);r(r.S+r.F*t(64)(function(){function t(){}return!(Array.of.call(t)instanceof t)}),"Array",{of:function(){for(var t=0,e=arguments.length,n=new("function"==typeof this?this:Array)(e);t<e;)a(n,t,arguments[t++]);return n.length=e,n}})},{53:53,62:62,64:64}],169:[function(t,e,n){"use strict";var r=t(62),a=t(43);r(r.P+r.F*!t(128)([].reduceRight,!0),"Array",{reduceRight:function(t){return a(this,t,arguments.length,arguments[1],!0)}})},{128:128,43:43,62:62}],170:[function(t,e,n){"use strict";var r=t(62),a=t(43);r(r.P+r.F*!t(128)([].reduce,!0),"Array",{reduce:function(t){return a(this,t,arguments.length,arguments[1],!1)}})},{128:128,43:43,62:62}],171:[function(t,e,n){"use strict";var r=t(62),a=t(73),A=t(48),l=t(137),c=t(141),u=[].slice;r(r.P+r.F*t(64)(function(){a&&u.call(a)}),"Array",{slice:function(t,e){var n=c(this.length),r=A(this);if(e=void 0===e?n:e,"Array"==r)return u.call(this,t,e);for(var a=l(t,n),t=l(e,n),o=c(t-a),i=new Array(o),s=0;s<o;s++)i[s]="String"==r?this.charAt(a+s):this[a+s];return i}})},{137:137,141:141,48:48,62:62,64:64,73:73}],172:[function(t,e,n){"use strict";var r=t(62),a=t(42)(3);r(r.P+r.F*!t(128)([].some,!0),"Array",{some:function(t){return a(this,t,arguments[1])}})},{128:128,42:42,62:62}],173:[function(t,e,n){"use strict";var r=t(62),a=t(33),o=t(142),i=t(64),s=[].sort,A=[1,2,3];r(r.P+r.F*(i(function(){A.sort(void 0)})||!i(function(){A.sort(null)})||!t(128)(s)),"Array",{sort:function(t){return void 0===t?s.call(o(this)):s.call(o(this),a(t))}})},{128:128,142:142,33:33,62:62,64:64}],174:[function(t,e,n){t(123)("Array")},{123:123}],175:[function(t,e,n){t=t(62);t(t.S,"Date",{now:function(){return(new Date).getTime()}})},{62:62}],176:[function(t,e,n){var r=t(62),t=t(55);r(r.P+r.F*(Date.prototype.toISOString!==t),"Date",{toISOString:t})},{55:55,62:62}],177:[function(t,e,n){"use strict";var r=t(62),a=t(142),o=t(143);r(r.P+r.F*t(64)(function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}),"Date",{toJSON:function(t){var e=a(this),n=o(e);return"number"!=typeof n||isFinite(n)?e.toISOString():null}})},{142:142,143:143,62:62,64:64}],178:[function(t,e,n){var r=t(152)("toPrimitive"),a=Date.prototype;r in a||t(72)(a,r,t(56))},{152:152,56:56,72:72}],179:[function(t,e,n){var r=Date.prototype,a="Invalid Date",o=r.toString,i=r.getTime;new Date(NaN)+""!=a&&t(118)(r,"toString",function(){var t=i.call(this);return t==t?o.call(this):a})},{118:118}],180:[function(t,e,n){var r=t(62);r(r.P,"Function",{bind:t(46)})},{46:46,62:62}],181:[function(t,e,n){"use strict";var r=t(81),a=t(105),o=t(152)("hasInstance"),i=Function.prototype;o in i||t(99).f(i,o,{value:function(t){if("function"==typeof this&&r(t)){if(!r(this.prototype))return t instanceof this;for(;t=a(t);)if(this.prototype===t)return!0}return!1}})},{105:105,152:152,81:81,99:99}],182:[function(t,e,n){var r=t(99).f,a=Function.prototype,o=/^\s*function ([^ (]*)/;"name"in a||t(58)&&r(a,"name",{configurable:!0,get:function(){try{return(""+this).match(o)[1]}catch(t){return""}}})},{58:58,99:99}],183:[function(t,e,n){"use strict";var r=t(49),a=t(149);e.exports=t(51)("Map",function(t){return function(){return t(this,0<arguments.length?arguments[0]:void 0)}},{get:function(t){t=r.getEntry(a(this,"Map"),t);return t&&t.v},set:function(t,e){return r.def(a(this,"Map"),0===t?0:t,e)}},r,!0)},{149:149,49:49,51:51}],184:[function(t,e,n){var r=t(62),a=t(92),o=Math.sqrt,t=Math.acosh;r(r.S+r.F*!(t&&710==Math.floor(t(Number.MAX_VALUE))&&t(1/0)==1/0),"Math",{acosh:function(t){return(t=+t)<1?NaN:94906265.62425156<t?Math.log(t)+Math.LN2:a(t-1+o(t-1)*o(t+1))}})},{62:62,92:92}],185:[function(t,e,n){var t=t(62),r=Math.asinh;t(t.S+t.F*!(r&&0<1/r(0)),"Math",{asinh:function t(e){return isFinite(e=+e)&&0!=e?e<0?-t(-e):Math.log(e+Math.sqrt(e*e+1)):e}})},{62:62}],186:[function(t,e,n){var t=t(62),r=Math.atanh;t(t.S+t.F*!(r&&1/r(-0)<0),"Math",{atanh:function(t){return 0==(t=+t)?t:Math.log((1+t)/(1-t))/2}})},{62:62}],187:[function(t,e,n){var r=t(62),a=t(93);r(r.S,"Math",{cbrt:function(t){return a(t=+t)*Math.pow(Math.abs(t),1/3)}})},{62:62,93:93}],188:[function(t,e,n){t=t(62);t(t.S,"Math",{clz32:function(t){return(t>>>=0)?31-Math.floor(Math.log(t+.5)*Math.LOG2E):32}})},{62:62}],189:[function(t,e,n){var t=t(62),r=Math.exp;t(t.S,"Math",{cosh:function(t){return(r(t=+t)+r(-t))/2}})},{62:62}],190:[function(t,e,n){var r=t(62),t=t(90);r(r.S+r.F*(t!=Math.expm1),"Math",{expm1:t})},{62:62,90:90}],191:[function(t,e,n){var r=t(62);r(r.S,"Math",{fround:t(91)})},{62:62,91:91}],192:[function(t,e,n){var t=t(62),A=Math.abs;t(t.S,"Math",{hypot:function(t,e){for(var n,r,a=0,o=0,i=arguments.length,s=0;o<i;)s<(n=A(arguments[o++]))?(a=a*(r=s/n)*r+1,s=n):a+=0<n?(r=n/s)*r:n;return s===1/0?1/0:s*Math.sqrt(a)}})},{62:62}],193:[function(t,e,n){var r=t(62),a=Math.imul;r(r.S+r.F*t(64)(function(){return-5!=a(4294967295,5)||2!=a.length}),"Math",{imul:function(t,e){var t=+t,e=+e,n=65535&t,r=65535&e;return 0|n*r+((65535&t>>>16)*r+n*(65535&e>>>16)<<16>>>0)}})},{62:62,64:64}],194:[function(t,e,n){t=t(62);t(t.S,"Math",{log10:function(t){return Math.log(t)*Math.LOG10E}})},{62:62}],195:[function(t,e,n){var r=t(62);r(r.S,"Math",{log1p:t(92)})},{62:62,92:92}],196:[function(t,e,n){t=t(62);t(t.S,"Math",{log2:function(t){return Math.log(t)/Math.LN2}})},{62:62}],197:[function(t,e,n){var r=t(62);r(r.S,"Math",{sign:t(93)})},{62:62,93:93}],198:[function(t,e,n){var r=t(62),a=t(90),o=Math.exp;r(r.S+r.F*t(64)(function(){return-2e-17!=!Math.sinh(-2e-17)}),"Math",{sinh:function(t){return Math.abs(t=+t)<1?(a(t)-a(-t))/2:(o(t-1)-o(-t-1))*(Math.E/2)}})},{62:62,64:64,90:90}],199:[function(t,e,n){var r=t(62),a=t(90),o=Math.exp;r(r.S,"Math",{tanh:function(t){var e=a(t=+t),n=a(-t);return e==1/0?1:n==1/0?-1:(e-n)/(o(t)+o(-t))}})},{62:62,90:90}],200:[function(t,e,n){t=t(62);t(t.S,"Math",{trunc:function(t){return(0<t?Math.floor:Math.ceil)(t)}})},{62:62}],201:[function(t,e,n){"use strict";function r(t){var e=l(t,!1);if("string"==typeof e&&2<e.length){var n,r,a,t=(e=v?e.trim():f(e,3)).charCodeAt(0);if(43===t||45===t){if(88===(n=e.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(e.charCodeAt(1)){case 66:case 98:r=2,a=49;break;case 79:case 111:r=8,a=55;break;default:return+e}for(var o,i=e.slice(2),s=0,A=i.length;s<A;s++)if((o=i.charCodeAt(s))<48||a<o)return NaN;return parseInt(i,r)}}return+e}var a=t(70),o=t(71),i=t(48),s=t(75),l=t(143),A=t(64),c=t(103).f,u=t(101).f,p=t(99).f,f=t(134).trim,d="Number",h=b=a[d],g=b.prototype,m=i(t(98)(g))==d,v="trim"in String.prototype;if(!b(" 0o1")||!b("0b1")||b("+0x1")){for(var y,b=function(t){var t=arguments.length<1?0:t,e=this;return e instanceof b&&(m?A(function(){g.valueOf.call(e)}):i(e)!=d)?s(new h(r(t)),e,b):r(t)},w=t(58)?c(h):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),x=0;w.length>x;x++)o(h,y=w[x])&&!o(b,y)&&p(b,y,u(h,y));(b.prototype=g).constructor=b,t(118)(a,d,b)}},{101:101,103:103,118:118,134:134,143:143,48:48,58:58,64:64,70:70,71:71,75:75,98:98,99:99}],202:[function(t,e,n){t=t(62);t(t.S,"Number",{EPSILON:Math.pow(2,-52)})},{62:62}],203:[function(t,e,n){var r=t(62),a=t(70).isFinite;r(r.S,"Number",{isFinite:function(t){return"number"==typeof t&&a(t)}})},{62:62,70:70}],204:[function(t,e,n){var r=t(62);r(r.S,"Number",{isInteger:t(80)})},{62:62,80:80}],205:[function(t,e,n){t=t(62);t(t.S,"Number",{isNaN:function(t){return t!=t}})},{62:62}],206:[function(t,e,n){var r=t(62),a=t(80),o=Math.abs;r(r.S,"Number",{isSafeInteger:function(t){return a(t)&&o(t)<=9007199254740991}})},{62:62,80:80}],207:[function(t,e,n){t=t(62);t(t.S,"Number",{MAX_SAFE_INTEGER:9007199254740991})},{62:62}],208:[function(t,e,n){t=t(62);t(t.S,"Number",{MIN_SAFE_INTEGER:-9007199254740991})},{62:62}],209:[function(t,e,n){var r=t(62),t=t(112);r(r.S+r.F*(Number.parseFloat!=t),"Number",{parseFloat:t})},{112:112,62:62}],210:[function(t,e,n){var r=t(62),t=t(113);r(r.S+r.F*(Number.parseInt!=t),"Number",{parseInt:t})},{113:113,62:62}],211:[function(t,e,n){"use strict";function s(t,e){for(var n=-1,r=e;++n<6;)i[n]=(r+=t*i[n])%1e7,r=o(r/1e7)}function A(t){for(var e=6,n=0;0<=--e;)i[e]=o((n+=i[e])/t),n=n%t*1e7}function l(){for(var t,e=6,n="";0<=--e;)""===n&&0!==e&&0===i[e]||(t=String(i[e]),n=""===n?t:n+f.call("0",7-t.length)+t);return n}function c(t,e,n){return 0===e?n:e%2==1?c(t,e-1,n*t):c(t*t,e/2,n)}var r=t(62),u=t(139),p=t(34),f=t(133),a=1..toFixed,o=Math.floor,i=[0,0,0,0,0,0],d="Number.toFixed: incorrect invocation!";r(r.P+r.F*(!!a&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==0xde0b6b3a7640080.toFixed(0))||!t(64)(function(){a.call({})})),"Number",{toFixed:function(t){var e,n,r,a=p(this,d),t=u(t),o="",i="0";if(t<0||20<t)throw RangeError(d);if(a!=a)return"NaN";if(a<=-1e21||1e21<=a)return String(a);if(a<0&&(o="-",a=-a),1e-21<a)if(r=(e=function(){for(var t=0,e=a*c(2,69,1);4096<=e;)t+=12,e/=4096;for(;2<=e;)t+=1,e/=2;return t}()-69)<0?a*c(2,-e,1):a/c(2,e,1),r*=4503599627370496,0<(e=52-e)){for(s(0,r),n=t;7<=n;)s(1e7,0),n-=7;for(s(c(10,n,1),0),n=e-1;23<=n;)A(1<<23),n-=23;A(1<<n),s(1,1),A(2),i=l()}else s(0,r),s(1<<-e,0),i=l()+f.call("0",t);return 0<t?o+((r=i.length)<=t?"0."+f.call("0",t-r)+i:i.slice(0,r-t)+"."+i.slice(r-t)):o+i}})},{133:133,139:139,34:34,62:62,64:64}],212:[function(t,e,n){"use strict";var r=t(62),a=t(64),o=t(34),i=1..toPrecision;r(r.P+r.F*(a(function(){return"1"!==i.call(1,void 0)})||!a(function(){i.call({})})),"Number",{toPrecision:function(t){var e=o(this,"Number#toPrecision: incorrect invocation!");return void 0===t?i.call(e):i.call(e,t)}})},{34:34,62:62,64:64}],213:[function(t,e,n){var r=t(62);r(r.S+r.F,"Object",{assign:t(97)})},{62:62,97:97}],214:[function(t,e,n){var r=t(62);r(r.S,"Object",{create:t(98)})},{62:62,98:98}],215:[function(t,e,n){var r=t(62);r(r.S+r.F*!t(58),"Object",{defineProperties:t(100)})},{100:100,58:58,62:62}],216:[function(t,e,n){var r=t(62);r(r.S+r.F*!t(58),"Object",{defineProperty:t(99).f})},{58:58,62:62,99:99}],217:[function(t,e,n){var r=t(81),a=t(94).onFreeze;t(109)("freeze",function(e){return function(t){return e&&r(t)?e(a(t)):t}})},{109:109,81:81,94:94}],218:[function(t,e,n){var r=t(140),a=t(101).f;t(109)("getOwnPropertyDescriptor",function(){return function(t,e){return a(r(t),e)}})},{101:101,109:109,140:140}],219:[function(t,e,n){t(109)("getOwnPropertyNames",function(){return t(102).f})},{102:102,109:109}],220:[function(t,e,n){var r=t(142),a=t(105);t(109)("getPrototypeOf",function(){return function(t){return a(r(t))}})},{105:105,109:109,142:142}],221:[function(t,e,n){var r=t(81);t(109)("isExtensible",function(e){return function(t){return!!r(t)&&(!e||e(t))}})},{109:109,81:81}],222:[function(t,e,n){var r=t(81);t(109)("isFrozen",function(e){return function(t){return!r(t)||!!e&&e(t)}})},{109:109,81:81}],223:[function(t,e,n){var r=t(81);t(109)("isSealed",function(e){return function(t){return!r(t)||!!e&&e(t)}})},{109:109,81:81}],224:[function(t,e,n){var r=t(62);r(r.S,"Object",{is:t(121)})},{121:121,62:62}],225:[function(t,e,n){var r=t(142),a=t(107);t(109)("keys",function(){return function(t){return a(r(t))}})},{107:107,109:109,142:142}],226:[function(t,e,n){var r=t(81),a=t(94).onFreeze;t(109)("preventExtensions",function(e){return function(t){return e&&r(t)?e(a(t)):t}})},{109:109,81:81,94:94}],227:[function(t,e,n){var r=t(81),a=t(94).onFreeze;t(109)("seal",function(e){return function(t){return e&&r(t)?e(a(t)):t}})},{109:109,81:81,94:94}],228:[function(t,e,n){var r=t(62);r(r.S,"Object",{setPrototypeOf:t(122).set})},{122:122,62:62}],229:[function(t,e,n){"use strict";var r=t(47),a={};a[t(152)("toStringTag")]="z",a+""!="[object z]"&&t(118)(Object.prototype,"toString",function(){return"[object "+r(this)+"]"},!0)},{118:118,152:152,47:47}],230:[function(t,e,n){var r=t(62),t=t(112);r(r.G+r.F*(parseFloat!=t),{parseFloat:t})},{112:112,62:62}],231:[function(t,e,n){var r=t(62),t=t(113);r(r.G+r.F*(parseInt!=t),{parseInt:t})},{113:113,62:62}],232:[function(n,R,O){"use strict";function r(){}function d(t){var e;return!(!f(t)||"function"!=typeof(e=t.then))&&e}function a(u,p){var f;u._n||(u._n=!0,f=u._c,x(function(){for(var t=u._v,e=1==u._s,n=0;f.length>n;){a=void 0;o=void 0;i=void 0;s=void 0;A=void 0;l=void 0;c=void 0;var r=f[n++];var a,o,i,s=e?r.ok:r.fail,A=r.resolve,l=r.reject,c=r.domain;try{s?(e||(2==u._h&&g(u),u._h=1),!0===s?a=t:(c&&c.enter(),a=s(t),c&&(c.exit(),i=!0)),a===r.promise?l(T("Promise-chain cycle")):(o=d(a))?o.call(a,A,l):A(a)):l(t)}catch(r){c&&!i&&c.exit(),l(r)}}u._c=[],u._n=!1,p&&!u._h&&h(u)}))}function o(t){var e=this;e._d||(e._d=!0,(e=e._w||e)._v=t,e._s=2,e._a||(e._a=e._c.slice()),a(e,!0))}function h(a){w.call(c,function(){var t,e,n=a._v,r=F(a);if(r&&(t=P(function(){k?B.emit("unhandledRejection",n,a):(e=c.onunhandledrejection)?e({promise:a,reason:n}):(e=c.console)&&e.error&&e.error("Unhandled promise rejection",n)}),a._h=k||F(a)?2:1),a._a=void 0,r&&t.e)throw t.v})}function g(e){w.call(c,function(){var t;k?B.emit("rejectionHandled",e):(t=c.onrejectionhandled)&&t({promise:e,reason:e._v})})}var e,i,s,A,l=n(89),c=n(70),u=n(54),t=n(47),p=n(62),f=n(81),m=n(33),v=n(37),y=n(68),b=n(127),w=n(136).set,x=n(95)(),C=n(96),P=n(114),S=n(148),L=n(115),E="Promise",T=c.TypeError,B=c.process,D=B&&B.versions,M=D&&D.v8||"",_=c[E],k="process"==t(B),N=i=C.f,D=!!function(){try{var t=_.resolve(1),e=(t.constructor={})[n(152)("species")]=function(t){t(r,r)};return(k||"function"==typeof PromiseRejectionEvent)&&t.then(r)instanceof e&&0!==M.indexOf("6.6")&&-1===S.indexOf("Chrome/66")}catch(t){}}(),F=function(t){return 1!==t._h&&0===(t._a||t._c).length},I=function(t){var n,r=this;if(!r._d){r._d=!0,r=r._w||r;try{if(r===t)throw T("Promise can't be resolved itself");(n=d(t))?x(function(){var e={_w:r,_d:!1};try{n.call(t,u(I,e,1),u(o,e,1))}catch(t){o.call(e,t)}}):(r._v=t,r._s=1,a(r,!1))}catch(t){o.call({_w:r,_d:!1},t)}}};D||(_=function(t){v(this,_,E,"_h"),m(t),e.call(this);try{t(u(I,this,1),u(o,this,1))}catch(t){o.call(this,t)}},(e=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=n(117)(_.prototype,{then:function(t,e){var n=N(b(this,_));return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,n.domain=k?B.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&a(this,!1),n.promise},catch:function(t){return this.then(void 0,t)}}),s=function(){var t=new e;this.promise=t,this.resolve=u(I,t,1),this.reject=u(o,t,1)},C.f=N=function(t){return t===_||t===A?new s:i(t)}),p(p.G+p.W+p.F*!D,{Promise:_}),n(124)(_,E),n(123)(E),A=n(52)[E],p(p.S+p.F*!D,E,{reject:function(t){var e=N(this);return(0,e.reject)(t),e.promise}}),p(p.S+p.F*(l||!D),E,{resolve:function(t){return L(l&&this===A?_:this,t)}}),p(p.S+p.F*!(D&&n(86)(function(t){_.all(t).catch(r)})),E,{all:function(t){var i=this,e=N(i),s=e.resolve,A=e.reject,n=P(function(){var r=[],a=0,o=1;y(t,!1,function(t){var e=a++,n=!1;r.push(void 0),o++,i.resolve(t).then(function(t){n||(n=!0,r[e]=t,--o)||s(r)},A)}),--o||s(r)});return n.e&&A(n.v),e.promise},race:function(t){var e=this,n=N(e),r=n.reject,a=P(function(){y(t,!1,function(t){e.resolve(t).then(n.resolve,r)})});return a.e&&r(a.v),n.promise}})},{114:114,115:115,117:117,123:123,124:124,127:127,136:136,148:148,152:152,33:33,37:37,47:47,52:52,54:54,62:62,68:68,70:70,81:81,86:86,89:89,95:95,96:96}],233:[function(t,e,n){var r=t(62),a=t(33),o=t(38),i=(t(70).Reflect||{}).apply,s=Function.apply;r(r.S+r.F*!t(64)(function(){i(function(){})}),"Reflect",{apply:function(t,e,n){t=a(t),n=o(n);return i?i(t,e,n):s.call(t,e,n)}})},{33:33,38:38,62:62,64:64,70:70}],234:[function(t,e,n){var r=t(62),a=t(98),o=t(33),i=t(38),s=t(81),A=t(64),l=t(46),c=(t(70).Reflect||{}).construct,u=A(function(){function t(){}return!(c(function(){},[],t)instanceof t)}),p=!A(function(){c(function(){})});r(r.S+r.F*(u||p),"Reflect",{construct:function(t,e){o(t),i(e);var n=arguments.length<3?t:o(arguments[2]);if(p&&!u)return c(t,e,n);if(t==n){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var r=[null];return r.push.apply(r,e),new(l.apply(t,r))}r=n.prototype,n=a(s(r)?r:Object.prototype),r=Function.apply.call(t,n,e);return s(r)?r:n}})},{33:33,38:38,46:46,62:62,64:64,70:70,81:81,98:98}],235:[function(t,e,n){var r=t(99),a=t(62),o=t(38),i=t(143);a(a.S+a.F*t(64)(function(){Reflect.defineProperty(r.f({},1,{value:1}),1,{value:2})}),"Reflect",{defineProperty:function(t,e,n){o(t),e=i(e,!0),o(n);try{return r.f(t,e,n),!0}catch(t){return!1}}})},{143:143,38:38,62:62,64:64,99:99}],236:[function(t,e,n){var r=t(62),a=t(101).f,o=t(38);r(r.S,"Reflect",{deleteProperty:function(t,e){var n=a(o(t),e);return!(n&&!n.configurable)&&delete t[e]}})},{101:101,38:38,62:62}],237:[function(t,e,n){"use strict";function r(t){this._t=o(t),this._i=0;var e,n=this._k=[];for(e in t)n.push(e)}var a=t(62),o=t(38);t(84)(r,"Object",function(){var t,e=this._k;do{if(this._i>=e.length)return{value:void 0,done:!0}}while(!((t=e[this._i++])in this._t));return{value:t,done:!1}}),a(a.S,"Reflect",{enumerate:function(t){return new r(t)}})},{38:38,62:62,84:84}],238:[function(t,e,n){var r=t(101),a=t(62),o=t(38);a(a.S,"Reflect",{getOwnPropertyDescriptor:function(t,e){return r.f(o(t),e)}})},{101:101,38:38,62:62}],239:[function(t,e,n){var r=t(62),a=t(105),o=t(38);r(r.S,"Reflect",{getPrototypeOf:function(t){return a(o(t))}})},{105:105,38:38,62:62}],240:[function(t,e,n){var o=t(101),i=t(105),s=t(71),r=t(62),A=t(81),l=t(38);r(r.S,"Reflect",{get:function t(e,n){var r,a=arguments.length<3?e:arguments[2];return l(e)===a?e[n]:(r=o.f(e,n))?s(r,"value")?r.value:void 0!==r.get?r.get.call(a):void 0:A(r=i(e))?t(r,n,a):void 0}})},{101:101,105:105,38:38,62:62,71:71,81:81}],241:[function(t,e,n){t=t(62);t(t.S,"Reflect",{has:function(t,e){return e in t}})},{62:62}],242:[function(t,e,n){var r=t(62),a=t(38),o=Object.isExtensible;r(r.S,"Reflect",{isExtensible:function(t){return a(t),!o||o(t)}})},{38:38,62:62}],243:[function(t,e,n){var r=t(62);r(r.S,"Reflect",{ownKeys:t(111)})},{111:111,62:62}],244:[function(t,e,n){var r=t(62),a=t(38),o=Object.preventExtensions;r(r.S,"Reflect",{preventExtensions:function(t){a(t);try{return o&&o(t),!0}catch(t){return!1}}})},{38:38,62:62}],245:[function(t,e,n){var r=t(62),a=t(122);a&&r(r.S,"Reflect",{setPrototypeOf:function(t,e){a.check(t,e);try{return a.set(t,e),!0}catch(t){return!1}}})},{122:122,62:62}],246:[function(t,e,n){var s=t(99),A=t(101),l=t(105),c=t(71),r=t(62),u=t(116),p=t(38),f=t(81);r(r.S,"Reflect",{set:function t(e,n,r){var a,o=arguments.length<4?e:arguments[3],i=A.f(p(e),n);if(!i){if(f(a=l(e)))return t(a,n,r,o);i=u(0)}if(c(i,"value")){if(!1===i.writable||!f(o))return!1;if(a=A.f(o,n)){if(a.get||a.set||!1===a.writable)return!1;a.value=r,s.f(o,n,a)}else s.f(o,n,u(0,r));return!0}return void 0!==i.set&&(i.set.call(o,r),!0)}})},{101:101,105:105,116:116,38:38,62:62,71:71,81:81,99:99}],247:[function(t,e,n){var r=t(70),o=t(75),a=t(99).f,i=t(103).f,s=t(82),A=t(66),l=d=r.RegExp,c=d.prototype,u=/a/g,p=/a/g,f=new d(u)!==u;if(t(58)&&(!f||t(64)(function(){return p[t(152)("match")]=!1,d(u)!=u||d(p)==p||"/a/i"!=d(u,"i")}))){for(var d=function(t,e){var n=this instanceof d,r=s(t),a=void 0===e;return!n&&r&&t.constructor===d&&a?t:o(f?new l(r&&!a?t.source:t,e):l((r=t instanceof d)?t.source:t,r&&a?A.call(t):e),n?this:c,d)},h=i(l),g=0;h.length>g;)!function(e){e in d||a(d,e,{configurable:!0,get:function(){return l[e]},set:function(t){l[e]=t}})}(h[g++]);(c.constructor=d).prototype=c,t(118)(r,"RegExp",d)}t(123)("RegExp")},{103:103,118:118,123:123,152:152,58:58,64:64,66:66,70:70,75:75,82:82,99:99}],248:[function(t,e,n){"use strict";var r=t(120);t(62)({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},{120:120,62:62}],249:[function(t,e,n){t(58)&&"g"!=/./g.flags&&t(99).f(RegExp.prototype,"flags",{configurable:!0,get:t(66)})},{58:58,66:66,99:99}],250:[function(t,e,n){"use strict";var c=t(38),u=t(141),p=t(36),f=t(119);t(65)("match",1,function(r,a,A,l){return[function(t){var e=r(this),n=null==t?void 0:t[a];return void 0!==n?n.call(t,e):new RegExp(t)[a](String(e))},function(t){var e=l(A,t,this);if(e.done)return e.value;var n=c(t),r=String(this);if(!n.global)return f(n,r);for(var a=n.unicode,o=[],i=n.lastIndex=0;null!==(s=f(n,r));){var s=String(s[0]);""===(o[i]=s)&&(n.lastIndex=p(r,u(n.lastIndex),a)),i++}return 0===i?null:o}]})},{119:119,141:141,36:36,38:38,65:65}],251:[function(t,e,n){"use strict";var x=t(38),C=t(142),P=t(141),S=t(139),L=t(36),E=t(119),T=Math.max,B=Math.min,D=Math.floor,_=/\$([$&`']|\d\d?|<[^>]*>)/g,k=/\$([$&`']|\d\d?)/g;t(65)("replace",2,function(a,o,b,w){return[function(t,e){var n=a(this),r=null==t?void 0:t[o];return void 0!==r?r.call(t,n,e):b.call(String(n),t,e)},function(t,e){var n=w(b,t,this,e);if(n.done)return n.value;var r,a=x(t),o=String(this),i="function"==typeof e,s=(i||(e=String(e)),a.global);s&&(r=a.unicode,a.lastIndex=0);for(var A=[];;){var l=E(a,o);if(null===l)break;if(A.push(l),!s)break;""===String(l[0])&&(a.lastIndex=L(o,P(a.lastIndex),r))}for(var c,u="",p=0,f=0;f<A.length;f++){for(var l=A[f],d=String(l[0]),h=T(B(S(l.index),o.length),0),g=[],m=1;m<l.length;m++)g.push(void 0===(c=l[m])?c:String(c));var v=l.groups,y=i?(y=[d].concat(g,h,o),void 0!==v&&y.push(v),String(e.apply(void 0,y))):function(o,i,s,A,l,t){var c=s+o.length,u=A.length,e=k;return void 0!==l&&(l=C(l),e=_),b.call(t,e,function(t,e){var n;switch(e.charAt(0)){case"$":return"$";case"&":return o;case"`":return i.slice(0,s);case"'":return i.slice(c);case"<":n=l[e.slice(1,-1)];break;default:var r,a=+e;if(0==a)return t;if(u<a)return 0!==(r=D(a/10))&&r<=u?void 0===A[r-1]?e.charAt(1):A[r-1]+e.charAt(1):t;n=A[a-1]}return void 0===n?"":n})}(d,o,h,g,v,e);p<=h&&(u+=o.slice(p,h)+y,p=h+d.length)}return u+o.slice(p)}]})},{119:119,139:139,141:141,142:142,36:36,38:38,65:65}],252:[function(t,e,n){"use strict";var s=t(38),A=t(121),l=t(119);t(65)("search",1,function(r,a,o,i){return[function(t){var e=r(this),n=null==t?void 0:t[a];return void 0!==n?n.call(t,e):new RegExp(t)[a](String(e))},function(t){var e,n=i(o,t,this);return n.done?n.value:(n=s(t),t=String(this),e=n.lastIndex,A(e,0)||(n.lastIndex=0),t=l(n,t),A(n.lastIndex,e)||(n.lastIndex=e),null===t?-1:t.index)}]})},{119:119,121:121,38:38,65:65}],253:[function(t,e,n){"use strict";var u=t(82),m=t(38),v=t(127),y=t(36),b=t(141),w=t(119),p=t(120),r=t(64),x=Math.min,f=[].push,i="split",C="length",P="lastIndex",S=4294967295,L=!r(function(){RegExp(S,"y")});t(65)("split",2,function(a,o,d,h){var g="c"=="abbc"[i](/(b)*/)[1]||4!="test"[i](/(?:)/,-1)[C]||2!="ab"[i](/(?:ab)*/)[C]||4!="."[i](/(.?)(.?)/)[C]||1<"."[i](/()()/)[C]||""[i](/.?/)[C]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!u(t))return d.call(n,t,e);for(var r,a,o,i=[],s=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),A=0,l=void 0===e?S:e>>>0,c=new RegExp(t.source,s+"g");(r=p.call(c,n))&&!(A<(a=c[P])&&(i.push(n.slice(A,r.index)),1<r[C]&&r.index<n[C]&&f.apply(i,r.slice(1)),o=r[0][C],A=a,l<=i[C]));)c[P]===r.index&&c[P]++;return A===n[C]?!o&&c.test("")||i.push(""):i.push(n.slice(A)),l<i[C]?i.slice(0,l):i}:"0"[i](void 0,0)[C]?function(t,e){return void 0===t&&0===e?[]:d.call(this,t,e)}:d;return[function(t,e){var n=a(this),r=null==t?void 0:t[o];return void 0!==r?r.call(t,n,e):g.call(String(n),t,e)},function(t,e){var n=h(g,t,this,e,g!==d);if(n.done)return n.value;var n=m(t),r=String(this),t=v(n,RegExp),a=n.unicode,o=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.unicode?"u":"")+(L?"y":"g"),i=new t(L?n:"^(?:"+n.source+")",o),s=void 0===e?S:e>>>0;if(0==s)return[];if(0===r.length)return null===w(i,r)?[r]:[];for(var A=0,l=0,c=[];l<r.length;){i.lastIndex=L?l:0;var u,p=w(i,L?r:r.slice(l));if(null===p||(u=x(b(i.lastIndex+(L?0:l)),r.length))===A)l=y(r,l,a);else{if(c.push(r.slice(A,l)),c.length===s)return c;for(var f=1;f<=p.length-1;f++)if(c.push(p[f]),c.length===s)return c;l=A=u}}return c.push(r.slice(A)),c}]})},{119:119,120:120,127:127,141:141,36:36,38:38,64:64,65:65,82:82}],254:[function(e,t,n){"use strict";function r(t){e(118)(RegExp.prototype,s,t,!0)}e(249);var a=e(38),o=e(66),i=e(58),s="toString",A=/./[s];e(64)(function(){return"/a/b"!=A.call({source:"a",flags:"b"})})?r(function(){var t=a(this);return"/".concat(t.source,"/","flags"in t?t.flags:!i&&t instanceof RegExp?o.call(t):void 0)}):A.name!=s&&r(function(){return A.call(this)})},{118:118,249:249,38:38,58:58,64:64,66:66}],255:[function(t,e,n){"use strict";var r=t(49),a=t(149);e.exports=t(51)("Set",function(t){return function(){return t(this,0<arguments.length?arguments[0]:void 0)}},{add:function(t){return r.def(a(this,"Set"),t=0===t?0:t,t)}},r)},{149:149,49:49,51:51}],256:[function(t,e,n){"use strict";t(131)("anchor",function(e){return function(t){return e(this,"a","name",t)}})},{131:131}],257:[function(t,e,n){"use strict";t(131)("big",function(t){return function(){return t(this,"big","","")}})},{131:131}],258:[function(t,e,n){"use strict";t(131)("blink",function(t){return function(){return t(this,"blink","","")}})},{131:131}],259:[function(t,e,n){"use strict";t(131)("bold",function(t){return function(){return t(this,"b","","")}})},{131:131}],260:[function(t,e,n){"use strict";var r=t(62),a=t(129)(!1);r(r.P,"String",{codePointAt:function(t){return a(this,t)}})},{129:129,62:62}],261:[function(t,e,n){"use strict";var r=t(62),a=t(141),o=t(130),i="endsWith",s=""[i];r(r.P+r.F*t(63)(i),"String",{endsWith:function(t){var e=o(this,t,i),n=1<arguments.length?arguments[1]:void 0,r=a(e.length),n=void 0===n?r:Math.min(a(n),r),r=String(t);return s?s.call(e,r,n):e.slice(n-r.length,n)===r}})},{130:130,141:141,62:62,63:63}],262:[function(t,e,n){"use strict";t(131)("fixed",function(t){return function(){return t(this,"tt","","")}})},{131:131}],263:[function(t,e,n){"use strict";t(131)("fontcolor",function(e){return function(t){return e(this,"font","color",t)}})},{131:131}],264:[function(t,e,n){"use strict";t(131)("fontsize",function(e){return function(t){return e(this,"font","size",t)}})},{131:131}],265:[function(t,e,n){var r=t(62),o=t(137),i=String.fromCharCode,t=String.fromCodePoint;r(r.S+r.F*(!!t&&1!=t.length),"String",{fromCodePoint:function(t){for(var e,n=[],r=arguments.length,a=0;a<r;){if(e=+arguments[a++],o(e,1114111)!==e)throw RangeError(e+" is not a valid code point");n.push(e<65536?i(e):i(55296+((e-=65536)>>10),e%1024+56320))}return n.join("")}})},{137:137,62:62}],266:[function(t,e,n){"use strict";var r=t(62),a=t(130);r(r.P+r.F*t(63)("includes"),"String",{includes:function(t){return!!~a(this,t,"includes").indexOf(t,1<arguments.length?arguments[1]:void 0)}})},{130:130,62:62,63:63}],267:[function(t,e,n){"use strict";t(131)("italics",function(t){return function(){return t(this,"i","","")}})},{131:131}],268:[function(t,e,n){"use strict";var r=t(129)(!0);t(85)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t=this._t,e=this._i;return e>=t.length?{value:void 0,done:!0}:(t=r(t,e),this._i+=t.length,{value:t,done:!1})})},{129:129,85:85}],269:[function(t,e,n){"use strict";t(131)("link",function(e){return function(t){return e(this,"a","href",t)}})},{131:131}],270:[function(t,e,n){var r=t(62),i=t(140),s=t(141);r(r.S,"String",{raw:function(t){for(var e=i(t.raw),n=s(e.length),r=arguments.length,a=[],o=0;o<n;)a.push(String(e[o++])),o<r&&a.push(String(arguments[o]));return a.join("")}})},{140:140,141:141,62:62}],271:[function(t,e,n){var r=t(62);r(r.P,"String",{repeat:t(133)})},{133:133,62:62}],272:[function(t,e,n){"use strict";t(131)("small",function(t){return function(){return t(this,"small","","")}})},{131:131}],273:[function(t,e,n){"use strict";var r=t(62),a=t(141),o=t(130),i="startsWith",s=""[i];r(r.P+r.F*t(63)(i),"String",{startsWith:function(t){var e=o(this,t,i),n=a(Math.min(1<arguments.length?arguments[1]:void 0,e.length)),t=String(t);return s?s.call(e,t,n):e.slice(n,n+t.length)===t}})},{130:130,141:141,62:62,63:63}],274:[function(t,e,n){"use strict";t(131)("strike",function(t){return function(){return t(this,"strike","","")}})},{131:131}],275:[function(t,e,n){"use strict";t(131)("sub",function(t){return function(){return t(this,"sub","","")}})},{131:131}],276:[function(t,e,n){"use strict";t(131)("sup",function(t){return function(){return t(this,"sup","","")}})},{131:131}],277:[function(t,e,n){"use strict";t(134)("trim",function(t){return function(){return t(this,3)}})},{134:134}],278:[function(t,R,O){"use strict";function r(t){var e=B[t]=b(C[L]);return e._k=t,e}function n(t,e){g(t);for(var n,r=Q(e=m(e)),a=0,o=r.length;a<o;)s(t,n=r[a++],e[n]);return t}function e(t){var e=tt.call(this,t=v(t,!0));return!(this===_&&l(B,t)&&!l(D,t))&&(!(e||!l(this,t)||!l(B,t)||l(this,E)&&this[E][t])||e)}function a(t,e){var n;if(t=m(t),e=v(e,!0),t!==_||!l(B,e)||l(D,e))return!(n=K(t,e))||!l(B,e)||l(t,E)&&t[E][e]||(n.enumerable=!0),n}function o(t){for(var e,n=Z(m(t)),r=[],a=0;n.length>a;)l(B,e=n[a++])||e==E||e==z||r.push(e);return r}function i(t){for(var e,n=t===_,r=Z(n?D:m(t)),a=[],o=0;r.length>o;)!l(B,e=r[o++])||n&&!l(_,e)||a.push(B[e]);return a}function s(t,e,n){return t===_&&s(D,e,n),g(t),e=v(e,!0),g(n),(l(B,e)?(n.enumerable?(l(t,E)&&t[E][e]&&(t[E][e]=!1),n=b(n,{enumerable:y(0,!1)})):(l(t,E)||x(t,E,y(1,{})),t[E][e]=!0),F):x)(t,e,n)}var A=t(70),l=t(71),c=t(58),u=t(62),M=t(118),z=t(94).KEY,p=t(64),f=t(126),d=t(124),U=t(147),h=t(152),j=t(151),G=t(150),Q=t(61),W=t(79),g=t(38),Y=t(81),X=t(142),m=t(140),v=t(143),y=t(116),b=t(98),H=t(102),V=t(101),w=t(104),q=t(99),J=t(107),K=V.f,x=q.f,Z=H.f,C=A.Symbol,P=A.JSON,S=P&&P.stringify,L="prototype",E=h("_hidden"),$=h("toPrimitive"),tt={}.propertyIsEnumerable,T=f("symbol-registry"),B=f("symbols"),D=f("op-symbols"),_=Object[L],f="function"==typeof C&&!!w.f,k=A.QObject,N=!k||!k[L]||!k[L].findChild,F=c&&p(function(){return 7!=b(x({},"a",{get:function(){return x(this,"a",{value:7}).a}})).a})?function(t,e,n){var r=K(_,e);r&&delete _[e],x(t,e,n),r&&t!==_&&x(_,e,r)}:x,I=f&&"symbol"==typeof C.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof C};f||(M((C=function(){if(this instanceof C)throw TypeError("Symbol is not a constructor!");var e=U(0<arguments.length?arguments[0]:void 0),n=function(t){this===_&&n.call(D,t),l(this,E)&&l(this[E],e)&&(this[E][e]=!1),F(this,e,y(1,t))};return c&&N&&F(_,e,{configurable:!0,set:n}),r(e)})[L],"toString",function(){return this._k}),V.f=a,q.f=s,t(103).f=H.f=o,t(108).f=e,w.f=i,c&&!t(89)&&M(_,"propertyIsEnumerable",e,!0),j.f=function(t){return r(h(t))}),u(u.G+u.W+u.F*!f,{Symbol:C});for(var et="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),nt=0;et.length>nt;)h(et[nt++]);for(var rt=J(h.store),at=0;rt.length>at;)G(rt[at++]);u(u.S+u.F*!f,"Symbol",{for:function(t){return l(T,t+="")?T[t]:T[t]=C(t)},keyFor:function(t){if(!I(t))throw TypeError(t+" is not a symbol!");for(var e in T)if(T[e]===t)return e},useSetter:function(){N=!0},useSimple:function(){N=!1}}),u(u.S+u.F*!f,"Object",{create:function(t,e){return void 0===e?b(t):n(b(t),e)},defineProperty:s,defineProperties:n,getOwnPropertyDescriptor:a,getOwnPropertyNames:o,getOwnPropertySymbols:i});k=p(function(){w.f(1)});u(u.S+u.F*k,"Object",{getOwnPropertySymbols:function(t){return w.f(X(t))}}),P&&u(u.S+u.F*(!f||p(function(){var t=C();return"[null]"!=S([t])||"{}"!=S({a:t})||"{}"!=S(Object(t))})),"JSON",{stringify:function(t){for(var e,n,r=[t],a=1;a<arguments.length;)r.push(arguments[a++]);if(n=e=r[1],(Y(e)||void 0!==t)&&!I(t))return W(e)||(e=function(t,e){if("function"==typeof n&&(e=n.call(this,t,e)),!I(e))return e}),r[1]=e,S.apply(P,r)}}),C[L][$]||t(72)(C[L],$,C[L].valueOf),d(C,"Symbol"),d(Math,"Math",!0),d(A.JSON,"JSON",!0)},{101:101,102:102,103:103,104:104,107:107,108:108,116:116,118:118,124:124,126:126,140:140,142:142,143:143,147:147,150:150,151:151,152:152,38:38,58:58,61:61,62:62,64:64,70:70,71:71,72:72,79:79,81:81,89:89,94:94,98:98,99:99}],279:[function(t,e,n){"use strict";var r=t(62),a=t(146),o=t(145),A=t(38),l=t(137),c=t(141),i=t(81),s=t(70).ArrayBuffer,u=t(127),p=o.ArrayBuffer,f=o.DataView,d=a.ABV&&s.isView,h=p.prototype.slice,g=a.VIEW,o="ArrayBuffer";r(r.G+r.W+r.F*(s!==p),{ArrayBuffer:p}),r(r.S+r.F*!a.CONSTR,o,{isView:function(t){return d&&d(t)||i(t)&&g in t}}),r(r.P+r.U+r.F*t(64)(function(){return!new p(2).slice(1,void 0).byteLength}),o,{slice:function(t,e){if(void 0!==h&&void 0===e)return h.call(A(this),t);for(var n=A(this).byteLength,r=l(t,n),a=l(void 0===e?n:e,n),t=new(u(this,p))(c(a-r)),o=new f(this),i=new f(t),s=0;r<a;)i.setUint8(s++,o.getUint8(r++));return t}}),t(123)(o)},{123:123,127:127,137:137,141:141,145:145,146:146,38:38,62:62,64:64,70:70,81:81}],280:[function(t,e,n){var r=t(62);r(r.G+r.W+r.F*!t(146).ABV,{DataView:t(145).DataView})},{145:145,146:146,62:62}],281:[function(t,e,n){t(144)("Float32",4,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],282:[function(t,e,n){t(144)("Float64",8,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],283:[function(t,e,n){t(144)("Int16",2,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],284:[function(t,e,n){t(144)("Int32",4,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],285:[function(t,e,n){t(144)("Int8",1,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],286:[function(t,e,n){t(144)("Uint16",2,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],287:[function(t,e,n){t(144)("Uint32",4,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],288:[function(t,e,n){t(144)("Uint8",1,function(r){return function(t,e,n){return r(this,t,e,n)}})},{144:144}],289:[function(t,e,n){t(144)("Uint8",1,function(r){return function(t,e,n){return r(this,t,e,n)}},!0)},{144:144}],290:[function(t,e,n){"use strict";function r(t){return function(){return t(this,0<arguments.length?arguments[0]:void 0)}}var a,o=t(70),i=t(42)(0),s=t(118),A=t(94),l=t(97),c=t(50),u=t(81),p=t(149),f=t(149),o=!o.ActiveXObject&&"ActiveXObject"in o,d="WeakMap",h=A.getWeak,g=Object.isExtensible,m=c.ufstore,v={get:function(t){var e;if(u(t))return!0===(e=h(t))?m(p(this,d)).get(t):e?e[this._i]:void 0},set:function(t,e){return c.def(p(this,d),t,e)}},y=e.exports=t(51)(d,r,v,c,!0,!0);f&&o&&(l((a=c.getConstructor(r,d)).prototype,v),A.NEED=!0,i(["delete","has","get","set"],function(n){var t=y.prototype,r=t[n];s(t,n,function(t,e){if(!u(t)||g(t))return r.call(this,t,e);this._f||(this._f=new a);t=this._f[n](t,e);return"set"==n?this:t})}))},{118:118,149:149,42:42,50:50,51:51,70:70,81:81,94:94,97:97}],291:[function(t,e,n){"use strict";var r=t(50),a=t(149);t(51)("WeakSet",function(t){return function(){return t(this,0<arguments.length?arguments[0]:void 0)}},{add:function(t){return r.def(a(this,"WeakSet"),t,!0)}},r,!1,!0)},{149:149,50:50,51:51}],292:[function(t,e,n){"use strict";var r=t(62),a=t(67),o=t(142),i=t(141),s=t(33),A=t(45);r(r.P,"Array",{flatMap:function(t){var e,n,r=o(this);return s(t),e=i(r.length),n=A(r,0),a(n,r,r,e,0,1,t,arguments[1]),n}}),t(35)("flatMap")},{141:141,142:142,33:33,35:35,45:45,62:62,67:67}],293:[function(t,e,n){"use strict";var r=t(62),a=t(41)(!0);r(r.P,"Array",{includes:function(t){return a(this,t,1<arguments.length?arguments[1]:void 0)}}),t(35)("includes")},{35:35,41:41,62:62}],294:[function(t,e,n){var r=t(62),a=t(110)(!0);r(r.S,"Object",{entries:function(t){return a(t)}})},{110:110,62:62}],295:[function(t,e,n){var r=t(62),A=t(111),l=t(140),c=t(101),u=t(53);r(r.S,"Object",{getOwnPropertyDescriptors:function(t){for(var e,n,r=l(t),a=c.f,o=A(r),i={},s=0;o.length>s;)void 0!==(n=a(r,e=o[s++]))&&u(i,e,n);return i}})},{101:101,111:111,140:140,53:53,62:62}],296:[function(t,e,n){var r=t(62),a=t(110)(!1);r(r.S,"Object",{values:function(t){return a(t)}})},{110:110,62:62}],297:[function(t,e,n){"use strict";var r=t(62),a=t(52),o=t(70),i=t(127),s=t(115);r(r.P+r.R,"Promise",{finally:function(e){var n=i(this,a.Promise||o.Promise),t="function"==typeof e;return this.then(t?function(t){return s(n,e()).then(function(){return t})}:e,t?function(t){return s(n,e()).then(function(){throw t})}:e)}})},{115:115,127:127,52:52,62:62,70:70}],298:[function(t,e,n){"use strict";var r=t(62),a=t(132),t=t(148),t=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(t);r(r.P+r.F*t,"String",{padEnd:function(t){return a(this,t,1<arguments.length?arguments[1]:void 0,!1)}})},{132:132,148:148,62:62}],299:[function(t,e,n){"use strict";var r=t(62),a=t(132),t=t(148),t=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(t);r(r.P+r.F*t,"String",{padStart:function(t){return a(this,t,1<arguments.length?arguments[1]:void 0,!0)}})},{132:132,148:148,62:62}],300:[function(t,e,n){"use strict";t(134)("trimLeft",function(t){return function(){return t(this,1)}},"trimStart")},{134:134}],301:[function(t,e,n){"use strict";t(134)("trimRight",function(t){return function(){return t(this,2)}},"trimEnd")},{134:134}],302:[function(t,e,n){t(150)("asyncIterator")},{150:150}],303:[function(t,e,n){for(var r=t(164),a=t(107),o=t(118),i=t(70),s=t(72),A=t(88),t=t(152),l=t("iterator"),c=t("toStringTag"),u=A.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},f=a(p),d=0;d<f.length;d++){var h,g=f[d],m=p[g],v=i[g],y=v&&v.prototype;if(y&&(y[l]||s(y,l,u),y[c]||s(y,c,g),A[g]=u,m))for(h in r)y[h]||o(y,h,r[h],!0)}},{107:107,118:118,152:152,164:164,70:70,72:72,88:88}],304:[function(t,e,n){var r=t(62),t=t(136);r(r.G+r.B,{setImmediate:t.set,clearImmediate:t.clear})},{136:136,62:62}],305:[function(t,e,n){function r(a){return function(t,e){var n=2<arguments.length,r=n&&i.call(arguments,2);return a(n?function(){("function"==typeof t?t:Function(t)).apply(this,r)}:t,e)}}var a=t(70),o=t(62),t=t(148),i=[].slice,t=/MSIE .\./.test(t);o(o.G+o.B+o.F*t,{setTimeout:r(a.setTimeout),setInterval:r(a.setInterval)})},{148:148,62:62,70:70}],306:[function(t,e,n){t(305),t(304),t(303),e.exports=t(52)},{303:303,304:304,305:305,52:52}],307:[function(t,e,n){e=function(o){"use strict";var A,t=Object.prototype,l=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",a=e.toStringTag||"@@toStringTag";function i(t,e,n,r){var a,o,i,s,e=e&&e.prototype instanceof g?e:g,e=Object.create(e.prototype),r=new P(r||[]);return e._invoke=(a=t,o=n,i=r,s=u,function(t,e){if(s===f)throw new Error("Generator is already running");if(s===d){if("throw"===t)throw e;return L()}for(i.method=t,i.arg=e;;){var n=i.delegate;if(n){n=function t(e,n){var r=e.iterator[n.method];if(r===A){if(n.delegate=null,"throw"===n.method){if(e.iterator.return&&(n.method="return",n.arg=A,t(e,n),"throw"===n.method))return h;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}r=c(r,e.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,h;r=r.arg;return r?r.done?(n[e.resultName]=r.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=A),n.delegate=null,h):r:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}(n,i);if(n){if(n===h)continue;return n}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(s===u)throw s=d,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);s=f;n=c(a,o,i);if("normal"===n.type){if(s=i.done?d:p,n.arg===h)continue;return{value:n.arg,done:i.done}}"throw"===n.type&&(s=d,i.method="throw",i.arg=n.arg)}}),e}function c(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}o.wrap=i;var u="suspendedStart",p="suspendedYield",f="executing",d="completed",h={};function g(){}function s(){}function m(){}var e={},v=(e[r]=function(){return this},Object.getPrototypeOf),v=v&&v(v(S([]))),y=(v&&v!==t&&l.call(v,r)&&(e=v),m.prototype=g.prototype=Object.create(e));function b(t){["next","throw","return"].forEach(function(e){t[e]=function(t){return this._invoke(e,t)}})}function w(i){var e;this._invoke=function(n,r){function t(){return new Promise(function(t,e){!function e(t,n,r,a){var o,t=c(i[t],i,n);return"throw"!==t.type?(n=(o=t.arg).value)&&"object"==typeof n&&l.call(n,"__await")?Promise.resolve(n.__await).then(function(t){e("next",t,r,a)},function(t){e("throw",t,r,a)}):Promise.resolve(n).then(function(t){o.value=t,r(o)},function(t){return e("throw",t,r,a)}):void a(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()}}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function S(e){if(e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(l.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=A,t.done=!0,t}).next=t}return{next:L}}function L(){return{value:A,done:!0}}return(s.prototype=y.constructor=m).constructor=s,m[a]=s.displayName="GeneratorFunction",o.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===s||"GeneratorFunction"===(t.displayName||t.name))},o.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,a in t||(t[a]="GeneratorFunction")),t.prototype=Object.create(y),t},o.awrap=function(t){return{__await:t}},b(w.prototype),w.prototype[n]=function(){return this},o.AsyncIterator=w,o.async=function(t,e,n,r){var a=new w(i(t,e,n,r));return o.isGeneratorFunction(e)?a:a.next().then(function(t){return t.done?t.value:a.next()})},b(y),y[a]="Generator",y[r]=function(){return this},y.toString=function(){return"[object Generator]"},o.keys=function(n){var t,r=[];for(t in n)r.push(t);return r.reverse(),function t(){for(;r.length;){var e=r.pop();if(e in n)return t.value=e,t.done=!1,t}return t.done=!0,t}},o.values=S,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(C),!t)for(var e in this)"t"===e.charAt(0)&&l.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=A)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function t(t,e){return o.type="throw",o.arg=n,r.next=t,e&&(r.method="next",r.arg=A),!!e}for(var e=this.tryEntries.length-1;0<=e;--e){var a=this.tryEntries[e],o=a.completion;if("root"===a.tryLoc)return t("end");if(a.tryLoc<=this.prev){var i=l.call(a,"catchLoc"),s=l.call(a,"finallyLoc");if(i&&s){if(this.prev<a.catchLoc)return t(a.catchLoc,!0);if(this.prev<a.finallyLoc)return t(a.finallyLoc)}else if(i){if(this.prev<a.catchLoc)return t(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return t(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&l.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}var o=(a=a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc?null:a)?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),C(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,a=this.tryEntries[e];if(a.tryLoc===t)return"throw"===(n=a.completion).type&&(r=n.arg,C(a)),r}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:S(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=A),h}},o}("object"==typeof e?e.exports:{});try{regeneratorRuntime=e}catch(t){Function("r","regeneratorRuntime = r")(e)}},{}]},{},[1]);var PptxGenJS=function(){"use strict";function E(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var T=E(JSZip),y=function(){return(y=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)};function u(t,i,s,A){return new(s=s||Promise)(function(n,e){function r(t){try{o(A.next(t))}catch(t){e(t)}}function a(t){try{o(A.throw(t))}catch(t){e(t)}}function o(t){var e;t.done?n(t.value):((e=t.value)instanceof s?e:new s(function(t){t(e)})).then(r,a)}o((A=A.apply(t,i||[])).next())})}function p(r,a){var o,i,s,A={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},l={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l;function t(n){return function(t){var e=[n,t];if(o)throw new TypeError("Generator is already executing.");for(;A=l&&e[l=0]?0:A;)try{if(o=1,i&&(s=2&e[0]?i.return:e[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,e[1])).done)return s;switch(i=0,(e=s?[2&e[0],s.value]:e)[0]){case 0:case 1:s=e;break;case 4:return A.label++,{value:e[1],done:!1};case 5:A.label++,i=e[1],e=[0];continue;case 7:e=A.ops.pop(),A.trys.pop();continue;default:if(!(s=0<(s=A.trys).length&&s[s.length-1])&&(6===e[0]||2===e[0])){A=0;continue}if(3===e[0]&&(!s||e[1]>s[0]&&e[1]<s[3]))A.label=e[1];else if(6===e[0]&&A.label<s[1])A.label=s[1],s=e;else{if(!(s&&A.label<s[2])){s[2]&&A.ops.pop(),A.trys.pop();continue}A.label=s[2],A.ops.push(e)}}e=a.call(r,A)}catch(t){e=[6,t],i=0}finally{o=s=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}}}function U(t,e,n){if(n||2===arguments.length)for(var r,a=0,o=e.length;a<o;a++)!r&&a in e||((r=r||Array.prototype.slice.call(e,0,a))[a]=e[a]);return t.concat(r||Array.prototype.slice.call(e))}var i,s,B,D,j,r,G,Q,A,b,W,o,_,a,f,t,k=914400,w=12700,d="\r\n",Y=2147483649,X=/^[0-9a-fA-F]{6}$/,H=1.67,V=27,q="solid",J="666666",K=1,Z=[.05,.1,.05,.1],$="363636",tt=1,x={color:"888888",style:"solid",size:1,cap:"flat"},C="000000",P=12,et=18,l="LAYOUT_16x9",nt="DEFAULT",rt="333333",c={type:"outer",blur:3,offset:23e3/12700,angle:90,color:"000000",opacity:.35,rotateWithShape:!0},at=[.5,.5,.5,.5],ot={type:"outer",blur:8,offset:4,angle:270,color:"000000",opacity:.75},it={size:8,color:"FFFFFF",opacity:.75},S="2094734552",st="2094734553",At="2094734554",lt="2094734555",ct="2094734556",ut="ABCDEFGHIJKLMNOPQRSTUVWXYZ".split(""),pt=["C0504D","4F81BD","9BBB59","8064A2","4BACC6","F79646","628FC6","C86360","C0504D","4F81BD","9BBB59","8064A2","4BACC6","F79646","628FC6","C86360"],ft=["5DA5DA","FAA43A","60BD68","F17CB0","B2912F","B276B2","DECF3F","F15854","A7A7A7","5DA5DA","FAA43A","60BD68","F17CB0","B2912F","B276B2","DECF3F","F15854","A7A7A7"],dt=((t=i=i||{}).left="left",t.center="center",t.right="right",t.justify="justify",(t=s=s||{}).b="b",t.ctr="ctr",t.t="t","{F7021451-1387-4CA6-816F-3879F97B5CBC}"),h=((t=B=B||{}).arraybuffer="arraybuffer",t.base64="base64",t.binarystring="binarystring",t.blob="blob",t.nodebuffer="nodebuffer",t.uint8array="uint8array",(t=D=D||{}).area="area",t.bar="bar",t.bar3d="bar3D",t.bubble="bubble",t.bubble3d="bubble3D",t.doughnut="doughnut",t.line="line",t.pie="pie",t.radar="radar",t.scatter="scatter",(t=j=j||{}).accentBorderCallout1="accentBorderCallout1",t.accentBorderCallout2="accentBorderCallout2",t.accentBorderCallout3="accentBorderCallout3",t.accentCallout1="accentCallout1",t.accentCallout2="accentCallout2",t.accentCallout3="accentCallout3",t.actionButtonBackPrevious="actionButtonBackPrevious",t.actionButtonBeginning="actionButtonBeginning",t.actionButtonBlank="actionButtonBlank",t.actionButtonDocument="actionButtonDocument",t.actionButtonEnd="actionButtonEnd",t.actionButtonForwardNext="actionButtonForwardNext",t.actionButtonHelp="actionButtonHelp",t.actionButtonHome="actionButtonHome",t.actionButtonInformation="actionButtonInformation",t.actionButtonMovie="actionButtonMovie",t.actionButtonReturn="actionButtonReturn",t.actionButtonSound="actionButtonSound",t.arc="arc",t.bentArrow="bentArrow",t.bentUpArrow="bentUpArrow",t.bevel="bevel",t.blockArc="blockArc",t.borderCallout1="borderCallout1",t.borderCallout2="borderCallout2",t.borderCallout3="borderCallout3",t.bracePair="bracePair",t.bracketPair="bracketPair",t.callout1="callout1",t.callout2="callout2",t.callout3="callout3",t.can="can",t.chartPlus="chartPlus",t.chartStar="chartStar",t.chartX="chartX",t.chevron="chevron",t.chord="chord",t.circularArrow="circularArrow",t.cloud="cloud",t.cloudCallout="cloudCallout",t.corner="corner",t.cornerTabs="cornerTabs",t.cube="cube",t.curvedDownArrow="curvedDownArrow",t.curvedLeftArrow="curvedLeftArrow",t.curvedRightArrow="curvedRightArrow",t.curvedUpArrow="curvedUpArrow",t.custGeom="custGeom",t.decagon="decagon",t.diagStripe="diagStripe",t.diamond="diamond",t.dodecagon="dodecagon",t.donut="donut",t.doubleWave="doubleWave",t.downArrow="downArrow",t.downArrowCallout="downArrowCallout",t.ellipse="ellipse",t.ellipseRibbon="ellipseRibbon",t.ellipseRibbon2="ellipseRibbon2",t.flowChartAlternateProcess="flowChartAlternateProcess",t.flowChartCollate="flowChartCollate",t.flowChartConnector="flowChartConnector",t.flowChartDecision="flowChartDecision",t.flowChartDelay="flowChartDelay",t.flowChartDisplay="flowChartDisplay",t.flowChartDocument="flowChartDocument",t.flowChartExtract="flowChartExtract",t.flowChartInputOutput="flowChartInputOutput",t.flowChartInternalStorage="flowChartInternalStorage",t.flowChartMagneticDisk="flowChartMagneticDisk",t.flowChartMagneticDrum="flowChartMagneticDrum",t.flowChartMagneticTape="flowChartMagneticTape",t.flowChartManualInput="flowChartManualInput",t.flowChartManualOperation="flowChartManualOperation",t.flowChartMerge="flowChartMerge",t.flowChartMultidocument="flowChartMultidocument",t.flowChartOfflineStorage="flowChartOfflineStorage",t.flowChartOffpageConnector="flowChartOffpageConnector",t.flowChartOnlineStorage="flowChartOnlineStorage",t.flowChartOr="flowChartOr",t.flowChartPredefinedProcess="flowChartPredefinedProcess",t.flowChartPreparation="flowChartPreparation",t.flowChartProcess="flowChartProcess",t.flowChartPunchedCard="flowChartPunchedCard",t.flowChartPunchedTape="flowChartPunchedTape",t.flowChartSort="flowChartSort",t.flowChartSummingJunction="flowChartSummingJunction",t.flowChartTerminator="flowChartTerminator",t.folderCorner="folderCorner",t.frame="frame",t.funnel="funnel",t.gear6="gear6",t.gear9="gear9",t.halfFrame="halfFrame",t.heart="heart",t.heptagon="heptagon",t.hexagon="hexagon",t.homePlate="homePlate",t.horizontalScroll="horizontalScroll",t.irregularSeal1="irregularSeal1",t.irregularSeal2="irregularSeal2",t.leftArrow="leftArrow",t.leftArrowCallout="leftArrowCallout",t.leftBrace="leftBrace",t.leftBracket="leftBracket",t.leftCircularArrow="leftCircularArrow",t.leftRightArrow="leftRightArrow",t.leftRightArrowCallout="leftRightArrowCallout",t.leftRightCircularArrow="leftRightCircularArrow",t.leftRightRibbon="leftRightRibbon",t.leftRightUpArrow="leftRightUpArrow",t.leftUpArrow="leftUpArrow",t.lightningBolt="lightningBolt",t.line="line",t.lineInv="lineInv",t.mathDivide="mathDivide",t.mathEqual="mathEqual",t.mathMinus="mathMinus",t.mathMultiply="mathMultiply",t.mathNotEqual="mathNotEqual",t.mathPlus="mathPlus",t.moon="moon",t.noSmoking="noSmoking",t.nonIsoscelesTrapezoid="nonIsoscelesTrapezoid",t.notchedRightArrow="notchedRightArrow",t.octagon="octagon",t.parallelogram="parallelogram",t.pentagon="pentagon",t.pie="pie",t.pieWedge="pieWedge",t.plaque="plaque",t.plaqueTabs="plaqueTabs",t.plus="plus",t.quadArrow="quadArrow",t.quadArrowCallout="quadArrowCallout",t.rect="rect",t.ribbon="ribbon",t.ribbon2="ribbon2",t.rightArrow="rightArrow",t.rightArrowCallout="rightArrowCallout",t.rightBrace="rightBrace",t.rightBracket="rightBracket",t.round1Rect="round1Rect",t.round2DiagRect="round2DiagRect",t.round2SameRect="round2SameRect",t.roundRect="roundRect",t.rtTriangle="rtTriangle",t.smileyFace="smileyFace",t.snip1Rect="snip1Rect",t.snip2DiagRect="snip2DiagRect",t.snip2SameRect="snip2SameRect",t.snipRoundRect="snipRoundRect",t.squareTabs="squareTabs",t.star10="star10",t.star12="star12",t.star16="star16",t.star24="star24",t.star32="star32",t.star4="star4",t.star5="star5",t.star6="star6",t.star7="star7",t.star8="star8",t.stripedRightArrow="stripedRightArrow",t.sun="sun",t.swooshArrow="swooshArrow",t.teardrop="teardrop",t.trapezoid="trapezoid",t.triangle="triangle",t.upArrow="upArrow",t.upArrowCallout="upArrowCallout",t.upDownArrow="upDownArrow",t.upDownArrowCallout="upDownArrowCallout",t.uturnArrow="uturnArrow",t.verticalScroll="verticalScroll",t.wave="wave",t.wedgeEllipseCallout="wedgeEllipseCallout",t.wedgeRectCallout="wedgeRectCallout",t.wedgeRoundRectCallout="wedgeRoundRectCallout",(t=r=r||{}).text1="tx1",t.text2="tx2",t.background1="bg1",t.background2="bg2",t.accent1="accent1",t.accent2="accent2",t.accent3="accent3",t.accent4="accent4",t.accent5="accent5",t.accent6="accent6",(t=G=G||{}).left="left",t.center="center",t.right="right",t.justify="justify",(t=Q=Q||{}).top="top",t.middle="middle",t.bottom="bottom",(t=A=A||{}).ACTION_BUTTON_BACK_OR_PREVIOUS="actionButtonBackPrevious",t.ACTION_BUTTON_BEGINNING="actionButtonBeginning",t.ACTION_BUTTON_CUSTOM="actionButtonBlank",t.ACTION_BUTTON_DOCUMENT="actionButtonDocument",t.ACTION_BUTTON_END="actionButtonEnd",t.ACTION_BUTTON_FORWARD_OR_NEXT="actionButtonForwardNext",t.ACTION_BUTTON_HELP="actionButtonHelp",t.ACTION_BUTTON_HOME="actionButtonHome",t.ACTION_BUTTON_INFORMATION="actionButtonInformation",t.ACTION_BUTTON_MOVIE="actionButtonMovie",t.ACTION_BUTTON_RETURN="actionButtonReturn",t.ACTION_BUTTON_SOUND="actionButtonSound",t.ARC="arc",t.BALLOON="wedgeRoundRectCallout",t.BENT_ARROW="bentArrow",t.BENT_UP_ARROW="bentUpArrow",t.BEVEL="bevel",t.BLOCK_ARC="blockArc",t.CAN="can",t.CHART_PLUS="chartPlus",t.CHART_STAR="chartStar",t.CHART_X="chartX",t.CHEVRON="chevron",t.CHORD="chord",t.CIRCULAR_ARROW="circularArrow",t.CLOUD="cloud",t.CLOUD_CALLOUT="cloudCallout",t.CORNER="corner",t.CORNER_TABS="cornerTabs",t.CROSS="plus",t.CUBE="cube",t.CURVED_DOWN_ARROW="curvedDownArrow",t.CURVED_DOWN_RIBBON="ellipseRibbon",t.CURVED_LEFT_ARROW="curvedLeftArrow",t.CURVED_RIGHT_ARROW="curvedRightArrow",t.CURVED_UP_ARROW="curvedUpArrow",t.CURVED_UP_RIBBON="ellipseRibbon2",t.CUSTOM_GEOMETRY="custGeom",t.DECAGON="decagon",t.DIAGONAL_STRIPE="diagStripe",t.DIAMOND="diamond",t.DODECAGON="dodecagon",t.DONUT="donut",t.DOUBLE_BRACE="bracePair",t.DOUBLE_BRACKET="bracketPair",t.DOUBLE_WAVE="doubleWave",t.DOWN_ARROW="downArrow",t.DOWN_ARROW_CALLOUT="downArrowCallout",t.DOWN_RIBBON="ribbon",t.EXPLOSION1="irregularSeal1",t.EXPLOSION2="irregularSeal2",t.FLOWCHART_ALTERNATE_PROCESS="flowChartAlternateProcess",t.FLOWCHART_CARD="flowChartPunchedCard",t.FLOWCHART_COLLATE="flowChartCollate",t.FLOWCHART_CONNECTOR="flowChartConnector",t.FLOWCHART_DATA="flowChartInputOutput",t.FLOWCHART_DECISION="flowChartDecision",t.FLOWCHART_DELAY="flowChartDelay",t.FLOWCHART_DIRECT_ACCESS_STORAGE="flowChartMagneticDrum",t.FLOWCHART_DISPLAY="flowChartDisplay",t.FLOWCHART_DOCUMENT="flowChartDocument",t.FLOWCHART_EXTRACT="flowChartExtract",t.FLOWCHART_INTERNAL_STORAGE="flowChartInternalStorage",t.FLOWCHART_MAGNETIC_DISK="flowChartMagneticDisk",t.FLOWCHART_MANUAL_INPUT="flowChartManualInput",t.FLOWCHART_MANUAL_OPERATION="flowChartManualOperation",t.FLOWCHART_MERGE="flowChartMerge",t.FLOWCHART_MULTIDOCUMENT="flowChartMultidocument",t.FLOWCHART_OFFLINE_STORAGE="flowChartOfflineStorage",t.FLOWCHART_OFFPAGE_CONNECTOR="flowChartOffpageConnector",t.FLOWCHART_OR="flowChartOr",t.FLOWCHART_PREDEFINED_PROCESS="flowChartPredefinedProcess",t.FLOWCHART_PREPARATION="flowChartPreparation",t.FLOWCHART_PROCESS="flowChartProcess",t.FLOWCHART_PUNCHED_TAPE="flowChartPunchedTape",t.FLOWCHART_SEQUENTIAL_ACCESS_STORAGE="flowChartMagneticTape",t.FLOWCHART_SORT="flowChartSort",t.FLOWCHART_STORED_DATA="flowChartOnlineStorage",t.FLOWCHART_SUMMING_JUNCTION="flowChartSummingJunction",t.FLOWCHART_TERMINATOR="flowChartTerminator",t.FOLDED_CORNER="folderCorner",t.FRAME="frame",t.FUNNEL="funnel",t.GEAR_6="gear6",t.GEAR_9="gear9",t.HALF_FRAME="halfFrame",t.HEART="heart",t.HEPTAGON="heptagon",t.HEXAGON="hexagon",t.HORIZONTAL_SCROLL="horizontalScroll",t.ISOSCELES_TRIANGLE="triangle",t.LEFT_ARROW="leftArrow",t.LEFT_ARROW_CALLOUT="leftArrowCallout",t.LEFT_BRACE="leftBrace",t.LEFT_BRACKET="leftBracket",t.LEFT_CIRCULAR_ARROW="leftCircularArrow",t.LEFT_RIGHT_ARROW="leftRightArrow",t.LEFT_RIGHT_ARROW_CALLOUT="leftRightArrowCallout",t.LEFT_RIGHT_CIRCULAR_ARROW="leftRightCircularArrow",t.LEFT_RIGHT_RIBBON="leftRightRibbon",t.LEFT_RIGHT_UP_ARROW="leftRightUpArrow",t.LEFT_UP_ARROW="leftUpArrow",t.LIGHTNING_BOLT="lightningBolt",t.LINE_CALLOUT_1="borderCallout1",t.LINE_CALLOUT_1_ACCENT_BAR="accentCallout1",t.LINE_CALLOUT_1_BORDER_AND_ACCENT_BAR="accentBorderCallout1",t.LINE_CALLOUT_1_NO_BORDER="callout1",t.LINE_CALLOUT_2="borderCallout2",t.LINE_CALLOUT_2_ACCENT_BAR="accentCallout2",t.LINE_CALLOUT_2_BORDER_AND_ACCENT_BAR="accentBorderCallout2",t.LINE_CALLOUT_2_NO_BORDER="callout2",t.LINE_CALLOUT_3="borderCallout3",t.LINE_CALLOUT_3_ACCENT_BAR="accentCallout3",t.LINE_CALLOUT_3_BORDER_AND_ACCENT_BAR="accentBorderCallout3",t.LINE_CALLOUT_3_NO_BORDER="callout3",t.LINE_CALLOUT_4="borderCallout3",t.LINE_CALLOUT_4_ACCENT_BAR="accentCallout3",t.LINE_CALLOUT_4_BORDER_AND_ACCENT_BAR="accentBorderCallout3",t.LINE_CALLOUT_4_NO_BORDER="callout3",t.LINE="line",t.LINE_INVERSE="lineInv",t.MATH_DIVIDE="mathDivide",t.MATH_EQUAL="mathEqual",t.MATH_MINUS="mathMinus",t.MATH_MULTIPLY="mathMultiply",t.MATH_NOT_EQUAL="mathNotEqual",t.MATH_PLUS="mathPlus",t.MOON="moon",t.NON_ISOSCELES_TRAPEZOID="nonIsoscelesTrapezoid",t.NOTCHED_RIGHT_ARROW="notchedRightArrow",t.NO_SYMBOL="noSmoking",t.OCTAGON="octagon",t.OVAL="ellipse",t.OVAL_CALLOUT="wedgeEllipseCallout",t.PARALLELOGRAM="parallelogram",t.PENTAGON="homePlate",t.PIE="pie",t.PIE_WEDGE="pieWedge",t.PLAQUE="plaque",t.PLAQUE_TABS="plaqueTabs",t.QUAD_ARROW="quadArrow",t.QUAD_ARROW_CALLOUT="quadArrowCallout",t.RECTANGLE="rect",t.RECTANGULAR_CALLOUT="wedgeRectCallout",t.REGULAR_PENTAGON="pentagon",t.RIGHT_ARROW="rightArrow",t.RIGHT_ARROW_CALLOUT="rightArrowCallout",t.RIGHT_BRACE="rightBrace",t.RIGHT_BRACKET="rightBracket",t.RIGHT_TRIANGLE="rtTriangle",t.ROUNDED_RECTANGLE="roundRect",t.ROUNDED_RECTANGULAR_CALLOUT="wedgeRoundRectCallout",t.ROUND_1_RECTANGLE="round1Rect",t.ROUND_2_DIAG_RECTANGLE="round2DiagRect",t.ROUND_2_SAME_RECTANGLE="round2SameRect",t.SMILEY_FACE="smileyFace",t.SNIP_1_RECTANGLE="snip1Rect",t.SNIP_2_DIAG_RECTANGLE="snip2DiagRect",t.SNIP_2_SAME_RECTANGLE="snip2SameRect",t.SNIP_ROUND_RECTANGLE="snipRoundRect",t.SQUARE_TABS="squareTabs",t.STAR_10_POINT="star10",t.STAR_12_POINT="star12",t.STAR_16_POINT="star16",t.STAR_24_POINT="star24",t.STAR_32_POINT="star32",t.STAR_4_POINT="star4",t.STAR_5_POINT="star5",t.STAR_6_POINT="star6",t.STAR_7_POINT="star7",t.STAR_8_POINT="star8",t.STRIPED_RIGHT_ARROW="stripedRightArrow",t.SUN="sun",t.SWOOSH_ARROW="swooshArrow",t.TEAR="teardrop",t.TRAPEZOID="trapezoid",t.UP_ARROW="upArrow",t.UP_ARROW_CALLOUT="upArrowCallout",t.UP_DOWN_ARROW="upDownArrow",t.UP_DOWN_ARROW_CALLOUT="upDownArrowCallout",t.UP_RIBBON="ribbon2",t.U_TURN_ARROW="uturnArrow",t.VERTICAL_SCROLL="verticalScroll",t.WAVE="wave",(t=b=b||{}).AREA="area",t.BAR="bar",t.BAR3D="bar3D",t.BUBBLE="bubble",t.BUBBLE3D="bubble3D",t.DOUGHNUT="doughnut",t.LINE="line",t.PIE="pie",t.RADAR="radar",t.SCATTER="scatter",(t=W=W||{}).TEXT1="tx1",t.TEXT2="tx2",t.BACKGROUND1="bg1",t.BACKGROUND2="bg2",t.ACCENT1="accent1",t.ACCENT2="accent2",t.ACCENT3="accent3",t.ACCENT4="accent4",t.ACCENT5="accent5",t.ACCENT6="accent6",(t=o=o||{}).chart="chart",t.image="image",t.line="line",t.rect="rect",t.text="text",t.placeholder="placeholder",(t=_=_||{}).chart="chart",t.hyperlink="hyperlink",t.image="image",t.media="media",t.online="online",t.placeholder="placeholder",t.table="table",t.tablecell="tablecell",t.text="text",t.notes="notes",(t=a=a||{}).title="title",t.body="body",t.image="pic",t.chart="chart",t.table="tbl",t.media="media",(t=f=f||{}).DEFAULT="&#x2022;",t.CHECK="&#x2713;",t.STAR="&#x2605;",t.TRIANGLE="&#x25B6;","data:image/png;base64,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");function N(t,e,n){return"number"==typeof(t="string"!=typeof t||isNaN(Number(t))?t:Number(t))&&t<100?I(t):"number"==typeof t&&100<=t?t:"string"==typeof t&&t.includes("%")?(!e||"X"!==e)&&e&&"Y"===e?Math.round(parseFloat(t)/100*n.height):Math.round(parseFloat(t)/100*n.width):0}function ht(t){return t.replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})}function F(t){return void 0===t||null==t?"":t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}function I(t){return"number"==typeof t&&100<t?t:("string"==typeof t&&(t=Number(t.replace(/in*/gi,""))),Math.round(k*t))}function R(t){t=Number(t)||0;return isNaN(t)?0:Math.round(t*w)}function O(t){return t=t||0,Math.round(6e4*(360<t?t-360:t))}function gt(t){t=t.toString(16);return 1===t.length?"0"+t:t}function mt(t,e,n){return(gt(t)+gt(e)+gt(n)).toUpperCase()}function M(t,e){var t=(t||"").replace("#",""),n=(X.test(t)||t===r.background1||t===r.background2||t===r.text1||t===r.text2||t===r.accent1||t===r.accent2||t===r.accent3||t===r.accent4||t===r.accent5||t===r.accent6||(console.warn('"'.concat(t,'" is not a valid scheme color or hex RGB! "').concat(C,"\" used instead. Only provide 6-digit RGB or 'pptx.SchemeColor' values!")),t=C),X.test(t)?"srgbClr":"schemeClr"),t='val="'+(X.test(t)?t.toUpperCase():t)+'"';return e?"<a:".concat(n," ").concat(t,">").concat(e,"</a:").concat(n,">"):"<a:".concat(n," ").concat(t,"/>")}function z(t){var e="solid",n="",r="",a="";return t&&("string"==typeof t?n=t:(t.type&&(e=t.type),t.color&&(n=t.color),t.alpha&&(r+='<a:alpha val="'.concat(Math.round(1e3*(100-t.alpha)),'"/>')),t.transparency&&(r+='<a:alpha val="'.concat(Math.round(1e3*(100-t.transparency)),'"/>'))),a+="solid"===e?"<a:solidFill>".concat(M(n,r),"</a:solidFill>"):""),a}function g(t){return t._rels.length+t._relsChart.length+t._relsMedia.length+1}function vt(t){if(t&&"object"==typeof t)return"outer"!==t.type&&"inner"!==t.type&&"none"!==t.type&&(console.warn("Warning: shadow.type options are `outer`, `inner` or `none`."),t.type="outer"),t.angle&&((isNaN(Number(t.angle))||t.angle<0||359<t.angle)&&(console.warn("Warning: shadow.angle can only be 0-359"),t.angle=270),t.angle=Math.round(Number(t.angle))),t.opacity&&((isNaN(Number(t.opacity))||t.opacity<0||1<t.opacity)&&(console.warn("Warning: shadow.opacity can only be 0-1"),t.opacity=.75),t.opacity=Number(t.opacity)),t.color&&t.color.startsWith("#")&&(console.warn('Warning: shadow.color should not include hash (#) character, , e.g. "FF0000"'),t.color=t.color.replace("#","")),t}function yt(t,f,e,n){void 0===t&&(t=[]);var r,a=at,c=+k,u=0,o=0,d=[],i=N((f=void 0===f?{}:f).x,"X",e),s=N(f.y,"Y",e),A=N(f.w,"X",e),l=N(f.h,"Y",e),p=A;function h(){var t=0;0===d.length&&(t=s||I(a[0])),0<d.length&&(t=I(f.autoPageSlideStartY||f.newSlideStartY||a[0])),c=(l||e.height)-t-I(a[2]),1<d.length&&("number"==typeof f.autoPageSlideStartY?c=(l||e.height)-I(f.autoPageSlideStartY+a[2]):"number"==typeof f.newSlideStartY?c=(l||e.height)-I(f.newSlideStartY+a[2]):s&&(c=(l||e.height)-I((s/k<a[0]?s/k:a[0])+a[2]))<l&&(c=l))}if(f.verbose&&(console.log("[[VERBOSE MODE]]"),console.log("|-- TABLE PROPS --------------------------------------------------------|"),console.log("| presLayout.width ................................ = ".concat((e.width/k).toFixed(1))),console.log("| presLayout.height ............................... = ".concat((e.height/k).toFixed(1))),console.log("| tableProps.x .................................... = ".concat("number"==typeof f.x?(f.x/k).toFixed(1):f.x)),console.log("| tableProps.y .................................... = ".concat("number"==typeof f.y?(f.y/k).toFixed(1):f.y)),console.log("| tableProps.w .................................... = ".concat("number"==typeof f.w?(f.w/k).toFixed(1):f.w)),console.log("| tableProps.h .................................... = ".concat("number"==typeof f.h?(f.h/k).toFixed(1):f.h)),console.log("| tableProps.slideMargin .......................... = ".concat(f.slideMargin?String(f.slideMargin):"")),console.log("| tableProps.margin ............................... = ".concat(String(f.margin))),console.log("| tableProps.colW ................................. = ".concat(String(f.colW))),console.log("| tableProps.autoPageSlideStartY .................. = ".concat(f.autoPageSlideStartY)),console.log("| tableProps.autoPageCharWeight ................... = ".concat(f.autoPageCharWeight)),console.log("|-- CALCULATIONS -------------------------------------------------------|"),console.log("| tablePropX ...................................... = ".concat(i/k)),console.log("| tablePropY ...................................... = ".concat(s/k)),console.log("| tablePropW ...................................... = ".concat(A/k)),console.log("| tablePropH ...................................... = ".concat(l/k)),console.log("| tableCalcW ...................................... = ".concat(p/k))),f.slideMargin||0===f.slideMargin||(f.slideMargin=at[0]),n&&void 0!==n._margin?Array.isArray(n._margin)?a=n._margin:isNaN(Number(n._margin))||(a=[Number(n._margin),Number(n._margin),Number(n._margin),Number(n._margin)]):!f.slideMargin&&0!==f.slideMargin||(Array.isArray(f.slideMargin)?a=f.slideMargin:isNaN(f.slideMargin)||(a=[f.slideMargin,f.slideMargin,f.slideMargin,f.slideMargin])),f.verbose&&console.log("| arrInchMargins .................................. = [".concat(a.join(", "),"]")),(t[0]||[]).forEach(function(t){t=(t=t||{_type:_.tablecell}).options||null;o+=Number(null!==t&&t.colspan?t.colspan:1)}),f.verbose&&console.log("| numCols ......................................... = ".concat(o)),!A&&f.colW&&(p=Array.isArray(f.colW)?f.colW.reduce(function(t,e){return t+e})*k:f.colW*o||0,f.verbose)&&console.log("| tableCalcW ...................................... = ".concat(p/k)),r=p||I((i?i/k:a[1])+a[3]),f.verbose&&console.log("| emuSlideTabW .................................... = ".concat((r/k).toFixed(1))),!f.colW||!Array.isArray(f.colW))if(f.colW&&!isNaN(Number(f.colW))){var g=[];(t[0]||[]).forEach(function(){return g.push(f.colW)}),f.colW=[],g.forEach(function(t){Array.isArray(f.colW)&&f.colW.push(t)})}else{f.colW=[];for(var m=0;m<o;m++)f.colW.push(r/k/o)}var v={rows:[]};return t.forEach(function(t,e){for(var p=[],n=0,r=0,a=[],o=(t.forEach(function(t){var e;a.push({_type:_.tablecell,text:[],options:t.options}),t.options.margin&&1<=t.options.margin[0]?(null!=(e=t.options)&&e.margin&&t.options.margin[0]&&R(t.options.margin[0])>n?n=R(t.options.margin[0]):null!=f&&f.margin&&f.margin[0]&&R(f.margin[0])>n&&(n=R(f.margin[0])),null!=(e=t.options)&&e.margin&&t.options.margin[2]&&R(t.options.margin[2])>r?r=R(t.options.margin[2]):null!=f&&f.margin&&f.margin[2]&&R(f.margin[2])>r&&(r=R(f.margin[2]))):(null!=(e=t.options)&&e.margin&&t.options.margin[0]&&I(t.options.margin[0])>n?n=I(t.options.margin[0]):null!=f&&f.margin&&f.margin[0]&&I(f.margin[0])>n&&(n=I(f.margin[0])),null!=(e=t.options)&&e.margin&&t.options.margin[2]&&I(t.options.margin[2])>r?r=I(t.options.margin[2]):null!=f&&f.margin&&f.margin[2]&&I(f.margin[2])>r&&(r=I(f.margin[2])))}),h(),u+=n+r,f.verbose&&0===e&&console.log("| SLIDE [".concat(d.length,"]: emuSlideTabH ...... = ").concat((c/k).toFixed(1)," ")),t.forEach(function(n,r){var t,a,e,o,i,s,A,l,c={_type:_.tablecell,_lines:null,_lineHeight:I((null!=(c=n.options)&&c.fontSize?n.options.fontSize:f.fontSize||P)*(H+(f.autoPageLineWeight||0))/100),text:[],options:n.options},u=(c.options.rowspan&&(c._lineHeight=0),c.options.autoPageCharWeight=f.autoPageCharWeight||null,f.colW[r]);n.options.colspan&&Array.isArray(f.colW)&&(u=f.colW.filter(function(t,e){return r<=e&&e<e+n.options.colspan}).reduce(function(t,e){return t+e})),c._lines=(u=u,a=!1,e=2.3+(null!=(e=(t=n).options)&&e.autoPageCharWeight?t.options.autoPageCharWeight:0),o=Math.floor(u/w*k)/((null!=(u=t.options)&&u.fontSize?t.options.fontSize:P)/e),i=[],u=[],s=[],A=[],t.text&&0===t.text.toString().trim().length?u.push({_type:_.tablecell,text:" "}):"number"==typeof t.text||"string"==typeof t.text?u.push({_type:_.tablecell,text:(t.text||"").toString().trim()}):Array.isArray(t.text)&&(u=t.text),a&&(console.log("[1/4] inputCells"),u.forEach(function(t,e){return console.log("[1/4] [".concat(e+1,"] cell: ").concat(JSON.stringify(t)))})),l=[],u.forEach(function(e){var t;"string"==typeof e.text&&(1<e.text.split("\n").length?e.text.split("\n").forEach(function(t){l.push({_type:_.tablecell,text:t,options:y(y({},e.options),{breakLine:!0})})}):l.push({_type:_.tablecell,text:e.text.trim(),options:e.options}),null!=(t=e.options))&&t.breakLine&&(a&&console.log("inputCells: new line > ".concat(JSON.stringify(l))),s.push(l),l=[]),0<l.length&&(s.push(l),l=[])}),a&&(console.log("[2/4] inputLines1 (".concat(s.length,")")),s.forEach(function(t,e){return console.log("[2/4] [".concat(e+1,"] line: ").concat(JSON.stringify(t)))})),s.forEach(function(t){t.forEach(function(r){var a=[],o=String(r.text).split(" ");o.forEach(function(t,e){var n=y({},r.options);null!=n&&n.breakLine&&(n.breakLine=e+1===o.length),a.push({_type:_.tablecell,text:t+(e+1<o.length?" ":""),options:n})}),A.push(a)})}),a&&(console.log("[3/4] inputLines2 (".concat(A.length,")")),A.forEach(function(t){return console.log("[3/4] line: ".concat(JSON.stringify(t)))})),A.forEach(function(t){var e=[],n="";t.forEach(function(t){n.length+t.text.length>o&&(i.push(e),e=[],n=""),e.push(t),n+=t.text.toString()}),0<e.length&&i.push(e)}),a&&(console.log("[4/4] parsedLines (".concat(i.length,")")),i.forEach(function(t,e){return console.log("[4/4] [Line ".concat(e+1,"]:\n").concat(JSON.stringify(t)))}),console.log("...............................................\n\n")),i),p.push(c)}),f.verbose&&console.log("\n| SLIDE [".concat(d.length,"]: ROW [").concat(e,"]: START...")),0),i=0,s=!1;!s;){var A=p[o],l=a[o],A=(p.forEach(function(t){t._lineHeight>=i&&(i=t._lineHeight)}),c<u+i&&(f.verbose&&(console.log("\n|-----------------------------------------------------------------------|"),console.log("|-- NEW SLIDE CREATED (currTabH+currLineH > maxH) => ".concat((u/k).toFixed(2)," + ").concat((A._lineHeight/k).toFixed(2)," > ").concat(c/k)),console.log("|-----------------------------------------------------------------------|\n\n")),0<a.length&&0<a.map(function(t){return t.text.length}).reduce(function(t,e){return t+e})&&v.rows.push(a),d.push(v),v={rows:[]},a=[],t.forEach(function(t){return a.push({_type:_.tablecell,text:[],options:t.options})}),h(),u+=n+r,f.verbose&&console.log("| SLIDE [".concat(d.length,"]: emuSlideTabH ...... = ").concat((c/k).toFixed(1)," ")),u=0,(f.addHeaderToEach||f.autoPageRepeatHeader)&&f._arrObjTabHeadRows&&f._arrObjTabHeadRows.forEach(function(t){var e=[],n=0;t.forEach(function(t){e.push(t),t._lineHeight>n&&(n=t._lineHeight)}),v.rows.push(e),u+=n}),l=a[o]),A._lines.shift());Array.isArray(l.text)&&(A?l.text=l.text.concat(A):0===l.text.length&&(l.text=l.text.concat({_type:_.tablecell,text:""}))),o===p.length-1&&(u+=i),o=o<p.length-1?o+1:0,0===p.map(function(t){return t._lines.length}).reduce(function(t,e){return t+e})&&(s=!0)}0<a.length&&v.rows.push(a),f.verbose&&console.log("- SLIDE [".concat(d.length,"]: ROW [").concat(e,"]: ...COMPLETE ...... emuTabCurrH = ").concat((u/k).toFixed(2)," ( emuSlideTabH = ").concat((c/k).toFixed(2)," )"))}),d.push(v),f.verbose&&(console.log("\n|================================================|"),console.log("| FINAL: tableRowSlides.length = ".concat(d.length)),d.forEach(function(t){return console.log(t)}),console.log("|================================================|\n\n")),d}var bt=0;function wt(t,e,n,r){function a(t){t&&"none"!==t.style&&(void 0!==t.size&&(isNaN(Number(t.size))||t.size<=0)&&(console.warn("Warning: chart.gridLine.size must be greater than 0."),delete t.size),t.style&&!["solid","dash","dot"].includes(t.style)&&(console.warn("Warning: chart.gridLine.style options: `solid`, `dash`, `dot`."),delete t.style),t.cap)&&!["flat","square","round"].includes(t.cap)&&(console.warn("Warning: chart.gridLine.cap options: `flat`, `square`, `round`."),delete t.cap)}var o=++bt,i={_type:null,text:null,options:null,chartRid:null},s=null,A=[],s=Array.isArray(e)?(e.forEach(function(t){A=A.concat(t.data)}),n||r):(A=n,r),l=(A.forEach(function(t,e){t._dataIndex=e,void 0===t.labels||Array.isArray(t.labels[0])||(t.labels=[t.labels])}),s&&"object"==typeof s?s:{});l._type=e,l.x=void 0===l.x||null==l.x||isNaN(Number(l.x))?1:l.x,l.y=void 0===l.y||null==l.y||isNaN(Number(l.y))?1:l.y,l.w=l.w||"50%",l.h=l.h||"50%",l.objectName=l.objectName?F(l.objectName):"Chart ".concat(t._slideObjects.filter(function(t){return t._type===_.chart}).length),["bar","col"].includes(l.barDir||"")||(l.barDir="col"),l._type!==b.AREA||["stacked","standard","percentStacked"].includes(l.barGrouping||"")||(l.barGrouping="standard"),l._type!==b.BAR||["clustered","stacked","percentStacked"].includes(l.barGrouping||"")||(l.barGrouping="clustered"),l._type!==b.BAR3D||["clustered","stacked","standard","percentStacked"].includes(l.barGrouping||"")||(l.barGrouping="standard"),null!=(n=l.barGrouping)&&n.includes("tacked")&&!l.barGapWidthPct&&(l.barGapWidthPct=50),l.dataLabelPosition&&(l._type!==b.AREA&&l._type!==b.BAR3D&&l._type!==b.DOUGHNUT&&l._type!==b.RADAR||delete l.dataLabelPosition,l._type!==b.PIE||["bestFit","ctr","inEnd","outEnd"].includes(l.dataLabelPosition)||delete l.dataLabelPosition,l._type!==b.BUBBLE&&l._type!==b.BUBBLE3D&&l._type!==b.LINE&&l._type!==b.SCATTER||["b","ctr","l","r","t"].includes(l.dataLabelPosition)||delete l.dataLabelPosition,l._type===b.BAR)&&(["stacked","percentStacked"].includes(l.barGrouping||"")||["ctr","inBase","inEnd"].includes(l.dataLabelPosition)||delete l.dataLabelPosition,["clustered"].includes(l.barGrouping||"")||["ctr","inBase","inEnd","outEnd"].includes(l.dataLabelPosition)||delete l.dataLabelPosition),l.dataLabelBkgrdColors=!(!l.dataLabelBkgrdColors&&l.dataLabelBkgrdColors)&&l.dataLabelBkgrdColors,["b","l","r","t","tr"].includes(l.legendPos||"")||(l.legendPos="r"),["cone","coneToMax","box","cylinder","pyramid","pyramidToMax"].includes(l.bar3DShape||"")||(l.bar3DShape="box"),["circle","dash","diamond","dot","none","square","triangle"].includes(l.lineDataSymbol||"")||(l.lineDataSymbol="circle"),["gap","span"].includes(l.displayBlanksAs||"")||(l.displayBlanksAs="span"),["standard","marker","filled"].includes(l.radarStyle||"")||(l.radarStyle="standard"),l.lineDataSymbolSize=l.lineDataSymbolSize&&!isNaN(l.lineDataSymbolSize)?l.lineDataSymbolSize:6,l.lineDataSymbolLineSize=l.lineDataSymbolLineSize&&!isNaN(l.lineDataSymbolLineSize)?R(l.lineDataSymbolLineSize):R(.75),l.layout&&["x","y","w","h"].forEach(function(t){var e=l.layout[t];(isNaN(Number(e))||e<0||1<e)&&(console.warn("Warning: chart.layout."+t+" can only be 0-1"),delete l.layout[t])}),l.catGridLine=l.catGridLine||(l._type===b.SCATTER?{color:"D9D9D9",size:1}:{style:"none"}),l.valGridLine=l.valGridLine||(l._type===b.SCATTER?{color:"D9D9D9",size:1}:{}),l.serGridLine=l.serGridLine||(l._type===b.SCATTER?{color:"D9D9D9",size:1}:{style:"none"}),a(l.catGridLine),a(l.valGridLine),a(l.serGridLine),vt(l.shadow),l.showDataTable=!(!l.showDataTable&&l.showDataTable)&&l.showDataTable,l.showDataTableHorzBorder=!(l.showDataTableHorzBorder||!l.showDataTableHorzBorder)||l.showDataTableHorzBorder,l.showDataTableVertBorder=!(l.showDataTableVertBorder||!l.showDataTableVertBorder)||l.showDataTableVertBorder,l.showDataTableOutline=!(l.showDataTableOutline||!l.showDataTableOutline)||l.showDataTableOutline,l.showDataTableKeys=!(l.showDataTableKeys||!l.showDataTableKeys)||l.showDataTableKeys,l.showLabel=!(!l.showLabel&&l.showLabel)&&l.showLabel,l.showLegend=!(!l.showLegend&&l.showLegend)&&l.showLegend,l.showPercent=!(l.showPercent||!l.showPercent)||l.showPercent,l.showTitle=!(!l.showTitle&&l.showTitle)&&l.showTitle,l.showValue=!(!l.showValue&&l.showValue)&&l.showValue,l.showLeaderLines=!(!l.showLeaderLines&&l.showLeaderLines)&&l.showLeaderLines,l.catAxisLineShow=void 0===l.catAxisLineShow||l.catAxisLineShow,l.valAxisLineShow=void 0===l.valAxisLineShow||l.valAxisLineShow,l.serAxisLineShow=void 0===l.serAxisLineShow||l.serAxisLineShow,l.v3DRotX=!isNaN(l.v3DRotX)&&-90<=l.v3DRotX&&l.v3DRotX<=90?l.v3DRotX:30,l.v3DRotY=!isNaN(l.v3DRotY)&&0<=l.v3DRotY&&l.v3DRotY<=360?l.v3DRotY:30,l.v3DRAngAx=!(l.v3DRAngAx||!l.v3DRAngAx)||l.v3DRAngAx,l.v3DPerspective=!isNaN(l.v3DPerspective)&&0<=l.v3DPerspective&&l.v3DPerspective<=240?l.v3DPerspective:30,l.barGapWidthPct=!isNaN(l.barGapWidthPct)&&0<=l.barGapWidthPct&&l.barGapWidthPct<=1e3?l.barGapWidthPct:150,l.barGapDepthPct=!isNaN(l.barGapDepthPct)&&0<=l.barGapDepthPct&&l.barGapDepthPct<=1e3?l.barGapDepthPct:150,l.chartColors=Array.isArray(l.chartColors)?l.chartColors:l._type===b.PIE||l._type===b.DOUGHNUT?ft:pt,l.chartColorsOpacity=l.chartColorsOpacity&&!isNaN(l.chartColorsOpacity)?l.chartColorsOpacity:null,l.border=l.border&&"object"==typeof l.border?l.border:null,!l.border||l.border.pt&&!isNaN(l.border.pt)||(l.border.pt=tt),!l.border||l.border.color&&"string"==typeof l.border.color||(l.border.color=$),l.plotArea=l.plotArea||{},l.plotArea.border=l.plotArea.border&&"object"==typeof l.plotArea.border?l.plotArea.border:null,!l.plotArea.border||l.plotArea.border.pt&&!isNaN(l.plotArea.border.pt)||(l.plotArea.border.pt=tt),!l.plotArea.border||l.plotArea.border.color&&"string"==typeof l.plotArea.border.color||(l.plotArea.border.color=$),l.border&&(l.plotArea.border=l.border),l.plotArea.fill=l.plotArea.fill||{color:null,transparency:null},l.fill&&(l.plotArea.fill.color=l.fill),l.chartArea=l.chartArea||{},l.chartArea.border=l.chartArea.border&&"object"==typeof l.chartArea.border?l.chartArea.border:null,l.chartArea.border&&(l.chartArea.border={color:l.chartArea.border.color||$,pt:l.chartArea.border.pt||tt}),l.chartArea.roundedCorners="boolean"!=typeof l.chartArea.roundedCorners||l.chartArea.roundedCorners,l.dataBorder=l.dataBorder&&"object"==typeof l.dataBorder?l.dataBorder:null,!l.dataBorder||l.dataBorder.pt&&!isNaN(l.dataBorder.pt)||(l.dataBorder.pt=.75),!l.dataBorder||l.dataBorder.color&&"string"==typeof l.dataBorder.color&&6===l.dataBorder.color.length||(l.dataBorder.color="F9F9F9"),l.dataLabelFormatCode||l._type!==b.SCATTER||(l.dataLabelFormatCode="General"),l.dataLabelFormatCode||l._type!==b.PIE&&l._type!==b.DOUGHNUT||(l.dataLabelFormatCode=l.showPercent?"0%":"General"),l.dataLabelFormatCode=l.dataLabelFormatCode&&"string"==typeof l.dataLabelFormatCode?l.dataLabelFormatCode:"#,##0",l.dataLabelFormatScatter||l._type!==b.SCATTER||(l.dataLabelFormatScatter="custom"),l.lineSize="number"==typeof l.lineSize?l.lineSize:2,l.valAxisMajorUnit="number"==typeof l.valAxisMajorUnit?l.valAxisMajorUnit:null,l._type===b.AREA||l._type===b.BAR||l._type===b.BAR3D||l._type===b.LINE?l.catAxisMultiLevelLabels=!!l.catAxisMultiLevelLabels:delete l.catAxisMultiLevelLabels,i._type="chart",i.options=l,i.chartRid=g(t),t._relsChart.push({rId:g(t),data:A,opts:l,type:l._type,globalId:o,fileName:"chart".concat(o,".xml"),Target:"/ppt/charts/chart".concat(o,".xml")}),t._slideObjects.push(i)}function xt(t,e){var n={_type:null,text:null,options:null,image:null,imageRid:null,hyperlink:null},r=e.x||0,a=e.y||0,o=e.w||0,i=e.h||0,s=e.sizing||null,A=e.hyperlink||"",l=e.data||"",c=e.path||"",u=g(t),p=e.objectName?F(e.objectName):"Image ".concat(t._slideObjects.filter(function(t){return t._type===_.image}).length);if(c||l)if(c&&"string"!=typeof c)console.error("ERROR: addImage() 'path' should be a string, ex: {path:'/img/sample.png'} - you sent ".concat(String(c)));else if(l&&"string"!=typeof l)console.error("ERROR: addImage() 'data' should be a string, ex: {data:'image/png;base64,NMP[...]'} - you sent ".concat(String(l)));else if(l&&"string"==typeof l&&!l.toLowerCase().includes("base64,"))console.error("ERROR: Image `data` value lacks a base64 header! Ex: 'image/png;base64,NMP[...]')");else{var f=(c.substring(c.lastIndexOf("/")+1).split("?")[0].split(".").pop().split("#")[0]||"png").toLowerCase();if(l&&/image\/(\w+);/.exec(l)&&0</image\/(\w+);/.exec(l).length?f=/image\/(\w+);/.exec(l)[1]:null!=l&&l.toLowerCase().includes("image/svg+xml")&&(f="svg"),n._type=_.image,n.image=c||"preencoded.png",n.options={x:r||0,y:a||0,w:o||1,h:i||1,altText:e.altText||"",rounding:"boolean"==typeof e.rounding&&e.rounding,sizing:s,placeholder:e.placeholder,rotate:e.rotate||0,flipV:e.flipV||!1,flipH:e.flipH||!1,transparency:e.transparency||0,objectName:p,shadow:vt(e.shadow)},"svg"===f?(t._relsMedia.push({path:c||l+"png",type:"image/png",extn:"png",data:l||"",rId:u,Target:"../media/image-".concat(t._slideNum,"-").concat(t._relsMedia.length+1,".png"),isSvgPng:!0,svgSize:{w:N(n.options.w,"X",t._presLayout),h:N(n.options.h,"Y",t._presLayout)}}),t._relsMedia.push({path:c||l,type:"image/svg+xml",extn:f,data:l||"",rId:(n.imageRid=u)+1,Target:"../media/image-".concat(t._slideNum,"-").concat(t._relsMedia.length+1,".").concat(f)}),n.imageRid=u+1):(r=t._relsMedia.filter(function(t){return t.path&&t.path===c&&t.type==="image/"+f&&!t.isDuplicate})[0],t._relsMedia.push({path:c||"preencoded."+f,type:"image/"+f,extn:f,data:l||"",rId:u,isDuplicate:!(null==r||!r.Target),Target:null!=r&&r.Target?r.Target:"../media/image-".concat(t._slideNum,"-").concat(t._relsMedia.length+1,".").concat(f)}),n.imageRid=u),"object"==typeof A){if(!A.url&&!A.slide)throw new Error("ERROR: `hyperlink` option requires either: `url` or `slide`");t._rels.push({type:_.hyperlink,data:A.slide?"slide":"dummy",rId:++u,Target:A.url||A.slide.toString()}),A._rId=u,n.hyperlink=A}t._slideObjects.push(n)}else console.error("ERROR: addImage() requires either 'data' or 'path' parameter!")}function Ct(t,e,n){var n="object"==typeof n?n:{},r=(n.line=n.line||{type:"none"},{_type:_.text,shape:e||A.RECTANGLE,options:n,text:null});if(!e)throw new Error("Missing/Invalid shape parameter! Example: `addShape(pptxgen.shapes.LINE, {x:1, y:1, w:1, h:1});`");var e={type:n.line.type||"solid",color:n.line.color||rt,transparency:n.line.transparency||0,width:n.line.width||1,dashType:n.line.dashType||"solid",beginArrowType:n.line.beginArrowType||null,endArrowType:n.line.endArrowType||null};"object"==typeof n.line&&"none"!==n.line.type&&(n.line=e),n.x=n.x||(0===n.x?0:1),n.y=n.y||(0===n.y?0:1),n.w=n.w||(0===n.w?0:1),n.h=n.h||(0===n.h?0:1),n.objectName=n.objectName?F(n.objectName):"Shape ".concat(t._slideObjects.filter(function(t){return t._type===_.text}).length),"string"==typeof n.line&&((e=e).color=String(n.line),n.line=e),"number"==typeof n.lineSize&&(n.line.width=n.lineSize),"string"==typeof n.lineDash&&(n.line.dashType=n.lineDash),"string"==typeof n.lineHead&&(n.line.beginArrowType=n.lineHead),"string"==typeof n.lineTail&&(n.line.endArrowType=n.lineTail),m(t,r),t._slideObjects.push(r)}function Pt(n,t,e,r){var a={_type:r?_.placeholder:_.text,shape:(null==e?void 0:e.shape)||A.RECTANGLE,text:t&&0!==t.length?t:[{text:"",options:null}],options:e||{}};function o(e){var t;return e.placeholder||(e.color=e.color||a.options.color||n.color||C),(e.placeholder||r)&&(e.bullet=e.bullet||!1),(e=e.placeholder&&n._slideLayout&&n._slideLayout._slideObjects&&null!=(t=n._slideLayout._slideObjects.filter(function(t){return"placeholder"===t._type&&t.options&&t.options.placeholder&&t.options.placeholder===e.placeholder})[0])&&t.options?y(y({},e),t.options):e).objectName=e.objectName?F(e.objectName):"Text ".concat(n._slideObjects.filter(function(t){return t._type===_.text}).length),e.shape===A.LINE&&(t={type:e.line.type||"solid",color:e.line.color||rt,transparency:e.line.transparency||0,width:e.line.width||1,dashType:e.line.dashType||"solid",beginArrowType:e.line.beginArrowType||null,endArrowType:e.line.endArrowType||null},"object"==typeof e.line&&(e.line=t),"string"==typeof e.line&&(t=t,"string"==typeof e.line&&(t.color=e.line),e.line=t),"number"==typeof e.lineSize&&(e.line.width=e.lineSize),"string"==typeof e.lineDash&&(e.line.dashType=e.lineDash),"string"==typeof e.lineHead&&(e.line.beginArrowType=e.lineHead),"string"==typeof e.lineTail)&&(e.line.endArrowType=e.lineTail),e.line=e.line||{},e.lineSpacing=e.lineSpacing&&!isNaN(e.lineSpacing)?e.lineSpacing:null,e.lineSpacingMultiple=e.lineSpacingMultiple&&!isNaN(e.lineSpacingMultiple)?e.lineSpacingMultiple:null,e._bodyProp=e._bodyProp||{},e._bodyProp.autoFit=e.autoFit||!1,e._bodyProp.anchor=e.placeholder?null:s.ctr,e._bodyProp.vert=e.vert||null,e._bodyProp.wrap="boolean"!=typeof e.wrap||e.wrap,(e.inset&&!isNaN(Number(e.inset))||0===e.inset)&&(e._bodyProp.lIns=I(e.inset),e._bodyProp.rIns=I(e.inset),e._bodyProp.tIns=I(e.inset),e._bodyProp.bIns=I(e.inset)),"boolean"==typeof e.underline&&!0===e.underline&&(e.underline={style:"sng"}),0===(e.align||"").toLowerCase().indexOf("c")?e._bodyProp.align=i.center:0===(e.align||"").toLowerCase().indexOf("l")?e._bodyProp.align=i.left:0===(e.align||"").toLowerCase().indexOf("r")?e._bodyProp.align=i.right:0===(e.align||"").toLowerCase().indexOf("j")&&(e._bodyProp.align=i.justify),0===(e.valign||"").toLowerCase().indexOf("b")?e._bodyProp.anchor=s.b:0===(e.valign||"").toLowerCase().indexOf("m")?e._bodyProp.anchor=s.ctr:0===(e.valign||"").toLowerCase().indexOf("t")&&(e._bodyProp.anchor=s.t),vt(e.shadow),e}a.options=o(a.options),a.text.forEach(function(t){return t.options=o(t.options||{})}),m(n,a.text||""),n._slideObjects.push(a)}function St(t,e){var n,r;e.bkgd&&(e.background||(e.background={}),"string"==typeof e.bkgd?e.background.color=e.bkgd:(e.bkgd.data&&(e.background.data=e.bkgd.data),e.bkgd.path&&(e.background.path=e.bkgd.path),e.bkgd.src&&(e.background.path=e.bkgd.src))),null!=(n=e.background)&&n.fill&&(e.background.color=e.background.fill),t&&(t.path||t.data)&&(t.path=t.path||"preencoded.png","jpg"===(n=(t.path.split(".").pop()||"png").split("?")[0])&&(n="jpeg"),e._relsMedia=e._relsMedia||[],r=e._relsMedia.length+1,e._relsMedia.push({path:t.path,type:_.image,extn:n,data:t.data||null,rId:r,Target:"../media/".concat((e._name||"").replace(/\s+/gi,"-"),"-image-").concat(e._relsMedia.length+1,".").concat(n)}),e._bkgdImgRid=r)}function m(n,t){var e=[];"string"!=typeof t&&"number"!=typeof t&&(Array.isArray(t)?e=t:"object"==typeof t&&(e=[t]),e.forEach(function(t){var e;Array.isArray(t)?m(n,t):Array.isArray(t.text)?m(n,t.text):t&&"object"==typeof t&&t.options&&t.options.hyperlink&&!t.options.hyperlink._rId&&("object"!=typeof t.options.hyperlink?console.log("ERROR: text `hyperlink` option should be an object. Ex: `hyperlink: {url:'https://github.com'}` "):t.options.hyperlink.url||t.options.hyperlink.slide?(e=g(n),n._rels.push({type:_.hyperlink,data:t.options.hyperlink.slide?"slide":"dummy",rId:e,Target:F(t.options.hyperlink.url)||t.options.hyperlink.slide.toString()}),t.options.hyperlink._rId=e):console.log("ERROR: 'hyperlink requires either: `url` or `slide`'"))}))}Object.defineProperty(e.prototype,"bkgd",{get:function(){return this._bkgd},set:function(t){this._bkgd=t,this._background&&this._background.color||(this._background||(this._background={}),"string"==typeof t&&(this._background.color=t))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){(this._background=t)&&St(t,this)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"color",{get:function(){return this._color},set:function(t){this._color=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hidden",{get:function(){return this._hidden},set:function(t){this._hidden=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"slideNumber",{get:function(){return this._slideNumberProps},set:function(t){this._slideNumberProps=t,this._setSlideNum(t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"newAutoPagedSlides",{get:function(){return this._newAutoPagedSlides},enumerable:!1,configurable:!0}),e.prototype.addChart=function(t,e,n){return wt(this,(n||{})._type=t,e,n),this},e.prototype.addImage=function(t){return xt(this,t),this},e.prototype.addMedia=function(t){var e,n,r=this,a=t.x||0,o=t.y||0,i=t.w||2,s=t.h||2,A=t.data||"",l=t.link||"",c=t.path||"",u=t.type||"audio",p=t.cover||"data:image/png;base64,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",f=t.objectName?F(t.objectName):"Media ".concat(r._slideObjects.filter(function(t){return t._type===_.media}).length),d={_type:_.media};if(!c&&!A&&"online"!==u)throw new Error("addMedia() error: either `data` or `path` are required!");if(A&&!A.toLowerCase().includes("base64,"))throw new Error("addMedia() error: `data` value lacks a base64 header! Ex: 'video/mpeg;base64,NMP[...]')");if(p&&!p.toLowerCase().includes("base64,"))throw new Error("addMedia() error: `cover` value lacks a base64 header! Ex: 'data:image/png;base64,iV[...]')");if("online"!==u||l)return e=t.extn||(A?A.split(";")[0].split("/")[1]:c.split(".").pop())||"mp3",d.mtype=u,d.media=c||"preencoded.mov",d.options={},d.options.x=a,d.options.y=o,d.options.w=i,d.options.h=s,d.options.objectName=f,"online"===u?(n=g(r),r._relsMedia.push({path:c||"preencoded"+e,data:"dummy",type:"online",extn:e,rId:n,Target:l}),d.mediaRid=n,r._relsMedia.push({path:"preencoded.png",data:p,type:"image/png",extn:"png",rId:g(r),Target:"../media/image-".concat(r._slideNum,"-").concat(r._relsMedia.length+1,".png")})):(t=r._relsMedia.filter(function(t){return t.path&&t.path===c&&t.type===u+"/"+e&&!t.isDuplicate})[0],n=g(r),r._relsMedia.push({path:c||"preencoded"+e,type:u+"/"+e,extn:e,data:A||"",rId:n,isDuplicate:!(null==t||!t.Target),Target:null!=t&&t.Target?t.Target:"../media/media-".concat(r._slideNum,"-").concat(r._relsMedia.length+1,".").concat(e)}),d.mediaRid=n,r._relsMedia.push({path:c||"preencoded"+e,type:u+"/"+e,extn:e,data:A||"",rId:g(r),isDuplicate:!(null==t||!t.Target),Target:null!=t&&t.Target?t.Target:"../media/media-".concat(r._slideNum,"-").concat(r._relsMedia.length+0,".").concat(e)}),r._relsMedia.push({path:"preencoded.png",type:"image/png",extn:"png",data:p,rId:g(r),Target:"../media/image-".concat(r._slideNum,"-").concat(r._relsMedia.length+1,".png")})),r._slideObjects.push(d),this;throw new Error("addMedia() error: online videos require `link` value")},e.prototype.addNotes=function(t){return this._slideObjects.push({_type:_.notes,text:[{text:t}]}),this},e.prototype.addShape=function(t,e){return Ct(this,t,e),this},e.prototype.addTable=function(t,e){return this._newAutoPagedSlides=function(r,t,e,a,n,o,i){var s,A,l,c=[r],u=e&&"object"==typeof e?e:{};if(u.objectName=u.objectName?F(u.objectName):"Table ".concat(r._slideObjects.filter(function(t){return t._type===_.table}).length),null===t||0===t.length||!Array.isArray(t))throw new Error("addTable: Array expected! EX: 'slide.addTable( [rows], {options} );' (https://gitbrent.github.io/PptxGenJS/docs/api-tables.html)");if(t[0]&&Array.isArray(t[0]))return s=[],t.forEach(function(t){var n=[];Array.isArray(t)?t.forEach(function(t){var e={_type:_.tablecell,text:"",options:"object"==typeof t&&t.options?t.options:{}},t=("string"==typeof t||"number"==typeof t?e.text=t.toString():t.text&&("string"==typeof t.text||"number"==typeof t.text?e.text=t.text.toString():t.text&&(e.text=t.text),t.options)&&"object"==typeof t.options&&(e.options=t.options),e.options.border=e.options.border||u.border||[{type:"none"},{type:"none"},{type:"none"},{type:"none"}],e.options.border);Array.isArray(t)||"object"!=typeof t||(e.options.border=[t,t,t,t]),e.options.border[0]||(e.options.border[0]={type:"none"}),e.options.border[1]||(e.options.border[1]={type:"none"}),e.options.border[2]||(e.options.border[2]={type:"none"}),e.options.border[3]||(e.options.border[3]={type:"none"});[0,1,2,3].forEach(function(t){e.options.border[t]={type:e.options.border[t].type||q,color:e.options.border[t].color||J,pt:"number"==typeof e.options.border[t].pt?e.options.border[t].pt:K}}),n.push(e)}):(console.log("addTable: tableRows has a bad row. A row should be an array of cells. You provided:"),console.log(t)),s.push(n)}),u.x=N(u.x||(0===u.x?0:k/2),"X",n),u.y=N(u.y||(0===u.y?0:k/2),"Y",n),u.h&&(u.h=N(u.h,"Y",n)),u.fontSize=u.fontSize||P,u.margin=0===u.margin||u.margin?u.margin:Z,"number"==typeof u.margin&&(u.margin=[Number(u.margin),Number(u.margin),Number(u.margin),Number(u.margin)]),u.color||(u.color=u.color||C),"string"==typeof u.border?(console.warn("addTable `border` option must be an object. Ex: `{border: {type:'none'}}`"),u.border=null):Array.isArray(u.border)&&[0,1,2,3].forEach(function(t){u.border[t]=u.border[t]?{type:u.border[t].type||q,color:u.border[t].color||J,pt:u.border[t].pt||K}:{type:"none"}}),u.autoPage="boolean"==typeof u.autoPage&&u.autoPage,u.autoPageRepeatHeader="boolean"==typeof u.autoPageRepeatHeader&&u.autoPageRepeatHeader,u.autoPageHeaderRows=void 0===u.autoPageHeaderRows||isNaN(Number(u.autoPageHeaderRows))?1:Number(u.autoPageHeaderRows),u.autoPageLineWeight=void 0===u.autoPageLineWeight||isNaN(Number(u.autoPageLineWeight))?0:Number(u.autoPageLineWeight),u.autoPageLineWeight&&(1<u.autoPageLineWeight?u.autoPageLineWeight=1:u.autoPageLineWeight<-1&&(u.autoPageLineWeight=-1)),A=at,a&&void 0!==a._margin&&(Array.isArray(a._margin)?A=a._margin:isNaN(Number(a._margin))||(A=[Number(a._margin),Number(a._margin),Number(a._margin),Number(a._margin)])),u.colW?(e=s[0].reduce(function(t,e){var n;return null!=(n=null==e?void 0:e.options)&&n.colspan&&"number"==typeof e.options.colspan?t+=e.options.colspan:t+=1,t},0),"string"==typeof u.colW||"number"==typeof u.colW||u.colW&&Array.isArray(u.colW)&&1===u.colW.length&&1<e?(u.w=Math.floor(Number(u.colW)*e),u.colW=null):u.colW&&Array.isArray(u.colW)&&u.colW.length!==e&&(console.warn("addTable: mismatch: (colW.length != data.length) Therefore, defaulting to evenly distributed col widths."),u.colW=null)):u.w?u.w=N(u.w,"X",n):u.w=Math.floor(n._sizeW/k-A[1]-A[3]),u.x&&u.x<20&&(u.x=I(u.x)),u.y&&u.y<20&&(u.y=I(u.y)),u.w&&u.w<20&&(u.w=I(u.w)),u.h&&u.h<20&&(u.h=I(u.h)),s.forEach(function(n){n.forEach(function(t,e){"number"==typeof t||"string"==typeof t?n[e]={_type:_.tablecell,text:String(n[e]),options:u}:"object"==typeof t&&("number"==typeof t.text?n[e].text=n[e].text.toString():void 0!==t.text&&null!==t.text||(n[e].text=""),n[e].options=t.options||{},n[e]._type=_.tablecell)})}),l=[],u&&!u.autoPage?(m(r,s),r._slideObjects.push({_type:_.table,arrTabRows:s,options:Object.assign({},u)})):(u.autoPageRepeatHeader&&(u._arrObjTabHeadRows=s.filter(function(t,e){return e<u.autoPageHeaderRows})),yt(s,u,n,a).forEach(function(t,e){i(r._slideNum+e)||c.push(o({masterName:(null==a?void 0:a._name)||null})),0<e&&(u.y=I(u.autoPageSlideStartY||u.newSlideStartY||A[0]));var n=i(r._slideNum+e);u.autoPage=!1,m(n,t.rows),n.addTable(t.rows,Object.assign({},u)),0<e&&l.push(n)})),l;throw new Error("addTable: 'rows' should be an array of cells! EX: 'slide.addTable( [ ['A'], ['B'], {text:'C',options:{align:'center'}} ] );' (https://gitbrent.github.io/PptxGenJS/docs/api-tables.html)")}(this,t,e,this._slideLayout,this._presLayout,this.addSlide,this.getSlide),this},e.prototype.addText=function(t,e){return Pt(this,"string"==typeof t||"number"==typeof t?[{text:t,options:e}]:t,e,!1),this};var Lt=e;function e(t){this.addSlide=t.addSlide,this.getSlide=t.getSlide,this._name="Slide ".concat(t.slideNumber),this._presLayout=t.presLayout,this._rId=t.slideRId,this._rels=[],this._relsChart=[],this._relsMedia=[],this._setSlideNum=t.setSlideNum,this._slideId=t.slideId,this._slideLayout=t.slideLayout||null,this._slideNum=t.slideNumber,this._slideObjects=[],this._slideNumberProps=null!=(t=this._slideLayout)&&t._slideNumberProps?this._slideLayout._slideNumberProps:null}function Et(m,v){return u(this,void 0,void 0,function(){var g;return p(this,function(t){switch(t.label){case 0:return g=m.data,[4,new Promise(function(e,n){var r,t,a,o,i=new T.default,s=2*(g.length-1)+1,A=1<(null==(A=null==(A=g[0])?void 0:A.labels)?void 0:A.length),l=(i.folder("_rels"),i.folder("docProps"),i.folder("xl/_rels"),i.folder("xl/tables"),i.folder("xl/theme"),i.folder("xl/worksheets"),i.folder("xl/worksheets/_rels"),i.file("[Content_Types].xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">  <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>  <Default Extension="xml" ContentType="application/xml"/>  <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>  <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>  <Override PartName="/xl/theme/theme1.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml"/>  <Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml"/>  <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>  <Override PartName="/xl/tables/table1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml"/>  <Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml"/>  <Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"/></Types>\n'),i.file("_rels/.rels",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/><Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/></Relationships>\n'),i.file("docProps/app.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes"><Application>Microsoft Macintosh Excel</Application><DocSecurity>0</DocSecurity><ScaleCrop>false</ScaleCrop><HeadingPairs><vt:vector size="2" baseType="variant"><vt:variant><vt:lpstr>Worksheets</vt:lpstr></vt:variant><vt:variant><vt:i4>1</vt:i4></vt:variant></vt:vector></HeadingPairs><TitlesOfParts><vt:vector size="1" baseType="lpstr"><vt:lpstr>Sheet1</vt:lpstr></vt:vector></TitlesOfParts><Company></Company><LinksUpToDate>false</LinksUpToDate><SharedDoc>false</SharedDoc><HyperlinksChanged>false</HyperlinksChanged><AppVersion>16.0300</AppVersion></Properties>\n'),i.file("docProps/core.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><dc:creator>PptxGenJS</dc:creator><cp:lastModifiedBy>PptxGenJS</cp:lastModifiedBy><dcterms:created xsi:type="dcterms:W3CDTF">'+(new Date).toISOString()+'</dcterms:created><dcterms:modified xsi:type="dcterms:W3CDTF">'+(new Date).toISOString()+"</dcterms:modified></cp:coreProperties>"),i.file("xl/_rels/workbook.xml.rels",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="theme/theme1.xml"/><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/><Relationship Id="rId4" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/></Relationships>'),i.file("xl/styles.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main"><numFmts count="1"><numFmt numFmtId="0" formatCode="General"/></numFmts><fonts count="4"><font><sz val="9"/><color indexed="8"/><name val="Geneva"/></font><font><sz val="9"/><color indexed="8"/><name val="Geneva"/></font><font><sz val="10"/><color indexed="8"/><name val="Geneva"/></font><font><sz val="18"/><color indexed="8"/><name val="Arial"/></font></fonts><fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills><borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders><dxfs count="0"/><tableStyles count="0"/><colors><indexedColors><rgbColor rgb="ff000000"/><rgbColor rgb="ffffffff"/><rgbColor rgb="ffff0000"/><rgbColor rgb="ff00ff00"/><rgbColor rgb="ff0000ff"/><rgbColor rgb="ffffff00"/><rgbColor rgb="ffff00ff"/><rgbColor rgb="ff00ffff"/><rgbColor rgb="ff000000"/><rgbColor rgb="ffffffff"/><rgbColor rgb="ff878787"/><rgbColor rgb="fff9f9f9"/></indexedColors></colors></styleSheet>\n'),i.file("xl/theme/theme1.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme"><a:themeElements><a:clrScheme name="Office"><a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1><a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1><a:dk2><a:srgbClr val="44546A"/></a:dk2><a:lt2><a:srgbClr val="E7E6E6"/></a:lt2><a:accent1><a:srgbClr val="4472C4"/></a:accent1><a:accent2><a:srgbClr val="ED7D31"/></a:accent2><a:accent3><a:srgbClr val="A5A5A5"/></a:accent3><a:accent4><a:srgbClr val="FFC000"/></a:accent4><a:accent5><a:srgbClr val="5B9BD5"/></a:accent5><a:accent6><a:srgbClr val="70AD47"/></a:accent6><a:hlink><a:srgbClr val="0563C1"/></a:hlink><a:folHlink><a:srgbClr val="954F72"/></a:folHlink></a:clrScheme><a:fontScheme name="Office"><a:majorFont><a:latin typeface="Calibri Light" panose="020F0302020204030204"/><a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="Yu Gothic Light"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="DengXian Light"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Times New Roman"/><a:font script="Hebr" typeface="Times New Roman"/><a:font script="Thai" typeface="Tahoma"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="MoolBoran"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Times New Roman"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/></a:majorFont><a:minorFont><a:latin typeface="Calibri" panose="020F0502020204030204"/><a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="Yu Gothic"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="DengXian"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Arial"/><a:font script="Hebr" typeface="Arial"/><a:font script="Thai" typeface="Tahoma"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="DaunPenh"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Arial"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/></a:minorFont></a:fontScheme><a:fmtScheme name="Office"><a:fillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:lumMod val="110000"/><a:satMod val="105000"/><a:tint val="67000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:lumMod val="105000"/><a:satMod val="103000"/><a:tint val="73000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:lumMod val="105000"/><a:satMod val="109000"/><a:tint val="81000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:satMod val="103000"/><a:lumMod val="102000"/><a:tint val="94000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:satMod val="110000"/><a:lumMod val="100000"/><a:shade val="100000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:lumMod val="99000"/><a:satMod val="120000"/><a:shade val="78000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill></a:fillStyleLst><a:lnStyleLst><a:ln w="6350" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln><a:ln w="12700" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln><a:ln w="19050" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln></a:lnStyleLst><a:effectStyleLst><a:effectStyle><a:effectLst/></a:effectStyle><a:effectStyle><a:effectLst/></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="57150" dist="19050" dir="5400000" algn="ctr" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="63000"/></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle></a:effectStyleLst><a:bgFillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:solidFill><a:schemeClr val="phClr"><a:tint val="95000"/><a:satMod val="170000"/></a:schemeClr></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="93000"/><a:satMod val="150000"/><a:shade val="98000"/><a:lumMod val="102000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:tint val="98000"/><a:satMod val="130000"/><a:shade val="90000"/><a:lumMod val="103000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="63000"/><a:satMod val="120000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill></a:bgFillStyleLst></a:fmtScheme></a:themeElements><a:objectDefaults/><a:extraClrSchemeLst/><a:extLst><a:ext uri="{05A4C25C-085E-4340-85A3-A5531E510DB2}"><thm15:themeFamily xmlns:thm15="http://schemas.microsoft.com/office/thememl/2012/main" name="Office Theme" id="{62F939B6-93AF-4DB8-9C6B-D6C7DFDC589F}" vid="{4A3C46E8-61CC-4603-A589-7422A47A8E4A}"/></a:ext></a:extLst></a:theme>'),i.file("xl/workbook.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x15" xmlns:x15="http://schemas.microsoft.com/office/spreadsheetml/2010/11/main"><fileVersion appName="xl" lastEdited="7" lowestEdited="6" rupBuild="10507"/><workbookPr/><bookViews><workbookView xWindow="0" yWindow="500" windowWidth="20960" windowHeight="15960"/></bookViews><sheets><sheet name="Sheet1" sheetId="1" r:id="rId1"/></sheets><calcPr calcId="0" concurrentCalc="0"/></workbook>\n'),i.file("xl/worksheets/_rels/sheet1.xml.rels",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/table" Target="../tables/table1.xml"/></Relationships>\n'),'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'),c=(m.opts._type===b.BUBBLE||m.opts._type===b.BUBBLE3D?l+='<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="'.concat(s,'" uniqueCount="').concat(s,'">'):m.opts._type===b.SCATTER?l+='<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="'.concat(g.length,'" uniqueCount="').concat(g.length,'">'):l=A?(r=g.length,g[0].labels.forEach(function(t){return r+=t.filter(function(t){return t&&""!==t}).length}),l+'<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="'.concat(r,'" uniqueCount="').concat(r,'">')+"<si><t/></si>"):(t=g.length+g[0].labels.length*g[0].labels[0].length+g[0].labels.length,a=g.length+g[0].labels.length*g[0].labels[0].length+1,l+'<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="'.concat(t,'" uniqueCount="').concat(a,'">')+'<si><t xml:space="preserve"></t></si>'),m.opts._type===b.BUBBLE||m.opts._type===b.BUBBLE3D?g.forEach(function(t,e){0===e?l+="<si><t>X-Axis</t></si>":l=(l+="<si><t>".concat(F(t.name||"Y-Axis".concat(e)),"</t></si>"))+"<si><t>".concat(F("Size".concat(e)),"</t></si>")}):g.forEach(function(t){l+="<si><t>".concat(F((t.name||" ").replace("X-Axis","X-Values")),"</t></si>")}),m.opts._type!==b.BUBBLE&&m.opts._type!==b.BUBBLE3D&&m.opts._type!==b.SCATTER&&g[0].labels.slice().reverse().forEach(function(t){t.filter(function(t){return t&&""!==t}).forEach(function(t){l+="<si><t>".concat(F(t),"</t></si>")})}),l+="</sst>\n",i.file("xl/sharedStrings.xml",l),'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'),u=(m.opts._type===b.BUBBLE||m.opts._type===b.BUBBLE3D?(c=(c+='<table xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" id="1" name="Table1" displayName="Table1" ref="A1:'.concat(L(s)).concat(s,'" totalsRowShown="0">'))+'<tableColumns count="'.concat(s,'">'),o=1,g.forEach(function(t,e){0===e?c+='<tableColumn id="'.concat(e+1,'" name="X-Values"/>'):(c+='<tableColumn id="'.concat(e+o,'" name="').concat(t.name,'"/>'),o++,c+='<tableColumn id="'.concat(e+o,'" name="Size').concat(e,'"/>'))})):m.opts._type===b.SCATTER?(c=(c+='<table xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" id="1" name="Table1" displayName="Table1" ref="A1:'.concat(L(g.length)).concat(g[0].values.length+1,'" totalsRowShown="0">'))+'<tableColumns count="'.concat(g.length,'">'),g.forEach(function(t,e){c+='<tableColumn id="'.concat(e+1,'" name="').concat(0===e?"X-Values":"Y-Value ").concat(e,'"/>')})):(c=(c+='<table xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" id="1" name="Table1" displayName="Table1" ref="A1:'.concat(L(g.length+g[0].labels.length)).concat(g[0].labels[0].length+1,'\'" totalsRowShown="0">'))+'<tableColumns count="'.concat(g.length+g[0].labels.length,'">'),g[0].labels.forEach(function(t,e){c+='<tableColumn id="'.concat(e+1,'" name="Column').concat(e+1,'"/>')}),g.forEach(function(t,e){c+='<tableColumn id="'.concat(e+g[0].labels.length+1,'" name="').concat(F(t.name),'"/>')})),c=(c+="</tableColumns>")+'<tableStyleInfo showFirstColumn="0" showLastColumn="0" showRowStripes="1" showColumnStripes="0"/>'+"</table>",i.file("xl/tables/table1.xml",c),'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>');if(u+='<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">',m.opts._type===b.BUBBLE||m.opts._type===b.BUBBLE3D?u+='<dimension ref="A1:'.concat(L(s)).concat(g[0].values.length+1,'"/>'):m.opts._type===b.SCATTER?u+='<dimension ref="A1:'.concat(L(g.length)).concat(g[0].values.length+1,'"/>'):u+='<dimension ref="A1:'.concat(L(g.length+1)).concat(g[0].values.length+1,'"/>'),u=u+'<sheetViews><sheetView tabSelected="1" workbookViewId="0"><selection activeCell="B1" sqref="B1"/></sheetView></sheetViews>'+'<sheetFormatPr baseColWidth="10" defaultRowHeight="16"/>',m.opts._type===b.BUBBLE||m.opts._type===b.BUBBLE3D){for(var u=(u+="<sheetData>")+'<row r="1" spans="1:'.concat(s,'">')+'<c r="A1" t="s"><v>0</v></c>',p=1;p<s;p++)u+='<c r="'.concat(L(p+1),'1" t="s"><v>').concat(p,"</v></c>");u+="</row>",g[0].values.forEach(function(t,e){u=(u+='<row r="'.concat(e+2,'" spans="1:').concat(s,'">'))+'<c r="A'.concat(e+2,'"><v>').concat(t,"</v></c>");for(var n=2,r=1;r<g.length;r++)u=(u+='<c r="'.concat(L(n)).concat(e+2,'"><v>').concat(g[r].values[e]||"","</v></c>"))+'<c r="'.concat(L(++n)).concat(e+2,'"><v>').concat(g[r].sizes[e]||"","</v></c>"),n++;u+="</row>"})}else if(m.opts._type===b.SCATTER){u=(u+="<sheetData>")+'<row r="1" spans="1:'.concat(g.length,'">');for(p=0;p<g.length;p++)u+='<c r="'.concat(L(p+1),'1" t="s"><v>').concat(p,"</v></c>");u+="</row>",g[0].values.forEach(function(t,e){u=(u+='<row r="'.concat(e+2,'" spans="1:').concat(g.length,'">'))+'<c r="A'.concat(e+2,'"><v>').concat(t,"</v></c>");for(var n=1;n<g.length;n++)u+='<c r="'.concat(L(n+1)).concat(e+2,'"><v>').concat(g[n].values[e]||0===g[n].values[e]?g[n].values[e]:"","</v></c>");u+="</row>"})}else if(u+="<sheetData>",A){u+='<row r="1" spans="1:'.concat(g.length+g[0].labels.length,'">');for(p=0;p<g[0].labels.length;p++)u+='<c r="'.concat(L(p+1),'1" t="s"><v>0</v></c>');for(p=g[0].labels.length-1;p<g.length+g[0].labels.length-1;p++)u+='<c r="'.concat(L(p+g[0].labels.length),'1" t="s"><v>').concat(p,"</v></c>");u+="</row>";for(var f=g.length,d=g[0].labels[0].length,h=g[0].labels.length,p=0;p<d;p++)!function(n){u+='<row r="'.concat(n+2,'" spans="1:').concat(f+h,'">');var r=f,a=g[0].labels.slice().reverse();a.forEach(function(t,e){t[n]&&(t=0===e?1:a[e-1].filter(function(t){return t&&""!==t}).length,r+=t,u+='<c r="'.concat(L(n+1+e)).concat(n+2,'" t="s"><v>').concat(r,"</v></c>"))});for(var t=0;t<f;t++)u+='<c r="'.concat(L(h+t+1)).concat(n+2,'"><v>').concat(g[t].values[n]||0,"</v></c>");u+="</row>"}(p)}else{u+='<row r="1" spans="1:'.concat(g.length+g[0].labels.length,'">'),g[0].labels.forEach(function(t,e){u+='<c r="'.concat(L(e+1),'1" t="s"><v>0</v></c>')});for(var p=0;p<g.length;p++)u+='<c r="'.concat(L(p+1+g[0].labels.length),'1" t="s"><v>').concat(p+1,"</v></c>");u+="</row>",g[0].labels[0].forEach(function(t,e){u+='<row r="'.concat(e+2,'" spans="1:').concat(g.length+g[0].labels.length,'">');for(var n=g[0].labels.length-1;0<=n;n--)u=(u+='<c r="'.concat(L(g[0].labels.length-n)).concat(e+2,'" t="s">'))+"<v>".concat(g.length+e+1,"</v>")+"</c>";for(var r=0;r<g.length;r++)u+='<c r="'.concat(L(g[0].labels.length+r+1)).concat(e+2,'"><v>').concat(g[r].values[e]||"","</v></c>");u+="</row>"})}u+='</sheetData><pageMargins left="0.7" right="0.7" top="0.75" bottom="0.75" header="0.3" footer="0.3"/></worksheet>\n',i.file("xl/worksheets/sheet1.xml",u),i.generateAsync({type:"base64"}).then(function(t){v.file("ppt/embeddings/Microsoft_Excel_Worksheet".concat(m.globalId,".xlsx"),t,{base64:!0}),v.file("ppt/charts/_rels/"+m.fileName+".rels",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">'+'<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/package" Target="../embeddings/Microsoft_Excel_Worksheet'.concat(m.globalId,'.xlsx"/>')+"</Relationships>"),v.file("ppt/charts/".concat(m.fileName),function(a){var t,o='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>',i=!1;o=(o+='<c:chartSpace xmlns:c="http://schemas.openxmlformats.org/drawingml/2006/chart" xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"><c:date1904 val="0"/>')+'<c:roundedCorners val="'.concat(a.opts.chartArea.roundedCorners?"1":"0",'"/>')+"<c:chart>",a.opts.showTitle?o=o+_t({title:a.opts.title||"Chart Title",color:a.opts.titleColor,fontFace:a.opts.titleFontFace,fontSize:a.opts.titleFontSize||et,titleAlign:a.opts.titleAlign,titleBold:a.opts.titleBold,titlePos:a.opts.titlePos,titleRotate:a.opts.titleRotate},a.opts.x,a.opts.y)+'<c:autoTitleDeleted val="0"/>':o+='<c:autoTitleDeleted val="1"/>';a.opts._type===b.BAR3D&&(o+='<c:view3D><c:rotX val="'.concat(a.opts.v3DRotX,'"/><c:rotY val="').concat(a.opts.v3DRotY,'"/><c:rAngAx val="').concat(a.opts.v3DRAngAx?1:0,'"/><c:perspective val="').concat(a.opts.v3DPerspective,'"/></c:view3D>'));o+="<c:plotArea>",a.opts.layout?o=(o=(o=(o=(o=(o=(o=(o+="<c:layout>")+' <c:manualLayout>  <c:layoutTarget val="inner" />')+'  <c:xMode val="edge" />  <c:yMode val="edge" />')+'  <c:x val="'+(a.opts.layout.x||0)+'" />')+'  <c:y val="'+(a.opts.layout.y||0)+'" />')+'  <c:w val="'+(a.opts.layout.w||1)+'" />')+'  <c:h val="'+(a.opts.layout.h||1)+'" />')+" </c:manualLayout></c:layout>":o+="<c:layout/>";Array.isArray(a.opts._type)?a.opts._type.forEach(function(t){var e=y(y({},a.opts),t.options),n=e.secondaryValAxis?st:S,r=e.secondaryCatAxis?lt:At;i=i||e.secondaryValAxis,o+=Tt(t.type,t.data,e,n,r)}):o+=Tt(a.opts._type,a.data,a.opts,S,At);if(a.opts._type!==b.PIE&&a.opts._type!==b.DOUGHNUT){if(a.opts.valAxes&&1<a.opts.valAxes.length&&!i)throw new Error("Secondary axis must be used by one of the multiple charts");if(a.opts.catAxes){if(!a.opts.valAxes||a.opts.valAxes.length!==a.opts.catAxes.length)throw new Error("There must be the same number of value and category axes.");o+=Bt(y(y({},a.opts),a.opts.catAxes[0]),At,S)}else o+=Bt(a.opts,At,S);a.opts.valAxes?(o+=Dt(y(y({},a.opts),a.opts.valAxes[0]),S),a.opts.valAxes[1]&&(o+=Dt(y(y({},a.opts),a.opts.valAxes[1]),st))):(o+=Dt(a.opts,S),a.opts._type===b.BAR3D&&(o+=function(e,t,n){var r="";r=(r=(r=(r=(r=r+'<c:serAx>  <c:axId val="'+t+'"/>')+'  <c:scaling><c:orientation val="'+(e.serAxisOrientation||(e.barDir,"minMax"))+'"/></c:scaling>')+'  <c:delete val="'+(e.serAxisHidden?"1":"0")+'"/>')+'  <c:axPos val="'+("col"===e.barDir?"b":"l")+'"/>')+("none"!==e.serGridLine.style?kt(e.serGridLine):""),e.showSerAxisTitle&&(r+=_t({color:e.serAxisTitleColor,fontFace:e.serAxisTitleFontFace,fontSize:e.serAxisTitleFontSize,titleRotate:e.serAxisTitleRotate,title:e.serAxisTitle||"Axis Title"}));r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r+='  <c:numFmt formatCode="'.concat(F(e.serLabelFormatCode)||"General",'" sourceLinked="0"/>'))+'  <c:majorTickMark val="out"/>  <c:minorTickMark val="none"/>')+'  <c:tickLblPos val="'.concat(e.serAxisLabelPos||"col"===e.barDir?"low":"nextTo",'"/>'))+'  <c:spPr>    <a:ln w="12700" cap="flat">')+(e.serAxisLineShow?"<a:solidFill>".concat(M(e.serAxisLineColor||x.color),"</a:solidFill>"):"<a:noFill/>")+'      <a:prstDash val="solid"/>')+"      <a:round/>    </a:ln>")+"  </c:spPr>  <c:txPr>")+"    <a:bodyPr/>    <a:lstStyle/>")+"    <a:p>    <a:pPr>")+'    <a:defRPr sz="'.concat(Math.round(100*(e.serAxisLabelFontSize||P)),'" b="').concat(e.serAxisLabelFontBold?"1":"0",'" i="').concat(e.serAxisLabelFontItalic?"1":"0",'" u="none" strike="noStrike">'))+"      <a:solidFill>".concat(M(e.serAxisLabelColor||C),"</a:solidFill>"))+'      <a:latin typeface="'.concat(e.serAxisLabelFontFace||"Arial",'"/>'))+"   </a:defRPr>  </a:pPr>")+'  <a:endParaRPr lang="'+(e.lang||"en-US")+'"/>')+"  </a:p> </c:txPr>")+' <c:crossAx val="'+n+'"/> <c:crosses val="autoZero"/>',e.serAxisLabelFrequency&&(r+=' <c:tickLblSkip val="'+e.serAxisLabelFrequency+'"/>');e.serLabelFormatCode&&(["serAxisBaseTimeUnit","serAxisMajorTimeUnit","serAxisMinorTimeUnit"].forEach(function(t){!e[t]||"string"==typeof e[t]&&["days","months","years"].includes(t.toLowerCase())||(console.warn('"'.concat(t,"\" must be one of: 'days','months','years' !")),e[t]=null)}),e.serAxisBaseTimeUnit&&(r+=' <c:baseTimeUnit  val="'.concat(e.serAxisBaseTimeUnit.toLowerCase(),'"/>')),e.serAxisMajorTimeUnit&&(r+=' <c:majorTimeUnit val="'.concat(e.serAxisMajorTimeUnit.toLowerCase(),'"/>')),e.serAxisMinorTimeUnit&&(r+=' <c:minorTimeUnit val="'.concat(e.serAxisMinorTimeUnit.toLowerCase(),'"/>')),e.serAxisMajorUnit&&(r+=' <c:majorUnit val="'.concat(e.serAxisMajorUnit,'"/>')),e.serAxisMinorUnit)&&(r+=' <c:minorUnit val="'.concat(e.serAxisMinorUnit,'"/>'));return r+="</c:serAx>"}(a.opts,ct,S))),null!=(t=a.opts)&&t.catAxes&&null!=(t=a.opts)&&t.catAxes[1]&&(o+=Bt(y(y({},a.opts),a.opts.catAxes[1]),lt,st))}a.opts.showDataTable&&(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o+="<c:dTable>")+'  <c:showHorzBorder val="'.concat(a.opts.showDataTableHorzBorder?1:0,'"/>'))+'  <c:showVertBorder val="'.concat(a.opts.showDataTableVertBorder?1:0,'"/>'))+'  <c:showOutline    val="'.concat(a.opts.showDataTableOutline?1:0,'"/>'))+'  <c:showKeys       val="'.concat(a.opts.showDataTableKeys?1:0,'"/>'))+"  <c:spPr>    <a:noFill/>")+'    <a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="tx1"><a:lumMod val="15000"/><a:lumOff val="85000"/></a:schemeClr></a:solidFill><a:round/></a:ln>    <a:effectLst/>')+"  </c:spPr>  <c:txPr>")+'   <a:bodyPr rot="0" spcFirstLastPara="1" vertOverflow="ellipsis" vert="horz" wrap="square" anchor="ctr" anchorCtr="1"/>   <a:lstStyle/>')+'   <a:p>     <a:pPr rtl="0">')+'       <a:defRPr sz="'.concat(Math.round(100*(a.opts.dataTableFontSize||P)),'" b="0" i="0" u="none" strike="noStrike" kern="1200" baseline="0">'))+'         <a:solidFill><a:schemeClr val="tx1"><a:lumMod val="65000"/><a:lumOff val="35000"/></a:schemeClr></a:solidFill>         <a:latin typeface="+mn-lt"/>')+'         <a:ea typeface="+mn-ea"/>         <a:cs typeface="+mn-cs"/>')+"       </a:defRPr>     </a:pPr>")+'    <a:endParaRPr lang="en-US"/>   </a:p>')+" </c:txPr></c:dTable>");o=(o=(o=(o+="  <c:spPr>")+(null!=(t=a.opts.plotArea.fill)&&t.color?z(a.opts.plotArea.fill):"<a:noFill/>"))+(a.opts.plotArea.border?'<a:ln w="'.concat(R(a.opts.plotArea.border.pt),'" cap="flat">').concat(z(a.opts.plotArea.border.color),"</a:ln>"):"<a:ln><a:noFill/></a:ln>")+"    <a:effectLst/>")+"  </c:spPr></c:plotArea>",a.opts.showLegend&&(o=(o+="<c:legend>")+'<c:legendPos val="'+a.opts.legendPos+'"/><c:overlay val="0"/>',(a.opts.legendFontFace||a.opts.legendFontSize||a.opts.legendColor)&&(o=(o=(o=(o+="<c:txPr>")+"  <a:bodyPr/>  <a:lstStyle/>")+"  <a:p>    <a:pPr>")+(a.opts.legendFontSize?'<a:defRPr sz="'.concat(Math.round(100*Number(a.opts.legendFontSize)),'">'):"<a:defRPr>"),a.opts.legendColor&&(o+=z(a.opts.legendColor)),a.opts.legendFontFace&&(o+='<a:latin typeface="'+a.opts.legendFontFace+'"/>'),a.opts.legendFontFace&&(o+='<a:cs    typeface="'+a.opts.legendFontFace+'"/>'),o=(o=(o+="      </a:defRPr>")+'    </a:pPr>    <a:endParaRPr lang="en-US"/>')+"  </a:p></c:txPr>"),o+="</c:legend>");o=(o+='  <c:plotVisOnly val="1"/>')+'  <c:dispBlanksAs val="'+a.opts.displayBlanksAs+'"/>',a.opts._type===b.SCATTER&&(o+='<c:showDLblsOverMax val="1"/>');return o=(o=(o=(o=(o+="</c:chart><c:spPr>")+(null!=(t=a.opts.chartArea.fill)&&t.color?z(a.opts.chartArea.fill):"<a:noFill/>"))+(a.opts.chartArea.border?'<a:ln w="'.concat(R(a.opts.chartArea.border.pt),'" cap="flat">').concat(z(a.opts.chartArea.border.color),"</a:ln>"):"<a:ln><a:noFill/></a:ln>"))+"  <a:effectLst/></c:spPr>")+'<c:externalData r:id="rId1"><c:autoUpdate val="0"/></c:externalData></c:chartSpace>'}(m)),e("")}).catch(function(t){n(t)})})];case 1:return[2,t.sent()]}})})}function Tt(r,a,o,t,e){var i=-1,s=1,n=null,A="";switch(r){case b.AREA:case b.BAR:case b.BAR3D:case b.LINE:case b.RADAR:A+="<c:".concat(r,"Chart>"),r===b.AREA&&"stacked"===o.barGrouping&&(A+='<c:grouping val="'+o.barGrouping+'"/>'),r!==b.BAR&&r!==b.BAR3D||(A=(A+='<c:barDir val="'+o.barDir+'"/>')+'<c:grouping val="'+(o.barGrouping||"clustered")+'"/>'),r===b.RADAR&&(A+='<c:radarStyle val="'+o.radarStyle+'"/>'),A+='<c:varyColors val="0"/>',a.forEach(function(t){i++,A=(A=(A=(A+="<c:ser>")+'  <c:idx val="'.concat(t._dataIndex,'"/><c:order val="').concat(t._dataIndex,'"/>')+"  <c:tx>    <c:strRef>")+"      <c:f>Sheet1!$"+L(t._dataIndex+t.labels.length+1)+"$1</c:f>")+'      <c:strCache><c:ptCount val="1"/><c:pt idx="0"><c:v>'+F(t.name)+"</c:v></c:pt></c:strCache>    </c:strRef>  </c:tx>";var e=o.chartColors?o.chartColors[i%o.chartColors.length]:null;A+="  <c:spPr>","transparent"===e?A+="<a:noFill/>":o.chartColorsOpacity?A+="<a:solidFill>"+M(e,'<a:alpha val="'.concat(Math.round(1e3*o.chartColorsOpacity),'"/>'))+"</a:solidFill>":A+="<a:solidFill>"+M(e)+"</a:solidFill>",r===b.LINE||r===b.RADAR?0===o.lineSize?A+="<a:ln><a:noFill/></a:ln>":A=(A+='<a:ln w="'.concat(R(o.lineSize),'" cap="').concat(Nt(o.lineCap),'"><a:solidFill>').concat(M(e),"</a:solidFill>"))+('<a:prstDash val="'+(o.lineDash||"solid"))+'"/><a:round/></a:ln>':o.dataBorder&&(A+='<a:ln w="'.concat(R(o.dataBorder.pt),'" cap="').concat(Nt(o.lineCap),'"><a:solidFill>').concat(M(o.dataBorder.color),'</a:solidFill><a:prstDash val="solid"/><a:round/></a:ln>')),A=A+v(o.shadow,c)+'  </c:spPr>  <c:invertIfNegative val="0"/>',r!==b.RADAR&&(A=(A+="<c:dLbls>")+'<c:numFmt formatCode="'.concat(F(o.dataLabelFormatCode)||"General",'" sourceLinked="0"/>'),o.dataLabelBkgrdColors&&(A+="<c:spPr><a:solidFill>".concat(M(e),"</a:solidFill></c:spPr>")),A=(A=(A=(A+="<c:txPr><a:bodyPr/><a:lstStyle/><a:p><a:pPr>")+'<a:defRPr b="'.concat(o.dataLabelFontBold?1:0,'" i="').concat(o.dataLabelFontItalic?1:0,'" strike="noStrike" sz="').concat(Math.round(100*(o.dataLabelFontSize||P)),'" u="none">'))+"<a:solidFill>".concat(M(o.dataLabelColor||C),"</a:solidFill>"))+'<a:latin typeface="'.concat(o.dataLabelFontFace||"Arial",'"/>')+"</a:defRPr></a:pPr></a:p></c:txPr>",o.dataLabelPosition&&(A+='<c:dLblPos val="'.concat(o.dataLabelPosition,'"/>')),A=(A=(A=(A+='<c:showLegendKey val="0"/>')+'<c:showVal val="'.concat(o.showValue?"1":"0",'"/>'))+'<c:showCatName val="0"/><c:showSerName val="'.concat(o.showSerName?"1":"0",'"/><c:showPercent val="0"/><c:showBubbleSize val="0"/>'))+'<c:showLeaderLines val="'.concat(o.showLeaderLines?"1":"0",'"/>')+"</c:dLbls>"),r!==b.LINE&&r!==b.RADAR||(A=(A+="<c:marker>")+'  <c:symbol val="'+o.lineDataSymbol+'"/>',o.lineDataSymbolSize&&(A+='<c:size val="'.concat(o.lineDataSymbolSize,'"/>')),A=(A=(A+="  <c:spPr>")+"    <a:solidFill>".concat(M(o.chartColors[t._dataIndex+1>o.chartColors.length?Math.floor(Math.random()*o.chartColors.length):t._dataIndex]),"</a:solidFill>"))+'    <a:ln w="'.concat(o.lineDataSymbolLineSize,'" cap="flat"><a:solidFill>').concat(M(o.lineDataSymbolLineColor||e),'</a:solidFill><a:prstDash val="solid"/><a:round/></a:ln>')+"    <a:effectLst/>  </c:spPr></c:marker>"),r!==b.BAR&&r!==b.BAR3D||1!==a.length||!(o.chartColors&&o.chartColors!==pt&&1<o.chartColors.length||null!=(e=o.invertedColors)&&e.length)||t.values.forEach(function(t,e){t=t<0?o.invertedColors||o.chartColors||pt:o.chartColors||[];A=(A+="  <c:dPt>")+'    <c:idx val="'.concat(e,'"/>')+'      <c:invertIfNegative val="0"/>    <c:bubble3D val="0"/>    <c:spPr>',0===o.lineSize?A+="<a:ln><a:noFill/></a:ln>":A=r===b.BAR?(A+="<a:solidFill>")+'  <a:srgbClr val="'+t[e%t.length]+'"/></a:solidFill>':(A+="<a:ln>  <a:solidFill>")+'   <a:srgbClr val="'+t[e%t.length]+'"/>  </a:solidFill></a:ln>',A=A+v(o.shadow,c)+"    </c:spPr>  </c:dPt>"}),A+="<c:cat>",o.catLabelFormatCode?(A=(A=(A=(A+="  <c:numRef>")+"    <c:f>Sheet1!$A$2:$A$".concat(t.labels[0].length+1,"</c:f>")+"    <c:numCache>")+"      <c:formatCode>"+(o.catLabelFormatCode||"General")+"</c:formatCode>")+'      <c:ptCount val="'.concat(t.labels[0].length,'"/>'),t.labels[0].forEach(function(t,e){return A+='<c:pt idx="'.concat(e,'"><c:v>').concat(F(t),"</c:v></c:pt>")}),A+="    </c:numCache>  </c:numRef>"):(A=(A=(A+="  <c:multiLvlStrRef>")+"    <c:f>Sheet1!$A$2:$".concat(L(t.labels.length),"$").concat(t.labels[0].length+1,"</c:f>")+"    <c:multiLvlStrCache>")+'      <c:ptCount val="'.concat(t.labels[0].length,'"/>'),t.labels.forEach(function(t){A+="<c:lvl>",t.forEach(function(t,e){return A+='<c:pt idx="'.concat(e,'"><c:v>').concat(F(t),"</c:v></c:pt>")}),A+="</c:lvl>"}),A+="    </c:multiLvlStrCache>  </c:multiLvlStrRef>"),A=(A=(A=(A=A+"</c:cat>"+"<c:val>  <c:numRef>")+"<c:f>Sheet1!$".concat(L(t._dataIndex+t.labels.length+1),"$2:$").concat(L(t._dataIndex+t.labels.length+1),"$").concat(t.labels[0].length+1,"</c:f>")+"    <c:numCache>")+"      <c:formatCode>"+(o.valLabelFormatCode||o.dataTableFormatCode||"General")+"</c:formatCode>")+'      <c:ptCount val="'.concat(t.labels[0].length,'"/>'),t.values.forEach(function(t,e){return A+='<c:pt idx="'.concat(e,'"><c:v>').concat(t||0===t?t:"","</c:v></c:pt>")}),A+="    </c:numCache>  </c:numRef></c:val>",r===b.LINE&&(A+='<c:smooth val="'+(o.lineSmooth?"1":"0")+'"/>'),A+="</c:ser>"}),A=(A=(A=(A=(A+="  <c:dLbls>")+'    <c:numFmt formatCode="'.concat(F(o.dataLabelFormatCode)||"General",'" sourceLinked="0"/>')+"    <c:txPr>      <a:bodyPr/>      <a:lstStyle/>      <a:p><a:pPr>")+'        <a:defRPr b="'.concat(o.dataLabelFontBold?1:0,'" i="').concat(o.dataLabelFontItalic?1:0,'" strike="noStrike" sz="').concat(Math.round(100*(o.dataLabelFontSize||P)),'" u="none">'))+"          <a:solidFill>"+M(o.dataLabelColor||C)+"</a:solidFill>")+'          <a:latin typeface="'+(o.dataLabelFontFace||"Arial")+'"/>        </a:defRPr>      </a:pPr></a:p>    </c:txPr>',o.dataLabelPosition&&(A+=' <c:dLblPos val="'+o.dataLabelPosition+'"/>'),A=(A=(A=(A+='    <c:showLegendKey val="0"/>')+'    <c:showVal val="'+(o.showValue?"1":"0")+'"/>    <c:showCatName val="0"/>')+'    <c:showSerName val="'+(o.showSerName?"1":"0")+'"/>    <c:showPercent val="0"/>    <c:showBubbleSize val="0"/>')+'    <c:showLeaderLines val="'.concat(o.showLeaderLines?"1":"0",'"/>')+"  </c:dLbls>",r===b.BAR?A=(A+='  <c:gapWidth val="'.concat(o.barGapWidthPct,'"/>'))+'  <c:overlap val="'.concat((o.barGrouping||"").includes("tacked")?100:o.barOverlapPct||0,'"/>'):r===b.BAR3D?A=(A=(A+='  <c:gapWidth val="'.concat(o.barGapWidthPct,'"/>'))+'  <c:gapDepth val="'.concat(o.barGapDepthPct,'"/>'))+('  <c:shape val="'+o.bar3DShape)+'"/>':r===b.LINE&&(A+='  <c:marker val="1"/>'),A=(A+='<c:axId val="'.concat(e,'"/><c:axId val="').concat(t,'"/><c:axId val="').concat(ct,'"/>'))+"</c:".concat(r,"Chart>");break;case b.SCATTER:A=(A+="<c:"+r+"Chart>")+'<c:scatterStyle val="lineMarker"/>'+'<c:varyColors val="0"/>',i=-1,a.filter(function(t,e){return 0<e}).forEach(function(n,t){i++,A=(A=(A=(A=(A+="<c:ser>")+'  <c:idx val="'.concat(t,'"/>'))+'  <c:order val="'.concat(t,'"/>')+"  <c:tx>    <c:strRef>")+"      <c:f>Sheet1!$".concat(L(t+2),"$1</c:f>"))+'      <c:strCache><c:ptCount val="1"/><c:pt idx="0"><c:v>'+F(n.name)+"</c:v></c:pt></c:strCache>    </c:strRef>  </c:tx>  <c:spPr>";var r,e=o.chartColors[i%o.chartColors.length];"transparent"===e?A+="<a:noFill/>":o.chartColorsOpacity?A+="<a:solidFill>"+M(e,'<a:alpha val="'+Math.round(1e3*o.chartColorsOpacity).toString()+'"/>')+"</a:solidFill>":A+="<a:solidFill>"+M(e)+"</a:solidFill>",0===o.lineSize?A+="<a:ln><a:noFill/></a:ln>":A=(A+='<a:ln w="'.concat(R(o.lineSize),'" cap="').concat(Nt(o.lineCap),'"><a:solidFill>').concat(M(e),"</a:solidFill>"))+'<a:prstDash val="'.concat(o.lineDash||"solid",'"/><a:round/></a:ln>'),A=(A=(A+=v(o.shadow,c))+"  </c:spPr>"+"<c:marker>")+'  <c:symbol val="'+o.lineDataSymbol+'"/>',o.lineDataSymbolSize&&(A+='<c:size val="'.concat(o.lineDataSymbolSize,'"/>')),A=(A=(A+="<c:spPr>")+"<a:solidFill>".concat(M(o.chartColors[t+1>o.chartColors.length?Math.floor(Math.random()*o.chartColors.length):t]),"</a:solidFill>"))+'<a:ln w="'.concat(o.lineDataSymbolLineSize,'" cap="flat"><a:solidFill>').concat(M(o.lineDataSymbolLineColor||o.chartColors[i%o.chartColors.length]),'</a:solidFill><a:prstDash val="solid"/><a:round/></a:ln>')+"<a:effectLst/></c:spPr></c:marker>",o.showLabel&&(r=ht("-xxxx-xxxx-xxxx-xxxxxxxxxxxx"),!n.labels[0]||"custom"!==o.dataLabelFormatScatter&&"customXY"!==o.dataLabelFormatScatter||(A+="<c:dLbls>",n.labels[0].forEach(function(t,e){"custom"!==o.dataLabelFormatScatter&&"customXY"!==o.dataLabelFormatScatter||(A=(A=(A=(A+="  <c:dLbl>")+'    <c:idx val="'.concat(e,'"/>')+"    <c:tx>      <c:rich>            <a:bodyPr>                <a:spAutoFit/>            </a:bodyPr>            <a:lstStyle/>            <a:p>                <a:pPr>                    <a:defRPr/>                </a:pPr>              <a:r>")+'                    <a:rPr lang="'+(o.lang||"en-US")+'" dirty="0"/>')+"                    <a:t>"+F(t)+"</a:t>              </a:r>",A=("customXY"!==o.dataLabelFormatScatter||/^ *$/.test(t)?A:(A=(A=(A=(A=(A=(A=(A=(A=(A=(A+="              <a:r>")+'                  <a:rPr lang="'+(o.lang||"en-US")+'" baseline="0" dirty="0"/>                  <a:t> (</a:t>              </a:r>')+'              <a:fld id="{'+ht("xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")+'}" type="XVALUE">')+'                  <a:rPr lang="'+(o.lang||"en-US")+'" baseline="0"/>                  <a:pPr>                      <a:defRPr/>                  </a:pPr>')+"                  <a:t>["+F(n.name)+"</a:t>              </a:fld>              <a:r>")+'                  <a:rPr lang="'+(o.lang||"en-US")+'" baseline="0" dirty="0"/>                  <a:t>, </a:t>              </a:r>')+'              <a:fld id="{'+ht("xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")+'}" type="YVALUE">')+'                  <a:rPr lang="'+(o.lang||"en-US")+'" baseline="0"/>                  <a:pPr>                      <a:defRPr/>                  </a:pPr>')+"                  <a:t>["+F(n.name)+"]</a:t>              </a:fld>              <a:r>")+'                  <a:rPr lang="'+(o.lang||"en-US")+'" baseline="0" dirty="0"/>                  <a:t>)</a:t>              </a:r>')+'              <a:endParaRPr lang="'+(o.lang||"en-US")+'" dirty="0"/>')+"            </a:p>      </c:rich>    </c:tx>    <c:spPr>        <a:noFill/>        <a:ln>            <a:noFill/>        </a:ln>        <a:effectLst/>    </c:spPr>",o.dataLabelPosition&&(A+=' <c:dLblPos val="'+o.dataLabelPosition+'"/>'),A=(A+='    <c:showLegendKey val="0"/>    <c:showVal val="0"/>    <c:showCatName val="0"/>    <c:showSerName val="0"/>    <c:showPercent val="0"/>    <c:showBubbleSize val="0"/>       <c:showLeaderLines val="1"/>    <c:extLst>      <c:ext uri="{CE6537A1-D6FC-4f65-9D91-7224C49458BB}" xmlns:c15="http://schemas.microsoft.com/office/drawing/2012/chart"/>      <c:ext uri="{C3380CC4-5D6E-409C-BE32-E72D297353CC}" xmlns:c16="http://schemas.microsoft.com/office/drawing/2014/chart">')+'            <c16:uniqueId val="{'.concat("00000000".substring(0,8-(e+1).toString().length).toString()).concat(e+1).concat(r,'}"/>')+"      </c:ext>        </c:extLst></c:dLbl>")}),A+="</c:dLbls>"),"XY"===o.dataLabelFormatScatter)&&(A+='<c:dLbls>    <c:spPr>        <a:noFill/>        <a:ln>            <a:noFill/>        </a:ln>          <a:effectLst/>    </c:spPr>    <c:txPr>        <a:bodyPr>            <a:spAutoFit/>        </a:bodyPr>        <a:lstStyle/>        <a:p>            <a:pPr>                <a:defRPr/>            </a:pPr>            <a:endParaRPr lang="en-US"/>        </a:p>    </c:txPr>',o.dataLabelPosition&&(A+=' <c:dLblPos val="'+o.dataLabelPosition+'"/>'),A=(A=(A=(A+='    <c:showLegendKey val="0"/>')+' <c:showVal val="'.concat(o.showLabel?"1":"0",'"/>'))+' <c:showCatName val="'.concat(o.showLabel?"1":"0",'"/>'))+' <c:showSerName val="'.concat(o.showSerName?"1":"0",'"/>')+'    <c:showPercent val="0"/>    <c:showBubbleSize val="0"/>    <c:extLst>        <c:ext uri="{CE6537A1-D6FC-4f65-9D91-7224C49458BB}" xmlns:c15="http://schemas.microsoft.com/office/drawing/2012/chart">            <c15:showLeaderLines val="1"/>        </c:ext>    </c:extLst></c:dLbls>'),1===a.length&&o.chartColors!==pt&&n.values.forEach(function(t,e){t=t<0?o.invertedColors||o.chartColors||pt:o.chartColors||[];A=(A+="  <c:dPt>")+'    <c:idx val="'.concat(e,'"/>')+'      <c:invertIfNegative val="0"/>    <c:bubble3D val="0"/>    <c:spPr>',0===o.lineSize?A+="<a:ln><a:noFill/></a:ln>":A=(A+="<a:solidFill>")+' <a:srgbClr val="'+t[e%t.length]+'"/></a:solidFill>',A=A+v(o.shadow,c)+"    </c:spPr>  </c:dPt>"}),A=(A=(A+="<c:xVal>  <c:numRef>")+"    <c:f>Sheet1!$A$2:$A$".concat(a[0].values.length+1,"</c:f>")+"    <c:numCache>      <c:formatCode>General</c:formatCode>")+'      <c:ptCount val="'.concat(a[0].values.length,'"/>'),a[0].values.forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(t||0===t?t:"","</c:v></c:pt>")}),A=(A=(A+="    </c:numCache>  </c:numRef></c:xVal><c:yVal>  <c:numRef>")+"    <c:f>Sheet1!$".concat(L(t+2),"$2:$").concat(L(t+2),"$").concat(a[0].values.length+1,"</c:f>")+"    <c:numCache>      <c:formatCode>General</c:formatCode>")+'      <c:ptCount val="'.concat(a[0].values.length,'"/>'),a[0].values.forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(n.values[e]||0===n.values[e]?n.values[e]:"","</c:v></c:pt>")}),A=(A+="    </c:numCache>  </c:numRef></c:yVal>")+'<c:smooth val="'+(o.lineSmooth?"1":"0")+'"/></c:ser>'}),A=(A=(A=(A=(A+="  <c:dLbls>")+'    <c:numFmt formatCode="'.concat(F(o.dataLabelFormatCode)||"General",'" sourceLinked="0"/>')+"    <c:txPr>      <a:bodyPr/>      <a:lstStyle/>      <a:p><a:pPr>")+'        <a:defRPr b="'.concat(o.dataLabelFontBold?"1":"0",'" i="').concat(o.dataLabelFontItalic?"1":"0",'" strike="noStrike" sz="').concat(Math.round(100*(o.dataLabelFontSize||P)),'" u="none">'))+"          <a:solidFill>"+M(o.dataLabelColor||C)+"</a:solidFill>")+'          <a:latin typeface="'+(o.dataLabelFontFace||"Arial")+'"/>        </a:defRPr>      </a:pPr></a:p>    </c:txPr>',o.dataLabelPosition&&(A+=' <c:dLblPos val="'+o.dataLabelPosition+'"/>'),A=(A=(A+='    <c:showLegendKey val="0"/>')+'    <c:showVal val="'+(o.showValue?"1":"0")+'"/>    <c:showCatName val="0"/>')+'    <c:showSerName val="'+(o.showSerName?"1":"0")+'"/>    <c:showPercent val="0"/>    <c:showBubbleSize val="0"/>  </c:dLbls>',A=(A+='<c:axId val="'.concat(e,'"/><c:axId val="').concat(t,'"/>'))+("</c:"+r+"Chart>");break;case b.BUBBLE:case b.BUBBLE3D:A=A+"<c:bubbleChart>"+'<c:varyColors val="0"/>',i=-1,a.filter(function(t,e){return 0<e}).forEach(function(n,t){i++,A=(A=(A=(A=(A+="<c:ser>")+'  <c:idx val="'.concat(t,'"/>'))+'  <c:order val="'.concat(t,'"/>')+"  <c:tx>    <c:strRef>")+"      <c:f>Sheet1!$"+L(s+1)+"$1</c:f>")+'      <c:strCache><c:ptCount val="1"/><c:pt idx="0"><c:v>'+F(n.name)+"</c:v></c:pt></c:strCache>    </c:strRef>  </c:tx><c:spPr>";t=o.chartColors[i%o.chartColors.length];"transparent"===t?A+="<a:noFill/>":o.chartColorsOpacity?A+="<a:solidFill>".concat(M(t,'<a:alpha val="'+Math.round(1e3*o.chartColorsOpacity).toString()+'"/>'),"</a:solidFill>"):A+="<a:solidFill>"+M(t)+"</a:solidFill>",0===o.lineSize?A+="<a:ln><a:noFill/></a:ln>":o.dataBorder?A+='<a:ln w="'.concat(R(o.dataBorder.pt),'" cap="flat"><a:solidFill>').concat(M(o.dataBorder.color),'</a:solidFill><a:prstDash val="solid"/><a:round/></a:ln>'):A=(A+='<a:ln w="'.concat(R(o.lineSize),'" cap="flat"><a:solidFill>').concat(M(t),"</a:solidFill>"))+'<a:prstDash val="'.concat(o.lineDash||"solid",'"/><a:round/></a:ln>'),A=A+v(o.shadow,c)+"</c:spPr>",A=(A=(A+="<c:xVal>  <c:numRef>")+"    <c:f>Sheet1!$A$2:$A$".concat(a[0].values.length+1,"</c:f>")+"    <c:numCache>      <c:formatCode>General</c:formatCode>")+'      <c:ptCount val="'.concat(a[0].values.length,'"/>'),a[0].values.forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(t||0===t?t:"","</c:v></c:pt>")}),A=(A+="    </c:numCache>  </c:numRef></c:xVal><c:yVal>  <c:numRef>")+"<c:f>Sheet1!$".concat(L(s+1),"$2:$").concat(L(s+1),"$").concat(a[0].values.length+1,"</c:f>"),s++,A=(A+="    <c:numCache>      <c:formatCode>General</c:formatCode>")+'      <c:ptCount val="'.concat(a[0].values.length,'"/>'),a[0].values.forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(n.values[e]||0===n.values[e]?n.values[e]:"","</c:v></c:pt>")}),A=(A+="    </c:numCache>  </c:numRef></c:yVal>  <c:bubbleSize>    <c:numRef>")+"<c:f>Sheet1!$".concat(L(s+1),"$2:$").concat(L(s+1),"$").concat(n.sizes.length+1,"</c:f>"),s++,A=(A+="      <c:numCache>        <c:formatCode>General</c:formatCode>")+'           <c:ptCount val="'.concat(n.sizes.length,'"/>'),n.sizes.forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(t||"","</c:v></c:pt>")}),A=(A+="      </c:numCache>    </c:numRef>  </c:bubbleSize>")+'  <c:bubble3D val="'+(r===b.BUBBLE3D?"1":"0")+'"/></c:ser>'}),A=(A=(A=(A=(A+="<c:dLbls>")+'<c:numFmt formatCode="'.concat(F(o.dataLabelFormatCode)||"General",'" sourceLinked="0"/>')+"<c:txPr><a:bodyPr/><a:lstStyle/><a:p><a:pPr>")+'<a:defRPr b="'.concat(o.dataLabelFontBold?1:0,'" i="').concat(o.dataLabelFontItalic?1:0,'" strike="noStrike" sz="').concat(Math.round(100*Math.round(o.dataLabelFontSize||P)),'" u="none">'))+"<a:solidFill>".concat(M(o.dataLabelColor||C),"</a:solidFill>"))+'<a:latin typeface="'.concat(o.dataLabelFontFace||"Arial",'"/>')+"</a:defRPr></a:pPr></a:p></c:txPr>",o.dataLabelPosition&&(A+='<c:dLblPos val="'.concat(o.dataLabelPosition,'"/>')),A=(A=(A=(A=(A+='<c:showLegendKey val="0"/>')+'<c:showVal val="'.concat(o.showValue?"1":"0",'"/>'))+'<c:showCatName val="0"/><c:showSerName val="'.concat(o.showSerName?"1":"0",'"/><c:showPercent val="0"/><c:showBubbleSize val="0"/>')+'<c:extLst>  <c:ext uri="{CE6537A1-D6FC-4f65-9D91-7224C49458BB}" xmlns:c15="http://schemas.microsoft.com/office/drawing/2012/chart">')+'    <c15:showLeaderLines val="'+(o.showLeaderLines?"1":"0")+'"/>  </c:ext></c:extLst></c:dLbls>')+'<c:axId val="'.concat(e,'"/><c:axId val="').concat(t,'"/>')+"</c:bubbleChart>";break;case b.DOUGHNUT:case b.PIE:n=a[0],A=(A=(A=(A=(A=(A=(A=(A=(A=A+("<c:"+r+"Chart>")+'  <c:varyColors val="1"/>')+"<c:ser>"+'  <c:idx val="0"/>')+'  <c:order val="0"/>'+"  <c:tx>")+"    <c:strRef>"+"      <c:f>Sheet1!$B$1</c:f>")+"      <c:strCache>"+'        <c:ptCount val="1"/>')+('        <c:pt idx="0"><c:v>'+F(n.name)+"</c:v></c:pt>"))+"      </c:strCache>"+"    </c:strRef>")+"  </c:tx>"+"  <c:spPr>")+'    <a:solidFill><a:schemeClr val="accent1"/></a:solidFill>'+'    <a:ln w="9525" cap="flat"><a:solidFill><a:srgbClr val="F9F9F9"/></a:solidFill><a:prstDash val="solid"/><a:round/></a:ln>',o.dataNoEffects?A+="<a:effectLst/>":A+=v(o.shadow,c),A+="  </c:spPr>",n.labels[0].forEach(function(t,e){A=(A=(A+="<c:dPt>")+' <c:idx val="'.concat(e,'"/>')+' <c:bubble3D val="0"/> <c:spPr>')+"<a:solidFill>".concat(M(o.chartColors[e+1>o.chartColors.length?Math.floor(Math.random()*o.chartColors.length):e]),"</a:solidFill>"),o.dataBorder&&(A+='<a:ln w="'.concat(R(o.dataBorder.pt),'" cap="flat"><a:solidFill>').concat(M(o.dataBorder.color),'</a:solidFill><a:prstDash val="solid"/><a:round/></a:ln>')),A=A+v(o.shadow,c)+"  </c:spPr></c:dPt>"}),A+="<c:dLbls>",n.labels[0].forEach(function(t,e){A=(A=(A=(A=(A=(A+="<c:dLbl>")+' <c:idx val="'.concat(e,'"/>'))+'  <c:numFmt formatCode="'.concat(F(o.dataLabelFormatCode)||"General",'" sourceLinked="0"/>')+"  <c:spPr/><c:txPr>   <a:bodyPr/><a:lstStyle/>   <a:p><a:pPr>")+'   <a:defRPr sz="'.concat(Math.round(100*(o.dataLabelFontSize||P)),'" b="').concat(o.dataLabelFontBold?1:0,'" i="').concat(o.dataLabelFontItalic?1:0,'" u="none" strike="noStrike">'))+"    <a:solidFill>"+M(o.dataLabelColor||C)+"</a:solidFill>")+'    <a:latin typeface="'.concat(o.dataLabelFontFace||"Arial",'"/>')+"   </a:defRPr>      </a:pPr></a:p>    </c:txPr>",r===b.PIE&&o.dataLabelPosition&&(A+='<c:dLblPos val="'.concat(o.dataLabelPosition,'"/>')),A=(A=(A=(A=(A+='    <c:showLegendKey val="0"/>')+'    <c:showVal val="'+(o.showValue?"1":"0")+'"/>')+'    <c:showCatName val="'+(o.showLabel?"1":"0")+'"/>')+'    <c:showSerName val="'+(o.showSerName?"1":"0")+'"/>')+'    <c:showPercent val="'+(o.showPercent?"1":"0")+'"/>    <c:showBubbleSize val="0"/>  </c:dLbl>'}),A=(A=(A=(A=(A=(A=(A=(A=(A=(A=(A=(A=(A=(A=A+' <c:numFmt formatCode="'.concat(F(o.dataLabelFormatCode)||"General",'" sourceLinked="0"/>')+"    <c:txPr>")+"      <a:bodyPr/>"+"      <a:lstStyle/>")+"      <a:p>"+"        <a:pPr>")+'          <a:defRPr sz="1800" b="'.concat(o.dataLabelFontBold?"1":"0",'" i="').concat(o.dataLabelFontItalic?"1":"0",'" u="none" strike="noStrike">')+'            <a:solidFill><a:srgbClr val="000000"/></a:solidFill><a:latin typeface="Arial"/>')+"          </a:defRPr>"+"        </a:pPr>")+"      </a:p>"+"    </c:txPr>")+(r===b.PIE?'<c:dLblPos val="ctr"/>':""))+'    <c:showLegendKey val="0"/>'+'    <c:showVal val="0"/>')+'    <c:showCatName val="1"/>'+'    <c:showSerName val="0"/>')+'    <c:showPercent val="1"/>'+'    <c:showBubbleSize val="0"/>')+' <c:showLeaderLines val="'.concat(o.showLeaderLines?"1":"0",'"/>')+"</c:dLbls>")+"<c:cat>"+"  <c:strRef>")+"    <c:f>Sheet1!$A$2:$A$".concat(n.labels[0].length+1,"</c:f>")+"    <c:strCache>")+'         <c:ptCount val="'.concat(n.labels[0].length,'"/>'),n.labels[0].forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(F(t),"</c:v></c:pt>")}),A=(A=(A=(A=(A+="    </c:strCache>")+"  </c:strRef>"+"</c:cat>")+"  <c:val>"+"    <c:numRef>")+"      <c:f>Sheet1!$B$2:$B$".concat(n.labels[0].length+1,"</c:f>")+"      <c:numCache>")+'           <c:ptCount val="'.concat(n.labels[0].length,'"/>'),n.values.forEach(function(t,e){A+='<c:pt idx="'.concat(e,'"><c:v>').concat(t||0===t?t:"","</c:v></c:pt>")}),A=(A=(A=A+"      </c:numCache>"+"    </c:numRef>")+"  </c:val>"+"  </c:ser>")+'  <c:firstSliceAng val="'.concat(o.firstSliceAng?Math.round(o.firstSliceAng):0,'"/>'),r===b.DOUGHNUT&&(A+='<c:holeSize val="'.concat("number"==typeof o.holeSize?o.holeSize:"50",'"/>')),A+="</c:"+r+"Chart>";break;default:A+=""}return A}function Bt(e,t,n){var r="";return e._type===b.SCATTER||e._type===b.BUBBLE||e._type===b.BUBBLE3D?r+="<c:valAx>":r+="<c:"+(e.catLabelFormatCode?"dateAx":"catAx")+">",r=(r+='  <c:axId val="'+t+'"/>')+"  <c:scaling>"+('<c:orientation val="'+(e.catAxisOrientation||(e.barDir,"minMax"))+'"/>'),!e.catAxisMaxVal&&0!==e.catAxisMaxVal||(r+='<c:max val="'.concat(e.catAxisMaxVal,'"/>')),!e.catAxisMinVal&&0!==e.catAxisMinVal||(r+='<c:min val="'.concat(e.catAxisMinVal,'"/>')),r=(r=(r=r+"</c:scaling>"+('  <c:delete val="'+(e.catAxisHidden?"1":"0")+'"/>'))+('  <c:axPos val="'+("col"===e.barDir?"b":"l")+'"/>'))+("none"!==e.catGridLine.style?kt(e.catGridLine):""),e.showCatAxisTitle&&(r+=_t({color:e.catAxisTitleColor,fontFace:e.catAxisTitleFontFace,fontSize:e.catAxisTitleFontSize,titleRotate:e.catAxisTitleRotate,title:e.catAxisTitle||"Axis Title"})),e._type===b.SCATTER||e._type===b.BUBBLE||e._type===b.BUBBLE3D?r+='  <c:numFmt formatCode="'+(e.valAxisLabelFormatCode?F(e.valAxisLabelFormatCode):"General")+'" sourceLinked="1"/>':r+='  <c:numFmt formatCode="'+(F(e.catLabelFormatCode)||"General")+'" sourceLinked="1"/>',e._type===b.SCATTER?r+='  <c:majorTickMark val="none"/>  <c:minorTickMark val="none"/>  <c:tickLblPos val="nextTo"/>':r=(r=(r+='  <c:majorTickMark val="'+(e.catAxisMajorTickMark||"out")+'"/>')+'  <c:minorTickMark val="'+(e.catAxisMinorTickMark||"none")+'"/>')+'  <c:tickLblPos val="'+(e.catAxisLabelPos||("col"===e.barDir?"low":"nextTo"))+'"/>',r=(r=(r=(r=(r=(r+="  <c:spPr>")+'    <a:ln w="'.concat(e.catAxisLineSize?R(e.catAxisLineSize):w,'" cap="flat">'))+(e.catAxisLineShow?"<a:solidFill>"+M(e.catAxisLineColor||x.color)+"</a:solidFill>":"<a:noFill/>"))+('      <a:prstDash val="'+(e.catAxisLineStyle||"solid")+'"/>'))+"      <a:round/>"+"    </a:ln>")+"  </c:spPr>"+"  <c:txPr>",e.catAxisLabelRotate?r+='<a:bodyPr rot="'.concat(O(e.catAxisLabelRotate),'"/>'):r+="<a:bodyPr/>",r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r+="    <a:lstStyle/>")+"    <a:p>"+"    <a:pPr>")+'      <a:defRPr sz="'.concat(Math.round(100*(e.catAxisLabelFontSize||P)),'" b="').concat(e.catAxisLabelFontBold?1:0,'" i="').concat(e.catAxisLabelFontItalic?1:0,'" u="none" strike="noStrike">'))+("      <a:solidFill>"+M(e.catAxisLabelColor||C)+"</a:solidFill>"))+('      <a:latin typeface="'+(e.catAxisLabelFontFace||"Arial")+'"/>'))+"   </a:defRPr>"+"  </a:pPr>")+('  <a:endParaRPr lang="'+(e.lang||"en-US")+'"/>')+"  </a:p>")+" </c:txPr>"+(' <c:crossAx val="'+n+'"/>'))+" <c:".concat("number"==typeof e.valAxisCrossesAt?"crossesAt":"crosses",' val="').concat(e.valAxisCrossesAt||"autoZero",'"/>'))+' <c:auto val="1"/>'+' <c:lblAlgn val="ctr"/>')+' <c:noMultiLvlLbl val="'.concat(e.catAxisMultiLevelLabels?0:1,'"/>'),e.catAxisLabelFrequency&&(r+=' <c:tickLblSkip val="'+e.catAxisLabelFrequency+'"/>'),(e.catLabelFormatCode||e._type===b.SCATTER||e._type===b.BUBBLE||e._type===b.BUBBLE3D)&&(e.catLabelFormatCode&&(["catAxisBaseTimeUnit","catAxisMajorTimeUnit","catAxisMinorTimeUnit"].forEach(function(t){!e[t]||"string"==typeof e[t]&&["days","months","years"].includes(e[t].toLowerCase())||(console.warn('"'.concat(t,"\" must be one of: 'days','months','years' !")),e[t]=null)}),e.catAxisBaseTimeUnit&&(r+='<c:baseTimeUnit val="'+e.catAxisBaseTimeUnit.toLowerCase()+'"/>'),e.catAxisMajorTimeUnit&&(r+='<c:majorTimeUnit val="'+e.catAxisMajorTimeUnit.toLowerCase()+'"/>'),e.catAxisMinorTimeUnit)&&(r+='<c:minorTimeUnit val="'+e.catAxisMinorTimeUnit.toLowerCase()+'"/>'),e.catAxisMajorUnit&&(r+='<c:majorUnit val="'.concat(e.catAxisMajorUnit,'"/>')),e.catAxisMinorUnit)&&(r+='<c:minorUnit val="'.concat(e.catAxisMinorUnit,'"/>')),e._type===b.SCATTER||e._type===b.BUBBLE||e._type===b.BUBBLE3D?r+="</c:valAx>":r+="</c:"+(e.catLabelFormatCode?"dateAx":"catAx")+">",r}function Dt(t,e){var n=e===S?"col"===t.barDir?"l":"b":"col"!==t.barDir?"r":"t",r=(e===st&&(n="r"),e===S?At:lt),a="",a=(a+="<c:valAx>")+('  <c:axId val="'+e+'"/>')+"  <c:scaling>";return t.valAxisLogScaleBase&&(a+='<c:logBase val="'.concat(t.valAxisLogScaleBase,'"/>')),a+='<c:orientation val="'+(t.valAxisOrientation||(t.barDir,"minMax"))+'"/>',!t.valAxisMaxVal&&0!==t.valAxisMaxVal||(a+='<c:max val="'.concat(t.valAxisMaxVal,'"/>')),!t.valAxisMinVal&&0!==t.valAxisMinVal||(a+='<c:min val="'.concat(t.valAxisMinVal,'"/>')),a=(a+="  </c:scaling>")+'  <c:delete val="'.concat(t.valAxisHidden?1:0,'"/>')+('  <c:axPos val="'+n+'"/>'),"none"!==t.valGridLine.style&&(a+=kt(t.valGridLine)),t.showValAxisTitle&&(a+=_t({color:t.valAxisTitleColor,fontFace:t.valAxisTitleFontFace,fontSize:t.valAxisTitleFontSize,titleRotate:t.valAxisTitleRotate,title:t.valAxisTitle||"Axis Title"})),a+='<c:numFmt formatCode="'.concat(t.valAxisLabelFormatCode?F(t.valAxisLabelFormatCode):"General",'" sourceLinked="0"/>'),t._type===b.SCATTER?a+='  <c:majorTickMark val="none"/>  <c:minorTickMark val="none"/>  <c:tickLblPos val="nextTo"/>':a=(a=(a+=' <c:majorTickMark val="'+(t.valAxisMajorTickMark||"out")+'"/>')+' <c:minorTickMark val="'+(t.valAxisMinorTickMark||"none")+'"/>')+' <c:tickLblPos val="'+(t.valAxisLabelPos||("col"===t.barDir?"nextTo":"low"))+'"/>',a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a+=" <c:spPr>")+'   <a:ln w="'.concat(t.valAxisLineSize?R(t.valAxisLineSize):w,'" cap="flat">'))+(t.valAxisLineShow?"<a:solidFill>"+M(t.valAxisLineColor||x.color)+"</a:solidFill>":"<a:noFill/>"))+('     <a:prstDash val="'+(t.valAxisLineStyle||"solid")+'"/>'))+"     <a:round/>"+"   </a:ln>")+" </c:spPr>"+" <c:txPr>")+"  <a:bodyPr".concat(t.valAxisLabelRotate?' rot="'+O(t.valAxisLabelRotate).toString()+'"':"","/>")+"  <a:lstStyle/>")+"  <a:p>"+"    <a:pPr>")+'      <a:defRPr sz="'.concat(Math.round(100*(t.valAxisLabelFontSize||P)),'" b="').concat(t.valAxisLabelFontBold?1:0,'" i="').concat(t.valAxisLabelFontItalic?1:0,'" u="none" strike="noStrike">'))+("        <a:solidFill>"+M(t.valAxisLabelColor||C)+"</a:solidFill>"))+('        <a:latin typeface="'+(t.valAxisLabelFontFace||"Arial")+'"/>'))+"      </a:defRPr>"+"    </a:pPr>")+('  <a:endParaRPr lang="'+(t.lang||"en-US")+'"/>')+"  </a:p>")+" </c:txPr>"+(' <c:crossAx val="'+r+'"/>'),"number"==typeof t.catAxisCrossesAt?a+=' <c:crossesAt val="'.concat(t.catAxisCrossesAt,'"/>'):"string"==typeof t.catAxisCrossesAt?a+=' <c:crosses val="'+t.catAxisCrossesAt+'"/>':a+=' <c:crosses val="'+("r"===n||"t"===n?"max":"autoZero")+'"/>',a+=' <c:crossBetween val="'+(t._type===b.SCATTER||Array.isArray(t._type)&&0<t._type.filter(function(t){return t.type===b.AREA}).length?"midCat":"between")+'"/>',t.valAxisMajorUnit&&(a+=' <c:majorUnit val="'.concat(t.valAxisMajorUnit,'"/>')),t.valAxisDisplayUnit&&(a+='<c:dispUnits><c:builtInUnit val="'.concat(t.valAxisDisplayUnit,'"/>').concat(t.valAxisDisplayUnitLabel?"<c:dispUnitsLbl/>":"","</c:dispUnits>")),a+="</c:valAx>"}function _t(t,e,n){var r="left"===t.titleAlign||"right"===t.titleAlign?'<a:pPr algn="'.concat(t.titleAlign.substring(0,1),'">'):"<a:pPr>",a=t.titleRotate?'<a:bodyPr rot="'.concat(O(t.titleRotate),'"/>'):"<a:bodyPr/>",o=t.fontSize?'sz="'.concat(Math.round(100*t.fontSize),'"'):"",i=t.titleBold?1:0,s="<c:layout/>";return t.titlePos&&"number"==typeof t.titlePos.x&&"number"==typeof t.titlePos.y&&(1<=(e=0===(e=t.titlePos.x+e)?0:e*(e/5)/10)&&(e/=10),.1<=e&&(e/=10),1<=(n=0===(n=t.titlePos.y+n)?0:n*(n/5)/10)&&(n/=10),.1<=n&&(n/=10),s='<c:layout><c:manualLayout><c:xMode val="edge"/><c:yMode val="edge"/><c:x val="'.concat(e,'"/><c:y val="').concat(n,'"/></c:manualLayout></c:layout>')),"<c:title>\n      <c:tx>\n        <c:rich>\n          ".concat(a,"\n          <a:lstStyle/>\n          <a:p>\n            ").concat(r,"\n            <a:defRPr ").concat(o,' b="').concat(i,'" i="0" u="none" strike="noStrike">\n              <a:solidFill>').concat(M(t.color||C),'</a:solidFill>\n              <a:latin typeface="').concat(t.fontFace||"Arial",'"/>\n            </a:defRPr>\n          </a:pPr>\n          <a:r>\n            <a:rPr ').concat(o,' b="').concat(i,'" i="0" u="none" strike="noStrike">\n              <a:solidFill>').concat(M(t.color||C),'</a:solidFill>\n              <a:latin typeface="').concat(t.fontFace||"Arial",'"/>\n            </a:rPr>\n            <a:t>').concat(F(t.title)||"","</a:t>\n          </a:r>\n        </a:p>\n        </c:rich>\n      </c:tx>\n      ").concat(s,'\n      <c:overlay val="0"/>\n    </c:title>')}function L(t){t-=1;return t<=25?ut[t]:"".concat(ut[Math.floor(t/ut.length-1)]).concat(ut[t%ut.length])}function v(t,e){var n,r,a,o,i,s;return t?"object"!=typeof t?(console.warn("`shadow` options must be an object. Ex: `{shadow: {type:'none'}}`"),"<a:effectLst/>"):(n="<a:effectLst>",t=(e=y(y({},e),t)).type||"outer",r=R(e.blur),a=R(e.offset),o=Math.round(6e4*e.angle),i=e.color,s=Math.round(1e5*e.opacity),e=e.rotateWithShape?1:0,(n=(n=(n+="<a:".concat(t,'Shdw sx="100000" sy="100000" kx="0" ky="0"  algn="bl" blurRad="').concat(r,'" rotWithShape="').concat(e,'" dist="').concat(a,'" dir="').concat(o,'">'))+'<a:srgbClr val="'.concat(i,'">'))+'<a:alpha val="'.concat(s,'"/></a:srgbClr>'))+"</a:".concat(t,"Shdw>")+"</a:effectLst>"):"<a:effectLst/>"}function kt(t){var e="<c:majorGridlines>";return(e+=" <c:spPr>")+'  <a:ln w="'.concat(R(t.size||x.size),'" cap="').concat(Nt(t.cap||x.cap),'">')+('  <a:solidFill><a:srgbClr val="'+(t.color||x.color)+'"/></a:solidFill>')+('   <a:prstDash val="'+(t.style||x.style)+'"/><a:round/>')+"  </a:ln>"+" </c:spPr>"+"</c:majorGridlines>"}function Nt(t){if(t&&"flat"!==t){if("square"===t)return"sq";if("round"===t)return"rnd";throw new Error("Invalid chart line cap: ".concat(t))}return"flat"}function Ft(t){var o="undefined"!=typeof require&&"undefined"==typeof window?require("fs"):null,i="undefined"!=typeof require&&"undefined"==typeof window?require("https"):null,e=[],s=t._relsMedia.filter(function(t){return"online"!==t.type&&!t.data&&(!t.path||t.path&&!t.path.includes("preencoded"))}),n=[];return s.forEach(function(t){n.includes(t.path)?t.isDuplicate=!0:(t.isDuplicate=!1,n.push(t.path))}),s.filter(function(t){return!t.isDuplicate}).forEach(function(a){e.push(new Promise(function(n,r){var e;if(o&&0!==a.path.indexOf("http"))try{var t=o.readFileSync(a.path);a.data=Buffer.from(t).toString("base64"),s.filter(function(t){return t.isDuplicate&&t.path===a.path}).forEach(function(t){return t.data=a.data}),n("done")}catch(t){a.data=h,s.filter(function(t){return t.isDuplicate&&t.path===a.path}).forEach(function(t){return t.data=a.data}),r(new Error('ERROR: Unable to read media: "'.concat(a.path,'"\n').concat(String(t))))}else o&&i&&0===a.path.indexOf("http")?i.get(a.path,function(t){var e="";t.setEncoding("binary"),t.on("data",function(t){return e+=t}),t.on("end",function(){a.data=Buffer.from(e,"binary").toString("base64"),s.filter(function(t){return t.isDuplicate&&t.path===a.path}).forEach(function(t){return t.data=a.data}),n("done")}),t.on("error",function(t){a.data=h,s.filter(function(t){return t.isDuplicate&&t.path===a.path}).forEach(function(t){return t.data=a.data}),r(new Error("ERROR! Unable to load image (https.get): ".concat(a.path)))})}):((e=new XMLHttpRequest).onload=function(){var t=new FileReader;t.onloadend=function(){a.data=t.result,s.filter(function(t){return t.isDuplicate&&t.path===a.path}).forEach(function(t){return t.data=a.data}),a.isSvgPng?It(a).then(function(){n("done")}).catch(function(t){r(t)}):n("done")},t.readAsDataURL(e.response)},e.onerror=function(t){a.data=h,s.filter(function(t){return t.isDuplicate&&t.path===a.path}).forEach(function(t){return t.data=a.data}),r(new Error("ERROR! Unable to load image (xhr.onerror): ".concat(a.path)))},e.open("GET",a.path),e.responseType="blob",e.send())}))}),t._relsMedia.filter(function(t){return t.isSvgPng&&t.data}).forEach(function(t){o?(t.data=h,e.push(Promise.resolve().then(function(){return"done"}))):e.push(It(t))}),e}function It(a){return u(this,void 0,void 0,function(){return p(this,function(t){switch(t.label){case 0:return[4,new Promise(function(n,e){var r=new Image;r.onload=function(){r.width+r.height===0&&r.onerror("h/w=0");var t=document.createElement("CANVAS"),e=t.getContext("2d");t.width=r.width,t.height=r.height,e.drawImage(r,0,0);try{a.data=t.toDataURL(a.type),n("done")}catch(t){r.onerror(t)}},r.onerror=function(t){a.data=h,e(new Error("ERROR! Unable to load image (image.onerror): ".concat(a.path)))},r.src="string"==typeof a.data?a.data:h})];case 1:return[2,t.sent()]}})})}var Rt={cover:function(t,e){var t=t.h/t.w,n=t<e.h/e.w,r=n?e.h/t:e.w,n=n?e.h:e.w*t,t=Math.round(5e4*(1-e.w/r)),r=Math.round(5e4*(1-e.h/n));return'<a:srcRect l="'.concat(t,'" r="').concat(t,'" t="').concat(r,'" b="').concat(r,'"/><a:stretch/>')},contain:function(t,e){var t=t.h/t.w,n=t<e.h/e.w,r=n?e.w:e.h/t,n=n?e.w*t:e.h,t=Math.round(5e4*(1-e.w/r)),r=Math.round(5e4*(1-e.h/n));return'<a:srcRect l="'.concat(t,'" r="').concat(t,'" t="').concat(r,'" b="').concat(r,'"/><a:stretch/>')},crop:function(t,e){var n=e.x,r=t.w-(e.x+e.w),a=e.y,e=t.h-(e.y+e.h),n=Math.round(n/t.w*1e5),r=Math.round(r/t.w*1e5),a=Math.round(a/t.h*1e5),e=Math.round(e/t.h*1e5);return'<a:srcRect l="'.concat(n,'" r="').concat(r,'" t="').concat(a,'" b="').concat(e,'"/><a:stretch/>')}};function Ot(T){var t,B=T._name?'<p:cSld name="'+T._name+'">':"<p:cSld>",D=1;return T._bkgdImgRid?B+='<p:bg><p:bgPr><a:blipFill dpi="0" rotWithShape="1"><a:blip r:embed="rId'.concat(T._bkgdImgRid,'"><a:lum/></a:blip><a:srcRect/><a:stretch><a:fillRect/></a:stretch></a:blipFill><a:effectLst/></p:bgPr></p:bg>'):null!=(t=T.background)&&t.color?B+="<p:bg><p:bgPr>".concat(z(T.background),"</p:bgPr></p:bg>"):!T.bkgd&&T._name&&T._name===nt&&(B+='<p:bg><p:bgRef idx="1001"><a:schemeClr val="bg1"/></p:bgRef></p:bg>'),B=(B=B+"<p:spTree>"+'<p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr>')+'<p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/>'+'<a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr>',T._slideObjects.forEach(function(r,t){var e,n,A,a,o,i,s,l,c=0,u=0,p=N("75%","X",T._presLayout),f=0,d="",h=null,g=0,m=0,v=null,y=null==(e=r.options)?void 0:e.sizing,b=null==(e=r.options)?void 0:e.rounding,w=(void 0!==T._slideLayout&&void 0!==T._slideLayout._slideObjects&&r.options&&r.options.placeholder&&(n=T._slideLayout._slideObjects.filter(function(t){return t.options.placeholder===r.options.placeholder})[0]),r.options=r.options||{},void 0!==r.options.x&&(c=N(r.options.x,"X",T._presLayout)),void 0!==r.options.y&&(u=N(r.options.y,"Y",T._presLayout)),p=void 0!==r.options.w?N(r.options.w,"X",T._presLayout):p),x=f=void 0!==r.options.h?N(r.options.h,"Y",T._presLayout):f;switch(n&&(!n.options.x&&0!==n.options.x||(c=N(n.options.x,"X",T._presLayout)),!n.options.y&&0!==n.options.y||(u=N(n.options.y,"Y",T._presLayout)),!n.options.w&&0!==n.options.w||(p=N(n.options.w,"X",T._presLayout)),!n.options.h&&0!==n.options.h||(f=N(n.options.h,"Y",T._presLayout))),r.options.flipH&&(d+=' flipH="1"'),r.options.flipV&&(d+=' flipV="1"'),r.options.rotate&&(d+=' rot="'.concat(O(r.options.rotate),'"')),r._type){case _.table:if(h=r.arrTabRows,A=r.options,h[m=g=0].forEach(function(t){a=t.options||null,g+=null!==a&&a.colspan?Number(a.colspan):1}),v='<p:graphicFrame><p:nvGraphicFramePr><p:cNvPr id="'.concat(D*T._slideNum+1,'" name="').concat(r.options.objectName,'"/>'),v=(v+='<p:cNvGraphicFramePr><a:graphicFrameLocks noGrp="1"/></p:cNvGraphicFramePr>  <p:nvPr><p:extLst><p:ext uri="{D42A27DB-BD31-4B8C-83A1-F6EECF244321}"><p14:modId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="1579011935"/></p:ext></p:extLst></p:nvPr></p:nvGraphicFramePr>')+'<p:xfrm><a:off x="'.concat(c||(0===c?0:k),'" y="').concat(u||(0===u?0:k),'"/><a:ext cx="').concat(p||(0===p?0:k),'" cy="').concat(f||k,'"/></p:xfrm>')+'<a:graphic><a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/table"><a:tbl><a:tblPr/>',Array.isArray(A.colW)){v+="<a:tblGrid>";for(var C=0;C<g;C++){var P=I(A.colW[C]);null!=P&&!isNaN(P)||(P=("number"==typeof r.options.w?r.options.w:1)/g),v+='<a:gridCol w="'.concat(Math.round(P),'"/>')}}else{m=A.colW||k,r.options.w&&!A.colW&&(m=Math.round(("number"==typeof r.options.w?r.options.w:1)/g)),v+="<a:tblGrid>";for(var S=0;S<g;S++)v+='<a:gridCol w="'.concat(m,'"/>')}v+="</a:tblGrid>",h.forEach(function(a){for(var o,i,t=0;t<a.length;)!function(t){var e=a[t],n=null==(o=e.options)?void 0:o.colspan,r=null==(o=e.options)?void 0:o.rowspan;n&&1<n?(e=new Array(n-1).fill(void 0).map(function(t){return{_type:_.tablecell,options:{rowspan:r},_hmerge:!0}}),a.splice.apply(a,U([t+1,0],e,!1)),t+=n):t+=1,i=t}(t),t=i}),h.forEach(function(t,e){var a=h[e+1];a&&t.forEach(function(t,e){var n=t._rowContinue||(null==(n=t.options)?void 0:n.rowspan),r=null==(r=t.options)?void 0:r.colspan,t=t._hmerge;n&&1<n&&(r={_type:_.tablecell,options:{colspan:r},_rowContinue:n-1,_vmerge:!0,_hmerge:t},a.splice(e,0,r))})}),h.forEach(function(t,e){var n=0;Array.isArray(A.rowH)&&A.rowH[e]?n=I(Number(A.rowH[e])):A.rowH&&!isNaN(Number(A.rowH))?n=I(Number(A.rowH)):(r.options.cy||r.options.h)&&(n=Math.round((r.options.h?I(r.options.h):"number"==typeof r.options.cy?r.options.cy:1)/h.length)),v+='<a:tr h="'.concat(n,'">'),t.forEach(function(t){var e,n,r,a,o,i={rowSpan:1<(null==(s=t.options)?void 0:s.rowspan)?t.options.rowspan:void 0,gridSpan:1<(null==(s=t.options)?void 0:s.colspan)?t.options.colspan:void 0,vMerge:t._vmerge?1:void 0,hMerge:t._hmerge?1:void 0},s=(s=Object.keys(i).map(function(t){return[t,i[t]]}).filter(function(t){return t[0],!!t[1]}).map(function(t){var e=t[0],t=t[1];return"".concat(String(e),'="').concat(String(t),'"')}).join(" "))&&" "+s;t._hmerge||t._vmerge?v+="<a:tc".concat(s,"><a:tcPr/></a:tc>"):(e=t.options||{},t.options=e,["align","bold","border","color","fill","fontFace","fontSize","margin","underline","valign"].forEach(function(t){A[t]&&!e[t]&&0!==e[t]&&(e[t]=A[t])}),n=e.valign?' anchor="'.concat(e.valign.replace(/^c$/i,"ctr").replace(/^m$/i,"ctr").replace("center","ctr").replace("middle","ctr").replace("top","t").replace("btm","b").replace("bottom","b"),'"'):"",r=(r=(null!=(r=null==(r=t._optImp)?void 0:r.fill)&&r.color?t._optImp.fill.color:null!=(r=t._optImp)&&r.fill&&"string"==typeof t._optImp.fill?t._optImp.fill:"")||e.fill?e.fill:"")?z(r):"",a=0===e.margin||e.margin?e.margin:Z,o="",o=1<=(a=Array.isArray(a)||"number"!=typeof a?a:[a,a,a,a])[0]?' marL="'.concat(R(a[3]),'" marR="').concat(R(a[1]),'" marT="').concat(R(a[0]),'" marB="').concat(R(a[2]),'"'):' marL="'.concat(I(a[3]),'" marR="').concat(I(a[1]),'" marT="').concat(I(a[0]),'" marB="').concat(I(a[2]),'"'),v+="<a:tc".concat(s,">").concat(jt(t),"<a:tcPr").concat(o).concat(n,">"),e.border&&Array.isArray(e.border)&&[{idx:3,name:"lnL"},{idx:1,name:"lnR"},{idx:0,name:"lnT"},{idx:2,name:"lnB"}].forEach(function(t){"none"!==e.border[t.idx].type?v=(v=(v=(v+="<a:".concat(t.name,' w="').concat(R(e.border[t.idx].pt),'" cap="flat" cmpd="sng" algn="ctr">'))+"<a:solidFill>".concat(M(e.border[t.idx].color),"</a:solidFill>"))+'<a:prstDash val="'.concat("dash"===e.border[t.idx].type?"sysDash":"solid",'"/><a:round/><a:headEnd type="none" w="med" len="med"/><a:tailEnd type="none" w="med" len="med"/>'))+"</a:".concat(t.name,">"):v+="<a:".concat(t.name,' w="0" cap="flat" cmpd="sng" algn="ctr"><a:noFill/></a:').concat(t.name,">")}),v=v+r+"  </a:tcPr> </a:tc>")}),v+="</a:tr>"}),B+=v=(v=v+"      </a:tbl>"+"    </a:graphicData>")+"  </a:graphic>"+"</p:graphicFrame>",D++;break;case _.text:case _.placeholder:if(r.options.line||0!==f||(f=.3*k),r.options._bodyProp||(r.options._bodyProp={}),r.options.margin&&Array.isArray(r.options.margin)?(r.options._bodyProp.lIns=R(r.options.margin[0]||0),r.options._bodyProp.rIns=R(r.options.margin[1]||0),r.options._bodyProp.bIns=R(r.options.margin[2]||0),r.options._bodyProp.tIns=R(r.options.margin[3]||0)):"number"==typeof r.options.margin&&(r.options._bodyProp.lIns=R(r.options.margin),r.options._bodyProp.rIns=R(r.options.margin),r.options._bodyProp.bIns=R(r.options.margin),r.options._bodyProp.tIns=R(r.options.margin)),B=(B+="<p:sp>")+'<p:nvSpPr><p:cNvPr id="'.concat(t+2,'" name="').concat(r.options.objectName,'">'),null!=(o=r.options.hyperlink)&&o.url&&(B+='<a:hlinkClick r:id="rId'.concat(r.options.hyperlink._rId,'" tooltip="').concat(r.options.hyperlink.tooltip?F(r.options.hyperlink.tooltip):"",'"/>')),null!=(o=r.options.hyperlink)&&o.slide&&(B+='<a:hlinkClick r:id="rId'.concat(r.options.hyperlink._rId,'" tooltip="').concat(r.options.hyperlink.tooltip?F(r.options.hyperlink.tooltip):"",'" action="ppaction://hlinksldjump"/>')),B=(B=(B=(B=(B=(B+="</p:cNvPr>")+("<p:cNvSpPr"+(null!=(o=r.options)&&o.isTextBox?' txBox="1"/>':"/>")))+"<p:nvPr>".concat("placeholder"===r._type?Gt(r):Gt(n),"</p:nvPr>")+"</p:nvSpPr><p:spPr>")+"<a:xfrm".concat(d,">"))+'<a:off x="'.concat(c,'" y="').concat(u,'"/>'))+'<a:ext cx="'.concat(p,'" cy="').concat(f,'"/></a:xfrm>'),"custGeom"===r.shape)B=(B+='<a:custGeom><a:avLst /><a:gdLst></a:gdLst><a:ahLst /><a:cxnLst></a:cxnLst><a:rect l="l" t="t" r="r" b="b" /><a:pathLst>')+'<a:path w="'.concat(p,'" h="').concat(f,'">'),null!=(o=r.options.points)&&o.forEach(function(t,e){if("curve"in t)switch(t.curve.type){case"arc":B+='<a:arcTo hR="'.concat(N(t.curve.hR,"Y",T._presLayout),'" wR="').concat(N(t.curve.wR,"X",T._presLayout),'" stAng="').concat(O(t.curve.stAng),'" swAng="').concat(O(t.curve.swAng),'" />');break;case"cubic":B+='<a:cubicBezTo>\n\t\t\t\t\t\t\t\t\t<a:pt x="'.concat(N(t.curve.x1,"X",T._presLayout),'" y="').concat(N(t.curve.y1,"Y",T._presLayout),'" />\n\t\t\t\t\t\t\t\t\t<a:pt x="').concat(N(t.curve.x2,"X",T._presLayout),'" y="').concat(N(t.curve.y2,"Y",T._presLayout),'" />\n\t\t\t\t\t\t\t\t\t<a:pt x="').concat(N(t.x,"X",T._presLayout),'" y="').concat(N(t.y,"Y",T._presLayout),'" />\n\t\t\t\t\t\t\t\t\t</a:cubicBezTo>');break;case"quadratic":B+='<a:quadBezTo>\n\t\t\t\t\t\t\t\t\t<a:pt x="'.concat(N(t.curve.x1,"X",T._presLayout),'" y="').concat(N(t.curve.y1,"Y",T._presLayout),'" />\n\t\t\t\t\t\t\t\t\t<a:pt x="').concat(N(t.x,"X",T._presLayout),'" y="').concat(N(t.y,"Y",T._presLayout),'" />\n\t\t\t\t\t\t\t\t\t</a:quadBezTo>')}else"close"in t?B+="<a:close />":t.moveTo||0===e?B+='<a:moveTo><a:pt x="'.concat(N(t.x,"X",T._presLayout),'" y="').concat(N(t.y,"Y",T._presLayout),'" /></a:moveTo>'):B+='<a:lnTo><a:pt x="'.concat(N(t.x,"X",T._presLayout),'" y="').concat(N(t.y,"Y",T._presLayout),'" /></a:lnTo>')}),B+="</a:path></a:pathLst></a:custGeom>";else{if(B+='<a:prstGeom prst="'+r.shape+'"><a:avLst>',r.options.rectRadius)B+='<a:gd name="adj" fmla="val '.concat(Math.round(r.options.rectRadius*k*1e5/Math.min(p,f)),'"/>');else if(r.options.angleRange){for(var L=0;L<2;L++){var E=r.options.angleRange[L];B+='<a:gd name="adj'.concat(L+1,'" fmla="val ').concat(O(E),'" />')}r.options.arcThicknessRatio&&(B+='<a:gd name="adj3" fmla="val '.concat(Math.round(5e4*r.options.arcThicknessRatio),'" />'))}B+="</a:avLst></a:prstGeom>"}B+=r.options.fill?z(r.options.fill):"<a:noFill/>",r.options.line&&(B+=r.options.line.width?'<a:ln w="'.concat(R(r.options.line.width),'">'):"<a:ln>",r.options.line.color&&(B+=z(r.options.line)),r.options.line.dashType&&(B+='<a:prstDash val="'.concat(r.options.line.dashType,'"/>')),r.options.line.beginArrowType&&(B+='<a:headEnd type="'.concat(r.options.line.beginArrowType,'"/>')),r.options.line.endArrowType&&(B+='<a:tailEnd type="'.concat(r.options.line.endArrowType,'"/>')),B+="</a:ln>"),r.options.shadow&&"none"!==r.options.shadow.type&&(r.options.shadow.type=r.options.shadow.type||"outer",r.options.shadow.blur=R(r.options.shadow.blur||8),r.options.shadow.offset=R(r.options.shadow.offset||4),r.options.shadow.angle=Math.round(6e4*(r.options.shadow.angle||270)),r.options.shadow.opacity=Math.round(1e5*(r.options.shadow.opacity||.75)),r.options.shadow.color=r.options.shadow.color||ot.color,B=(B=(B=(B+="<a:effectLst>")+" <a:".concat(r.options.shadow.type,"Shdw ").concat("outer"===r.options.shadow.type?'sx="100000" sy="100000" kx="0" ky="0" algn="bl" rotWithShape="0"':"",' blurRad="').concat(r.options.shadow.blur,'" dist="').concat(r.options.shadow.offset,'" dir="').concat(r.options.shadow.angle,'">'))+' <a:srgbClr val="'.concat(r.options.shadow.color,'">'))+' <a:alpha val="'.concat(r.options.shadow.opacity,'"/></a:srgbClr>')+" </a:outerShdw></a:effectLst>"),B=(B+="</p:spPr>")+jt(r)+"</p:sp>";break;case _.image:B=(B=B+"<p:pic>"+"  <p:nvPicPr>")+'<p:cNvPr id="'.concat(t+2,'" name="').concat(r.options.objectName,'" descr="').concat(F(r.options.altText||r.image),'">'),null!=(o=r.hyperlink)&&o.url&&(B+='<a:hlinkClick r:id="rId'.concat(r.hyperlink._rId,'" tooltip="').concat(r.hyperlink.tooltip?F(r.hyperlink.tooltip):"",'"/>')),null!=(o=r.hyperlink)&&o.slide&&(B+='<a:hlinkClick r:id="rId'.concat(r.hyperlink._rId,'" tooltip="').concat(r.hyperlink.tooltip?F(r.hyperlink.tooltip):"",'" action="ppaction://hlinksldjump"/>')),B=(B=(B=B+"    </p:cNvPr>"+'    <p:cNvPicPr><a:picLocks noChangeAspect="1"/></p:cNvPicPr>')+("    <p:nvPr>"+Gt(n)+"</p:nvPr>"))+"  </p:nvPicPr>"+"<p:blipFill>",B=(T._relsMedia||[]).filter(function(t){return t.rId===r.imageRid})[0]&&"svg"===(T._relsMedia||[]).filter(function(t){return t.rId===r.imageRid})[0].extn?(B=(B+='<a:blip r:embed="rId'.concat(r.imageRid-1,'">'))+(r.options.transparency?' <a:alphaModFix amt="'.concat(Math.round(1e3*(100-r.options.transparency)),'"/>'):"")+' <a:extLst>  <a:ext uri="{96DAC541-7B7A-43D3-8B79-37D633B846F1}">')+'   <asvg:svgBlip xmlns:asvg="http://schemas.microsoft.com/office/drawing/2016/SVG/main" r:embed="rId'.concat(r.imageRid,'"/>')+"  </a:ext> </a:extLst></a:blip>":(B+='<a:blip r:embed="rId'.concat(r.imageRid,'">'))+(r.options.transparency?'<a:alphaModFix amt="'.concat(Math.round(1e3*(100-r.options.transparency)),'"/>'):"")+"</a:blip>",null!=y&&y.type?(o=y.w?N(y.w,"X",T._presLayout):p,i=y.h?N(y.h,"Y",T._presLayout):f,s=N(y.x||0,"X",T._presLayout),l=N(y.y||0,"Y",T._presLayout),B+=Rt[y.type]({w:w,h:x},{w:o,h:i,x:s,y:l}),w=o,x=i):B+="  <a:stretch><a:fillRect/></a:stretch>",B=(B=(B=(B=(B+="</p:blipFill>")+"<p:spPr>"+(" <a:xfrm"+d+">"))+'  <a:off x="'.concat(c,'" y="').concat(u,'"/>'))+'  <a:ext cx="'.concat(w,'" cy="').concat(x,'"/>')+" </a:xfrm>")+' <a:prstGeom prst="'.concat(b?"ellipse":"rect",'"><a:avLst/></a:prstGeom>'),r.options.shadow&&"none"!==r.options.shadow.type&&(r.options.shadow.type=r.options.shadow.type||"outer",r.options.shadow.blur=R(r.options.shadow.blur||8),r.options.shadow.offset=R(r.options.shadow.offset||4),r.options.shadow.angle=Math.round(6e4*(r.options.shadow.angle||270)),r.options.shadow.opacity=Math.round(1e5*(r.options.shadow.opacity||.75)),r.options.shadow.color=r.options.shadow.color||ot.color,B=(B=(B=(B=(B+="<a:effectLst>")+"<a:".concat(r.options.shadow.type,"Shdw ").concat("outer"===r.options.shadow.type?'sx="100000" sy="100000" kx="0" ky="0" algn="bl" rotWithShape="0"':"",' blurRad="').concat(r.options.shadow.blur,'" dist="').concat(r.options.shadow.offset,'" dir="').concat(r.options.shadow.angle,'">'))+'<a:srgbClr val="'.concat(r.options.shadow.color,'">'))+'<a:alpha val="'.concat(r.options.shadow.opacity,'"/></a:srgbClr>'))+"</a:".concat(r.options.shadow.type,"Shdw>")+"</a:effectLst>"),B=B+"</p:spPr>"+"</p:pic>";break;case _.media:B="online"===r.mtype?(B=(B=(B=(B+="<p:pic> <p:nvPicPr>")+'<p:cNvPr id="'.concat(r.mediaRid+2,'" name="').concat(r.options.objectName,'"/>')+" <p:cNvPicPr/> <p:nvPr>")+'  <a:videoFile r:link="rId'.concat(r.mediaRid,'"/>')+" </p:nvPr> </p:nvPicPr>")+' <p:blipFill><a:blip r:embed="rId'.concat(r.mediaRid+1,'"/><a:stretch><a:fillRect/></a:stretch></p:blipFill>')+" <p:spPr>")+"  <a:xfrm".concat(d,'><a:off x="').concat(c,'" y="').concat(u,'"/><a:ext cx="').concat(p,'" cy="').concat(f,'"/></a:xfrm>')+'  <a:prstGeom prst="rect"><a:avLst/></a:prstGeom> </p:spPr></p:pic>':(B=(B=(B=(B=(B+="<p:pic> <p:nvPicPr>")+'<p:cNvPr id="'.concat(r.mediaRid+2,'" name="').concat(r.options.objectName,'"><a:hlinkClick r:id="" action="ppaction://media"/></p:cNvPr>')+' <p:cNvPicPr><a:picLocks noChangeAspect="1"/></p:cNvPicPr> <p:nvPr>')+'  <a:videoFile r:link="rId'.concat(r.mediaRid,'"/>')+'  <p:extLst>   <p:ext uri="{DAA4B4D4-6D71-4841-9C94-3DE7FCFB9230}">')+'    <p14:media xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" r:embed="rId'.concat(r.mediaRid+1,'"/>')+"   </p:ext>  </p:extLst> </p:nvPr> </p:nvPicPr>")+' <p:blipFill><a:blip r:embed="rId'.concat(r.mediaRid+2,'"/><a:stretch><a:fillRect/></a:stretch></p:blipFill>')+" <p:spPr>")+"  <a:xfrm".concat(d,'><a:off x="').concat(c,'" y="').concat(u,'"/><a:ext cx="').concat(p,'" cy="').concat(f,'"/></a:xfrm>')+'  <a:prstGeom prst="rect"><a:avLst/></a:prstGeom> </p:spPr></p:pic>';break;case _.chart:B=(B=(B=(B=(B=(B=(B=B+"<p:graphicFrame>"+" <p:nvGraphicFramePr>")+'   <p:cNvPr id="'.concat(t+2,'" name="').concat(r.options.objectName,'" descr="').concat(F(r.options.altText||""),'"/>')+"   <p:cNvGraphicFramePr/>")+"   <p:nvPr>".concat(Gt(n),"</p:nvPr>")+" </p:nvGraphicFramePr>")+' <p:xfrm><a:off x="'.concat(c,'" y="').concat(u,'"/><a:ext cx="').concat(p,'" cy="').concat(f,'"/></p:xfrm>'))+' <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">'+'  <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/chart">')+'   <c:chart r:id="rId'.concat(r.chartRid,'" xmlns:c="http://schemas.openxmlformats.org/drawingml/2006/chart"/>')+"  </a:graphicData>")+" </a:graphic>"+"</p:graphicFrame>";break;default:B+=""}}),T._slideNumberProps&&(T._slideNumberProps.align||(T._slideNumberProps.align="left"),B=(B+='<p:sp> <p:nvSpPr>  <p:cNvPr id="25" name="Slide Number Placeholder 0"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr>  <p:nvPr><p:ph type="sldNum" sz="quarter" idx="4294967295"/></p:nvPr> </p:nvSpPr> <p:spPr>')+"<a:xfrm>"+'<a:off x="'.concat(N(T._slideNumberProps.x,"X",T._presLayout),'" y="').concat(N(T._slideNumberProps.y,"Y",T._presLayout),'"/>')+'<a:ext cx="'.concat(T._slideNumberProps.w?N(T._slideNumberProps.w,"X",T._presLayout):"800000",'" cy="').concat(T._slideNumberProps.h?N(T._slideNumberProps.h,"Y",T._presLayout):"300000",'"/>')+'</a:xfrm> <a:prstGeom prst="rect"><a:avLst/></a:prstGeom> <a:extLst><a:ext uri="{C572A759-6A51-4108-AA02-DFA0A04FC94B}"><ma14:wrappingTextBoxFlag val="0" xmlns:ma14="http://schemas.microsoft.com/office/mac/drawingml/2011/main"/></a:ext></a:extLst></p:spPr><p:txBody><a:bodyPr',T._slideNumberProps.margin&&Array.isArray(T._slideNumberProps.margin)?B=(B=(B=(B+=' lIns="'.concat(R(T._slideNumberProps.margin[3]||0),'"'))+' tIns="'.concat(R(T._slideNumberProps.margin[0]||0),'"'))+' rIns="'.concat(R(T._slideNumberProps.margin[1]||0),'"'))+' bIns="'.concat(R(T._slideNumberProps.margin[2]||0),'"'):"number"==typeof T._slideNumberProps.margin&&(B=(B=(B=(B+=' lIns="'.concat(R(T._slideNumberProps.margin||0),'"'))+' tIns="'.concat(R(T._slideNumberProps.margin||0),'"'))+' rIns="'.concat(R(T._slideNumberProps.margin||0),'"'))+' bIns="'.concat(R(T._slideNumberProps.margin||0),'"')),T._slideNumberProps.valign&&(B+=' anchor="'.concat(T._slideNumberProps.valign.replace("top","t").replace("middle","ctr").replace("bottom","b"),'"')),B+="/>  <a:lstStyle><a:lvl1pPr>",(T._slideNumberProps.fontFace||T._slideNumberProps.fontSize||T._slideNumberProps.color)&&(B+='<a:defRPr sz="'.concat(Math.round(100*(T._slideNumberProps.fontSize||12)),'">'),T._slideNumberProps.color&&(B+=z(T._slideNumberProps.color)),T._slideNumberProps.fontFace&&(B+='<a:latin typeface="'.concat(T._slideNumberProps.fontFace,'"/><a:ea typeface="').concat(T._slideNumberProps.fontFace,'"/><a:cs typeface="').concat(T._slideNumberProps.fontFace,'"/>')),B+="</a:defRPr>"),B+="</a:lvl1pPr></a:lstStyle><a:p>",T._slideNumberProps.align.startsWith("l")?B+='<a:pPr algn="l"/>':T._slideNumberProps.align.startsWith("c")?B+='<a:pPr algn="ctr"/>':T._slideNumberProps.align.startsWith("r")?B+='<a:pPr algn="r"/>':B+='<a:pPr algn="l"/>',B=(B+='<a:fld id="'.concat(dt,'" type="slidenum"><a:rPr b="').concat(T._slideNumberProps.bold?1:0,'" lang="en-US"/>'))+"<a:t>".concat(T._slideNum,'</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p>')+"</p:txBody></p:sp>"),B=B+"</p:spTree>"+"</p:cSld>"}function Mt(t,e){var n=0,r='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+d+'<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">';return t._rels.forEach(function(t){n=Math.max(n,t.rId),t.type.toLowerCase().includes("hyperlink")?"slide"===t.data?r+='<Relationship Id="rId'.concat(t.rId,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slide').concat(t.Target,'.xml"/>'):r+='<Relationship Id="rId'.concat(t.rId,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink" Target="').concat(t.Target,'" TargetMode="External"/>'):t.type.toLowerCase().includes("notesSlide")&&(r+='<Relationship Id="rId'.concat(t.rId,'" Target="').concat(t.Target,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesSlide"/>'))}),(t._relsChart||[]).forEach(function(t){n=Math.max(n,t.rId),r+='<Relationship Id="rId'.concat(t.rId,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart" Target="').concat(t.Target,'"/>')}),(t._relsMedia||[]).forEach(function(t){var e=t.rId.toString();n=Math.max(n,t.rId),t.type.toLowerCase().includes("image")?r+='<Relationship Id="rId'+e+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="'+t.Target+'"/>':t.type.toLowerCase().includes("audio")?r.includes(' Target="'+t.Target+'"')?r+='<Relationship Id="rId'+e+'" Type="http://schemas.microsoft.com/office/2007/relationships/media" Target="'+t.Target+'"/>':r+='<Relationship Id="rId'+e+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio" Target="'+t.Target+'"/>':t.type.toLowerCase().includes("video")?r.includes(' Target="'+t.Target+'"')?r+='<Relationship Id="rId'+e+'" Type="http://schemas.microsoft.com/office/2007/relationships/media" Target="'+t.Target+'"/>':r+='<Relationship Id="rId'+e+'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/video" Target="'+t.Target+'"/>':t.type.toLowerCase().includes("online")&&(r.includes(' Target="'+t.Target+'"')?r+='<Relationship Id="rId'+e+'" Type="http://schemas.microsoft.com/office/2007/relationships/image" Target="'+t.Target+'"/>':r+='<Relationship Id="rId'+e+'" Target="'+t.Target+'" TargetMode="External" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/video"/>')}),e.forEach(function(t,e){r+='<Relationship Id="rId'.concat(n+e+1,'" Type="').concat(t.type,'" Target="').concat(t.target,'"/>')}),r+="</Relationships>"}function zt(t,e){var n,r,a="",o="",i="",s="",A=e?"a:lvl1pPr":"a:pPr",l=R(V),c="<".concat(A).concat(t.options.rtlMode?' rtl="1" ':"");if(t.options.align)switch(t.options.align){case"left":c+=' algn="l"';break;case"right":c+=' algn="r"';break;case"center":c+=' algn="ctr"';break;case"justify":c+=' algn="just"';break;default:c+=""}return t.options.lineSpacing?o='<a:lnSpc><a:spcPts val="'.concat(Math.round(100*t.options.lineSpacing),'"/></a:lnSpc>'):t.options.lineSpacingMultiple&&(o='<a:lnSpc><a:spcPct val="'.concat(Math.round(1e5*t.options.lineSpacingMultiple),'"/></a:lnSpc>')),t.options.indentLevel&&!isNaN(Number(t.options.indentLevel))&&0<t.options.indentLevel&&(c+=' lvl="'.concat(t.options.indentLevel,'"')),t.options.paraSpaceBefore&&!isNaN(Number(t.options.paraSpaceBefore))&&0<t.options.paraSpaceBefore&&(i+='<a:spcBef><a:spcPts val="'.concat(Math.round(100*t.options.paraSpaceBefore),'"/></a:spcBef>')),t.options.paraSpaceAfter&&!isNaN(Number(t.options.paraSpaceAfter))&&0<t.options.paraSpaceAfter&&(i+='<a:spcAft><a:spcPts val="'.concat(Math.round(100*t.options.paraSpaceAfter),'"/></a:spcAft>')),"object"==typeof t.options.bullet?(null!=(r=null==(r=null==t?void 0:t.options)?void 0:r.bullet)&&r.indent&&(l=R(t.options.bullet.indent)),t.options.bullet.type?"number"===t.options.bullet.type.toString().toLowerCase()&&(c+=' marL="'.concat(t.options.indentLevel&&0<t.options.indentLevel?l+l*t.options.indentLevel:l,'" indent="-').concat(l,'"'),a='<a:buSzPct val="100000"/><a:buFont typeface="+mj-lt"/><a:buAutoNum type="'.concat(t.options.bullet.style||"arabicPeriod",'" startAt="').concat(t.options.bullet.numberStartAt||t.options.bullet.startAt||"1",'"/>')):a=t.options.bullet.characterCode?(n="&#x".concat(t.options.bullet.characterCode,";"),/^[0-9A-Fa-f]{4}$/.test(t.options.bullet.characterCode)||(console.warn("Warning: `bullet.characterCode should be a 4-digit unicode charatcer (ex: 22AB)`!"),n=f.DEFAULT),c+=' marL="'.concat(t.options.indentLevel&&0<t.options.indentLevel?l+l*t.options.indentLevel:l,'" indent="-').concat(l,'"'),'<a:buSzPct val="100000"/><a:buChar char="'+n+'"/>'):t.options.bullet.code?(n="&#x".concat(t.options.bullet.code,";"),/^[0-9A-Fa-f]{4}$/.test(t.options.bullet.code)||(console.warn("Warning: `bullet.code should be a 4-digit hex code (ex: 22AB)`!"),n=f.DEFAULT),c+=' marL="'.concat(t.options.indentLevel&&0<t.options.indentLevel?l+l*t.options.indentLevel:l,'" indent="-').concat(l,'"'),'<a:buSzPct val="100000"/><a:buChar char="'+n+'"/>'):(c+=' marL="'.concat(t.options.indentLevel&&0<t.options.indentLevel?l+l*t.options.indentLevel:l,'" indent="-').concat(l,'"'),'<a:buSzPct val="100000"/><a:buChar char="'.concat(f.DEFAULT,'"/>'))):t.options.bullet?(c+=' marL="'.concat(t.options.indentLevel&&0<t.options.indentLevel?l+l*t.options.indentLevel:l,'" indent="-').concat(l,'"'),a='<a:buSzPct val="100000"/><a:buChar char="'.concat(f.DEFAULT,'"/>')):t.options.bullet||(c+=' indent="0" marL="0"',a="<a:buNone/>"),t.options.tabStops&&Array.isArray(t.options.tabStops)&&(r=t.options.tabStops.map(function(t){return'<a:tab pos="'.concat(I(t.position||1),'" algn="').concat(t.alignment||"l",'"/>')}).join(""),s="<a:tabLst>".concat(r,"</a:tabLst>")),c+=">"+o+i+a+s,e&&(c+=Ut(t.options,!0)),c+="</"+A+">"}function Ut(t,e){var n,r,a,o,i="",e=e?"a:defRPr":"a:rPr",i=(i=(i=(i=(i+="<"+e+' lang="'+(t.lang||"en-US")+'"'+(t.lang?' altLang="en-US"':""))+(t.fontSize?' sz="'.concat(Math.round(100*t.fontSize),'"'):""))+(null!=t&&t.bold?' b="'.concat(t.bold?"1":"0",'"'):""))+(null!=t&&t.italic?' i="'.concat(t.italic?"1":"0",'"'):""))+(null!=t&&t.strike?' strike="'.concat("string"==typeof t.strike?t.strike:"sngStrike",'"'):"");if("object"==typeof t.underline&&null!=(n=t.underline)&&n.style?i+=' u="'.concat(t.underline.style,'"'):"string"==typeof t.underline?i+=' u="'.concat(String(t.underline),'"'):t.hyperlink&&(i+=' u="sng"'),t.baseline?i+=' baseline="'.concat(Math.round(50*t.baseline),'"'):t.subscript?i+=' baseline="-40000"':t.superscript&&(i+=' baseline="30000"'),i=i+(t.charSpacing?' spc="'.concat(Math.round(100*t.charSpacing),'" kern="0"'):"")+' dirty="0">',(t.color||t.fontFace||t.outline||"object"==typeof t.underline&&t.underline.color)&&(t.outline&&"object"==typeof t.outline&&(i+='<a:ln w="'.concat(R(t.outline.size||.75),'">').concat(z(t.outline.color||"FFFFFF"),"</a:ln>")),t.color&&(i+=z({color:t.color,transparency:t.transparency})),t.highlight&&(i+="<a:highlight>".concat(M(t.highlight),"</a:highlight>")),"object"==typeof t.underline&&t.underline.color&&(i+="<a:uFill>".concat(z(t.underline.color),"</a:uFill>")),t.glow&&(i+="<a:effectLst>".concat((n=t.glow,a="",r=y(y({},r=it),n),n=Math.round(r.size*w),o=r.color,r=Math.round(1e5*r.opacity),(a+='<a:glow rad="'.concat(n,'">'))+M(o,'<a:alpha val="'.concat(r,'"/>'))+"</a:glow>"),"</a:effectLst>")),t.fontFace)&&(i+='<a:latin typeface="'.concat(t.fontFace,'" pitchFamily="34" charset="0"/><a:ea typeface="').concat(t.fontFace,'" pitchFamily="34" charset="-122"/><a:cs typeface="').concat(t.fontFace,'" pitchFamily="34" charset="-120"/>')),t.hyperlink){if("object"!=typeof t.hyperlink)throw new Error("ERROR: text `hyperlink` option should be an object. Ex: `hyperlink:{url:'https://github.com'}` ");if(!t.hyperlink.url&&!t.hyperlink.slide)throw new Error("ERROR: 'hyperlink requires either `url` or `slide`'");t.hyperlink.url?i+='<a:hlinkClick r:id="rId'.concat(t.hyperlink._rId,'" invalidUrl="" action="" tgtFrame="" tooltip="').concat(t.hyperlink.tooltip?F(t.hyperlink.tooltip):"",'" history="1" highlightClick="0" endSnd="0"').concat(t.color?">":"/>"):t.hyperlink.slide&&(i+='<a:hlinkClick r:id="rId'.concat(t.hyperlink._rId,'" action="ppaction://hlinksldjump" tooltip="').concat(t.hyperlink.tooltip?F(t.hyperlink.tooltip):"",'"').concat(t.color?">":"/>")),t.color&&(i+=' <a:extLst>  <a:ext uri="{A12FA001-AC4F-418D-AE19-62706E023703}">   <ahyp:hlinkClr xmlns:ahyp="http://schemas.microsoft.com/office/drawing/2018/hyperlinkcolor" val="tx"/>  </a:ext> </a:extLst></a:hlinkClick>')}return i+="</".concat(e,">")}function jt(n){var o,t,e,r,a,i=n.options||{},s=[],A=[];return!i||n._type===_.tablecell||void 0!==n.text&&null!==n.text?(o=n._type===_.tablecell?"<a:txBody>":"<p:txBody>",o+=(e="<a:bodyPr",(t=n)&&t._type===_.text&&t.options._bodyProp?(e+=t.options._bodyProp.wrap?' wrap="square"':' wrap="none"',!t.options._bodyProp.lIns&&0!==t.options._bodyProp.lIns||(e+=' lIns="'.concat(t.options._bodyProp.lIns,'"')),!t.options._bodyProp.tIns&&0!==t.options._bodyProp.tIns||(e+=' tIns="'.concat(t.options._bodyProp.tIns,'"')),!t.options._bodyProp.rIns&&0!==t.options._bodyProp.rIns||(e+=' rIns="'.concat(t.options._bodyProp.rIns,'"')),!t.options._bodyProp.bIns&&0!==t.options._bodyProp.bIns||(e+=' bIns="'.concat(t.options._bodyProp.bIns,'"')),e+=' rtlCol="0"',t.options._bodyProp.anchor&&(e+=' anchor="'+t.options._bodyProp.anchor+'"'),t.options._bodyProp.vert&&(e+=' vert="'+t.options._bodyProp.vert+'"'),e+=">",t.options.fit&&("none"===t.options.fit?e+="":"shrink"===t.options.fit?e+="<a:normAutofit/>":"resize"===t.options.fit&&(e+="<a:spAutoFit/>")),t.options.shrinkText&&(e+="<a:normAutofit/>"),e=e+(t.options._bodyProp.autoFit?"<a:spAutoFit/>":"")+"</a:bodyPr>"):e+=' wrap="square" rtlCol="0"></a:bodyPr>',t._type===_.tablecell?"<a:bodyPr/>":e),0===i.h&&i.line&&i.align?o+='<a:lstStyle><a:lvl1pPr algn="l"/></a:lstStyle>':"placeholder"===n._type?o+="<a:lstStyle>".concat(zt(n,!0),"</a:lstStyle>"):o+="<a:lstStyle/>","string"==typeof n.text||"number"==typeof n.text?s.push({text:n.text.toString(),options:i||{}}):n.text&&!Array.isArray(n.text)&&"object"==typeof n.text&&Object.keys(n.text).includes("text")?s.push({text:n.text||"",options:n.options||{}}):Array.isArray(n.text)&&(s=n.text.map(function(t){return{text:t.text,options:t.options}})),s.forEach(function(e,t){e.text||(e.text=""),e.options=e.options||i||{},0===t&&e.options&&!e.options.bullet&&i.bullet&&(e.options.bullet=i.bullet),"string"!=typeof e.text&&"number"!=typeof e.text||(e.text=e.text.toString().replace(/\r*\n/g,d)),e.text.includes(d)&&null===e.text.match(/\n$/g)?e.text.split(d).forEach(function(t){e.options.breakLine=!0,A.push({text:t,options:e.options})}):A.push(e)}),r=[],a=[],A.forEach(function(t,e){0<a.length&&(t.options.align||i.align)?t.options.align!==A[e-1].options.align&&(r.push(a),a=[]):0<a.length&&t.options.bullet&&0<a.length&&(r.push(a),a=[],t.options.breakLine=!1),a.push(t),0<a.length&&t.options.breakLine&&e+1<A.length&&(r.push(a),a=[]),e+1===A.length&&r.push(a)}),r.forEach(function(t){var e,r=!1,a=(o+="<a:p>","<a:pPr ".concat(null!=(e=t[0].options)&&e.rtlMode?' rtl="1" ':""));t.forEach(function(n,t){0<(n.options._lineIdx=t)&&n.options.softBreakBefore&&(o+="<a:br/>"),n.options.align=n.options.align||i.align,n.options.lineSpacing=n.options.lineSpacing||i.lineSpacing,n.options.lineSpacingMultiple=n.options.lineSpacingMultiple||i.lineSpacingMultiple,n.options.indentLevel=n.options.indentLevel||i.indentLevel,n.options.paraSpaceBefore=n.options.paraSpaceBefore||i.paraSpaceBefore,n.options.paraSpaceAfter=n.options.paraSpaceAfter||i.paraSpaceAfter,a=zt(n,!1),o+=a.replace("<a:pPr></a:pPr>",""),Object.entries(i).filter(function(t){var e=t[0];return t[1],!(n.options.hyperlink&&"color"===e)}).forEach(function(t){var e=t[0],t=t[1];"bullet"===e||n.options[e]||(n.options[e]=t)}),o+=(t=n).text?"<a:r>".concat(Ut(t.options,!1),"<a:t>").concat(F(t.text),"</a:t></a:r>"):"",(!n.text&&i.fontSize||n.options.fontSize)&&(r=!0,i.fontSize=i.fontSize||n.options.fontSize)}),n._type===_.tablecell&&(i.fontSize||i.fontFace)?i.fontFace?o=(o=(o=(o+='<a:endParaRPr lang="'.concat(i.lang||"en-US",'"')+(i.fontSize?' sz="'.concat(Math.round(100*i.fontSize),'"'):"")+' dirty="0">')+'<a:latin typeface="'.concat(i.fontFace,'" charset="0"/>'))+'<a:ea typeface="'.concat(i.fontFace,'" charset="0"/>'))+'<a:cs typeface="'.concat(i.fontFace,'" charset="0"/>')+"</a:endParaRPr>":o+='<a:endParaRPr lang="'.concat(i.lang||"en-US",'"')+(i.fontSize?' sz="'.concat(Math.round(100*i.fontSize),'"'):"")+' dirty="0"/>':o+=r?'<a:endParaRPr lang="'.concat(i.lang||"en-US",'"')+(i.fontSize?' sz="'.concat(Math.round(100*i.fontSize),'"'):"")+' dirty="0"/>':'<a:endParaRPr lang="'.concat(i.lang||"en-US",'" dirty="0"/>'),o+="</a:p>"}),o+=n._type===_.tablecell?"</a:txBody>":"</p:txBody>"):""}function Gt(t){var e,n;return t?(e=null!=(e=t.options)&&e._placeholderIdx?t.options._placeholderIdx:"",n=(n=null!=(n=t.options)&&n._placeholderType?t.options._placeholderType:"")&&a[n]?a[n].toString():"","<p:ph\n\t\t".concat(e?' idx="'+e.toString()+'"':"","\n\t\t").concat(n&&a[n]?' type="'.concat(n,'"'):"","\n\t\t").concat(t.text&&0<t.text.length?' hasCustomPrompt="1"':"","\n\t\t/>")):""}function Qt(t){return'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<p:notes xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Slide Image Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1" noRot="1" noChangeAspect="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldImg"/></p:nvPr></p:nvSpPr><p:spPr/></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Notes Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" idx="1"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:r><a:rPr lang="en-US" dirty="0"/><a:t>').concat(F((e="",t._slideObjects.forEach(function(t){t._type===_.notes&&(e+=null!=t&&t.text&&t.text[0]?t.text[0].text:"")}),e.replace(/\r*\n/g,d))),'</a:t></a:r><a:endParaRPr lang="en-US" dirty="0"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Slide Number Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="10"/></p:nvPr></p:nvSpPr><p:spPr/><p:txBody><a:bodyPr/><a:lstStyle/><a:p><a:fld id="').concat(dt,'" type="slidenum"><a:rPr lang="en-US"/><a:t>').concat(t._slideNum,'</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="1024086991"/></p:ext></p:extLst></p:cSld><p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:notes>');var e}function Wt(t,e,n){return Mt(t[n-1],[{target:"../slideLayouts/slideLayout".concat(function(t,e,n){for(var r=0;r<e.length;r++)if(e[r]._name===t[n-1]._slideLayout._name)return r+1;return 1}(t,e,n),".xml"),type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout"},{target:"../notesSlides/notesSlide".concat(n,".xml"),type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesSlide"}])}function n(){var n=this;this._version="3.12.0",this._alignH=G,this._alignV=Q,this._chartType=D,this._outputType=B,this._schemeColor=r,this._shapeType=j,this._charts=b,this._colors=W,this._shapes=A,this.addNewSlide=function(t){var e=0<n.sections.length&&0<n.sections[n.sections.length-1]._slides.filter(function(t){return t._slideNum===n.slides[n.slides.length-1]._slideNum}).length;return t.sectionTitle=e?n.sections[n.sections.length-1].title:null,n.addSlide(t)},this.getSlide=function(e){return n.slides.filter(function(t){return t._slideNum===e})[0]},this.setSlideNumber=function(t){n.masterSlide._slideNumberProps=t,n.slideLayouts.filter(function(t){return t._name===nt})[0]._slideNumberProps=t},this.createChartMediaRels=function(t,n,e){t._relsChart.forEach(function(t){return e.push(Et(t,n))}),t._relsMedia.forEach(function(t){var e;"online"!==t.type&&"hyperlink"!==t.type&&(((e=t.data&&"string"==typeof t.data?t.data:"").includes(",")||e.includes(";"))&&e.includes(",")?e.includes(";")||(e="image/png;"+e):e="image/png;base64,"+e,n.file(t.Target.replace("..","ppt"),e.split(",").pop(),{base64:!0}))})},this.writeFileToBrowser=function(r,a){return u(n,void 0,void 0,function(){var e,n;return p(this,function(t){switch(t.label){case 0:return((e=document.createElement("a")).setAttribute("style","display:none;"),e.dataset.interception="off",document.body.appendChild(e),window.URL.createObjectURL)?(n=window.URL.createObjectURL(new Blob([a],{type:"application/vnd.openxmlformats-officedocument.presentationml.presentation"})),e.href=n,e.download=r,e.click(),setTimeout(function(){window.URL.revokeObjectURL(n),document.body.removeChild(e)},100),[4,Promise.resolve(r)]):[3,2];case 1:return[2,t.sent()];case 2:return[2]}})})},this.exportPresentation=function(c){return u(n,void 0,void 0,function(){var A,e,l,n=this;return p(this,function(t){switch(t.label){case 0:return A=[],e=[],l=new T.default,this.slides.forEach(function(t){e=e.concat(Ft(t))}),this.slideLayouts.forEach(function(t){e=e.concat(Ft(t))}),e=e.concat(Ft(this.masterSlide)),[4,Promise.all(e).then(function(){return u(n,void 0,void 0,function(){var s=this;return p(this,function(t){switch(t.label){case 0:return this.slides.forEach(function(t){var n;t._slideLayout&&((n=t)._slideLayout._slideObjects||[]).forEach(function(e){e._type===_.placeholder&&0===n._slideObjects.filter(function(t){return t.options&&t.options.placeholder===e.options.placeholder}).length&&Pt(n,[{text:""}],e.options,!1)})}),l.folder("_rels"),l.folder("docProps"),l.folder("ppt").folder("_rels"),l.folder("ppt/charts").folder("_rels"),l.folder("ppt/embeddings"),l.folder("ppt/media"),l.folder("ppt/slideLayouts").folder("_rels"),l.folder("ppt/slideMasters").folder("_rels"),l.folder("ppt/slides").folder("_rels"),l.folder("ppt/theme"),l.folder("ppt/notesMasters").folder("_rels"),l.folder("ppt/notesSlides").folder("_rels"),l.file("[Content_Types].xml",(r=this.slides,a=this.slideLayouts,o=this.masterSlide,i=(i=(i=(i=(i=(i='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+d)+'<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml"/>')+'<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/><Default Extension="jpeg" ContentType="image/jpeg"/>')+'<Default Extension="jpg" ContentType="image/jpg"/><Default Extension="svg" ContentType="image/svg+xml"/>')+'<Default Extension="png" ContentType="image/png"/><Default Extension="gif" ContentType="image/gif"/>')+'<Default Extension="m4v" ContentType="video/mp4"/><Default Extension="mp4" ContentType="video/mp4"/>',r.forEach(function(t){(t._relsMedia||[]).forEach(function(t){"image"===t.type||"online"===t.type||"chart"===t.type||"m4v"===t.extn||i.includes(t.type)||(i+='<Default Extension="'+t.extn+'" ContentType="'+t.type+'"/>')})}),i=(i+='<Default Extension="vml" ContentType="application/vnd.openxmlformats-officedocument.vmlDrawing"/><Default Extension="xlsx" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>')+'<Override PartName="/ppt/presentation.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml"/><Override PartName="/ppt/notesMasters/notesMaster1.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.notesMaster+xml"/>',r.forEach(function(t,e){i=(i+='<Override PartName="/ppt/slideMasters/slideMaster'.concat(e+1,'.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml"/>'))+'<Override PartName="/ppt/slides/slide'.concat(e+1,'.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slide+xml"/>'),t._relsChart.forEach(function(t){i+='<Override PartName="'.concat(t.Target,'" ContentType="application/vnd.openxmlformats-officedocument.drawingml.chart+xml"/>')})}),i=(i+='<Override PartName="/ppt/presProps.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.presProps+xml"/><Override PartName="/ppt/viewProps.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.viewProps+xml"/>')+'<Override PartName="/ppt/theme/theme1.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml"/><Override PartName="/ppt/tableStyles.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.tableStyles+xml"/>',a.forEach(function(t,e){i+='<Override PartName="/ppt/slideLayouts/slideLayout'.concat(e+1,'.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"/>'),(t._relsChart||[]).forEach(function(t){i+=' <Override PartName="'+t.Target+'" ContentType="application/vnd.openxmlformats-officedocument.drawingml.chart+xml"/>'})}),r.forEach(function(t,e){i+='<Override PartName="/ppt/notesSlides/notesSlide'.concat(e+1,'.xml" ContentType="application/vnd.openxmlformats-officedocument.presentationml.notesSlide+xml"/>')}),o._relsChart.forEach(function(t){i+=' <Override PartName="'+t.Target+'" ContentType="application/vnd.openxmlformats-officedocument.drawingml.chart+xml"/>'}),o._relsMedia.forEach(function(t){"image"===t.type||"online"===t.type||"chart"===t.type||"m4v"===t.extn||i.includes(t.type)||(i+=' <Default Extension="'+t.extn+'" ContentType="'+t.type+'"/>')}),i=(i+=' <Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml"/>')+' <Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"/></Types>')),l.file("_rels/.rels",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n\t\t<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/>\n\t\t<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/>\n\t\t<Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="ppt/presentation.xml"/>\n\t\t</Relationships>')),l.file("docProps/app.xml",(a=this.slides,r=this.company,'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">\n\t<TotalTime>0</TotalTime>\n\t<Words>0</Words>\n\t<Application>Microsoft Office PowerPoint</Application>\n\t<PresentationFormat>On-screen Show (16:9)</PresentationFormat>\n\t<Paragraphs>0</Paragraphs>\n\t<Slides>').concat(a.length,"</Slides>\n\t<Notes>").concat(a.length,'</Notes>\n\t<HiddenSlides>0</HiddenSlides>\n\t<MMClips>0</MMClips>\n\t<ScaleCrop>false</ScaleCrop>\n\t<HeadingPairs>\n\t\t<vt:vector size="6" baseType="variant">\n\t\t\t<vt:variant><vt:lpstr>Fonts Used</vt:lpstr></vt:variant>\n\t\t\t<vt:variant><vt:i4>2</vt:i4></vt:variant>\n\t\t\t<vt:variant><vt:lpstr>Theme</vt:lpstr></vt:variant>\n\t\t\t<vt:variant><vt:i4>1</vt:i4></vt:variant>\n\t\t\t<vt:variant><vt:lpstr>Slide Titles</vt:lpstr></vt:variant>\n\t\t\t<vt:variant><vt:i4>').concat(a.length,'</vt:i4></vt:variant>\n\t\t</vt:vector>\n\t</HeadingPairs>\n\t<TitlesOfParts>\n\t\t<vt:vector size="').concat(a.length+1+2,'" baseType="lpstr">\n\t\t\t<vt:lpstr>Arial</vt:lpstr>\n\t\t\t<vt:lpstr>Calibri</vt:lpstr>\n\t\t\t<vt:lpstr>Office Theme</vt:lpstr>\n\t\t\t').concat(a.map(function(t,e){return"<vt:lpstr>Slide ".concat(e+1,"</vt:lpstr>")}).join(""),"\n\t\t</vt:vector>\n\t</TitlesOfParts>\n\t<Company>").concat(r,"</Company>\n\t<LinksUpToDate>false</LinksUpToDate>\n\t<SharedDoc>false</SharedDoc>\n\t<HyperlinksChanged>false</HyperlinksChanged>\n\t<AppVersion>16.0000</AppVersion>\n\t</Properties>"))),l.file("docProps/core.xml",(o=this.title,a=this.subject,r=this.author,e=this.revision,'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n\t<cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">\n\t\t<dc:title>'.concat(F(o),"</dc:title>\n\t\t<dc:subject>").concat(F(a),"</dc:subject>\n\t\t<dc:creator>").concat(F(r),"</dc:creator>\n\t\t<cp:lastModifiedBy>").concat(F(r),"</cp:lastModifiedBy>\n\t\t<cp:revision>").concat(e,'</cp:revision>\n\t\t<dcterms:created xsi:type="dcterms:W3CDTF">').concat((new Date).toISOString().replace(/\.\d\d\dZ/,"Z"),'</dcterms:created>\n\t\t<dcterms:modified xsi:type="dcterms:W3CDTF">').concat((new Date).toISOString().replace(/\.\d\d\dZ/,"Z"),"</dcterms:modified>\n\t</cp:coreProperties>"))),l.file("ppt/_rels/presentation.xml.rels",function(t){for(var e=1,n=(n='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+d)+'<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">'+'<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster" Target="slideMasters/slideMaster1.xml"/>',r=1;r<=t.length;r++)n+='<Relationship Id="rId'.concat(++e,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="slides/slide').concat(r,'.xml"/>');return n+='<Relationship Id="rId'.concat(++e+0,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesMaster" Target="notesMasters/notesMaster1.xml"/>')+'<Relationship Id="rId'.concat(e+1,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/presProps" Target="presProps.xml"/>')+'<Relationship Id="rId'.concat(e+2,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/viewProps" Target="viewProps.xml"/>')+'<Relationship Id="rId'.concat(e+3,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="theme/theme1.xml"/>')+'<Relationship Id="rId'.concat(e+4,'" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/tableStyles" Target="tableStyles.xml"/>')+"</Relationships>"}(this.slides)),l.file("ppt/theme/theme1.xml",(a=null!=(a=(o=this).theme)&&a.headFontFace?'<a:latin typeface="'.concat(null==(a=o.theme)?void 0:a.headFontFace,'"/>'):'<a:latin typeface="Calibri Light" panose="020F0302020204030204"/>',o=null!=(r=o.theme)&&r.bodyFontFace?'<a:latin typeface="'.concat(null==(r=o.theme)?void 0:r.bodyFontFace,'"/>'):'<a:latin typeface="Calibri" panose="020F0502020204030204"/>','<?xml version="1.0" encoding="UTF-8" standalone="yes"?><a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme"><a:themeElements><a:clrScheme name="Office"><a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1><a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1><a:dk2><a:srgbClr val="44546A"/></a:dk2><a:lt2><a:srgbClr val="E7E6E6"/></a:lt2><a:accent1><a:srgbClr val="4472C4"/></a:accent1><a:accent2><a:srgbClr val="ED7D31"/></a:accent2><a:accent3><a:srgbClr val="A5A5A5"/></a:accent3><a:accent4><a:srgbClr val="FFC000"/></a:accent4><a:accent5><a:srgbClr val="5B9BD5"/></a:accent5><a:accent6><a:srgbClr val="70AD47"/></a:accent6><a:hlink><a:srgbClr val="0563C1"/></a:hlink><a:folHlink><a:srgbClr val="954F72"/></a:folHlink></a:clrScheme><a:fontScheme name="Office"><a:majorFont>'.concat(a,'<a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="游ゴシック Light"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="等线 Light"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Times New Roman"/><a:font script="Hebr" typeface="Times New Roman"/><a:font script="Thai" typeface="Angsana New"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="MoolBoran"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Times New Roman"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/><a:font script="Armn" typeface="Arial"/><a:font script="Bugi" typeface="Leelawadee UI"/><a:font script="Bopo" typeface="Microsoft JhengHei"/><a:font script="Java" typeface="Javanese Text"/><a:font script="Lisu" typeface="Segoe UI"/><a:font script="Mymr" typeface="Myanmar Text"/><a:font script="Nkoo" typeface="Ebrima"/><a:font script="Olck" typeface="Nirmala UI"/><a:font script="Osma" typeface="Ebrima"/><a:font script="Phag" typeface="Phagspa"/><a:font script="Syrn" typeface="Estrangelo Edessa"/><a:font script="Syrj" typeface="Estrangelo Edessa"/><a:font script="Syre" typeface="Estrangelo Edessa"/><a:font script="Sora" typeface="Nirmala UI"/><a:font script="Tale" typeface="Microsoft Tai Le"/><a:font script="Talu" typeface="Microsoft New Tai Lue"/><a:font script="Tfng" typeface="Ebrima"/></a:majorFont><a:minorFont>').concat(o,'<a:ea typeface=""/><a:cs typeface=""/><a:font script="Jpan" typeface="游ゴシック"/><a:font script="Hang" typeface="맑은 고딕"/><a:font script="Hans" typeface="等线"/><a:font script="Hant" typeface="新細明體"/><a:font script="Arab" typeface="Arial"/><a:font script="Hebr" typeface="Arial"/><a:font script="Thai" typeface="Cordia New"/><a:font script="Ethi" typeface="Nyala"/><a:font script="Beng" typeface="Vrinda"/><a:font script="Gujr" typeface="Shruti"/><a:font script="Khmr" typeface="DaunPenh"/><a:font script="Knda" typeface="Tunga"/><a:font script="Guru" typeface="Raavi"/><a:font script="Cans" typeface="Euphemia"/><a:font script="Cher" typeface="Plantagenet Cherokee"/><a:font script="Yiii" typeface="Microsoft Yi Baiti"/><a:font script="Tibt" typeface="Microsoft Himalaya"/><a:font script="Thaa" typeface="MV Boli"/><a:font script="Deva" typeface="Mangal"/><a:font script="Telu" typeface="Gautami"/><a:font script="Taml" typeface="Latha"/><a:font script="Syrc" typeface="Estrangelo Edessa"/><a:font script="Orya" typeface="Kalinga"/><a:font script="Mlym" typeface="Kartika"/><a:font script="Laoo" typeface="DokChampa"/><a:font script="Sinh" typeface="Iskoola Pota"/><a:font script="Mong" typeface="Mongolian Baiti"/><a:font script="Viet" typeface="Arial"/><a:font script="Uigh" typeface="Microsoft Uighur"/><a:font script="Geor" typeface="Sylfaen"/><a:font script="Armn" typeface="Arial"/><a:font script="Bugi" typeface="Leelawadee UI"/><a:font script="Bopo" typeface="Microsoft JhengHei"/><a:font script="Java" typeface="Javanese Text"/><a:font script="Lisu" typeface="Segoe UI"/><a:font script="Mymr" typeface="Myanmar Text"/><a:font script="Nkoo" typeface="Ebrima"/><a:font script="Olck" typeface="Nirmala UI"/><a:font script="Osma" typeface="Ebrima"/><a:font script="Phag" typeface="Phagspa"/><a:font script="Syrn" typeface="Estrangelo Edessa"/><a:font script="Syrj" typeface="Estrangelo Edessa"/><a:font script="Syre" typeface="Estrangelo Edessa"/><a:font script="Sora" typeface="Nirmala UI"/><a:font script="Tale" typeface="Microsoft Tai Le"/><a:font script="Talu" typeface="Microsoft New Tai Lue"/><a:font script="Tfng" typeface="Ebrima"/></a:minorFont></a:fontScheme><a:fmtScheme name="Office"><a:fillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:lumMod val="110000"/><a:satMod val="105000"/><a:tint val="67000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:lumMod val="105000"/><a:satMod val="103000"/><a:tint val="73000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:lumMod val="105000"/><a:satMod val="109000"/><a:tint val="81000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:satMod val="103000"/><a:lumMod val="102000"/><a:tint val="94000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:satMod val="110000"/><a:lumMod val="100000"/><a:shade val="100000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:lumMod val="99000"/><a:satMod val="120000"/><a:shade val="78000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill></a:fillStyleLst><a:lnStyleLst><a:ln w="6350" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln><a:ln w="12700" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln><a:ln w="19050" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/><a:miter lim="800000"/></a:ln></a:lnStyleLst><a:effectStyleLst><a:effectStyle><a:effectLst/></a:effectStyle><a:effectStyle><a:effectLst/></a:effectStyle><a:effectStyle><a:effectLst><a:outerShdw blurRad="57150" dist="19050" dir="5400000" algn="ctr" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="63000"/></a:srgbClr></a:outerShdw></a:effectLst></a:effectStyle></a:effectStyleLst><a:bgFillStyleLst><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:solidFill><a:schemeClr val="phClr"><a:tint val="95000"/><a:satMod val="170000"/></a:schemeClr></a:solidFill><a:gradFill rotWithShape="1"><a:gsLst><a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="93000"/><a:satMod val="150000"/><a:shade val="98000"/><a:lumMod val="102000"/></a:schemeClr></a:gs><a:gs pos="50000"><a:schemeClr val="phClr"><a:tint val="98000"/><a:satMod val="130000"/><a:shade val="90000"/><a:lumMod val="103000"/></a:schemeClr></a:gs><a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="63000"/><a:satMod val="120000"/></a:schemeClr></a:gs></a:gsLst><a:lin ang="5400000" scaled="0"/></a:gradFill></a:bgFillStyleLst></a:fmtScheme></a:themeElements><a:objectDefaults/><a:extraClrSchemeLst/><a:extLst><a:ext uri="{05A4C25C-085E-4340-85A3-A5531E510DB2}"><thm15:themeFamily xmlns:thm15="http://schemas.microsoft.com/office/thememl/2012/main" name="Office Theme" id="{62F939B6-93AF-4DB8-9C6B-D6C7DFDC589F}" vid="{4A3C46E8-61CC-4603-A589-7422A47A8E4A}"/></a:ext></a:extLst></a:theme>'))),l.file("ppt/presentation.xml",function(t){var e=(e='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d)+'<p:presentation xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" '+'xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" '.concat(t.rtlMode?'rtl="1"':"",' saveSubsetFonts="1" autoCompressPictures="0">'))+'<p:sldMasterIdLst><p:sldMasterId id="2147483648" r:id="rId1"/></p:sldMasterIdLst>'+"<p:sldIdLst>";t.slides.forEach(function(t){return e+='<p:sldId id="'.concat(t._slideId,'" r:id="rId').concat(t._rId,'"/>')}),e=(e=(e=(e+="</p:sldIdLst>")+'<p:notesMasterIdLst><p:notesMasterId r:id="rId'.concat(t.slides.length+2,'"/></p:notesMasterIdLst>'))+'<p:sldSz cx="'.concat(t.presLayout.width,'" cy="').concat(t.presLayout.height,'"/>'))+'<p:notesSz cx="'.concat(t.presLayout.height,'" cy="').concat(t.presLayout.width,'"/>')+"<p:defaultTextStyle>";for(var n=1;n<10;n++)e+="<a:lvl".concat(n,'pPr marL="').concat(457200*(n-1),'" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1">')+'<a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/>'+"</a:defRPr></a:lvl".concat(n,"pPr>");return e+="</p:defaultTextStyle>",t.sections&&0<t.sections.length&&(e+='<p:extLst><p:ext uri="{521415D9-36F7-43E2-AB2F-B90AF26B5E84}"><p14:sectionLst xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main">',t.sections.forEach(function(t){e+='<p14:section name="'.concat(F(t.title),'" id="{').concat(ht("xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"),'}"><p14:sldIdLst>'),t._slides.forEach(function(t){return e+='<p14:sldId id="'.concat(t._slideId,'"/>')}),e+="</p14:sldIdLst></p14:section>"}),e+='</p14:sectionLst></p:ext><p:ext uri="{EFAFB233-063F-42B5-8137-9DF3F51BA10A}"><p15:sldGuideLst xmlns:p15="http://schemas.microsoft.com/office/powerpoint/2012/main"/></p:ext></p:extLst>'),e+="</p:presentation>"}(this)),l.file("ppt/presProps.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<p:presentationPr xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"/>')),l.file("ppt/tableStyles.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<a:tblStyleLst xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" def="{5C22544A-7EE6-4342-B048-85BDC9FD1C3A}"/>')),l.file("ppt/viewProps.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<p:viewPr xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:normalViewPr horzBarState="maximized"><p:restoredLeft sz="15611"/><p:restoredTop sz="94610"/></p:normalViewPr><p:slideViewPr><p:cSldViewPr snapToGrid="0" snapToObjects="1"><p:cViewPr varScale="1"><p:scale><a:sx n="136" d="100"/><a:sy n="136" d="100"/></p:scale><p:origin x="216" y="312"/></p:cViewPr><p:guideLst/></p:cSldViewPr></p:slideViewPr><p:notesTextViewPr><p:cViewPr><p:scale><a:sx n="1" d="1"/><a:sy n="1" d="1"/></p:scale><p:origin x="0" y="0"/></p:cViewPr></p:notesTextViewPr><p:gridSpacing cx="76200" cy="76200"/></p:viewPr>')),this.slideLayouts.forEach(function(t,e){l.file("ppt/slideLayouts/slideLayout".concat(e+1,".xml"),'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n\t\t<p:sldLayout xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main" preserve="1">\n\t\t'.concat(Ot(t),"\n\t\t<p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sldLayout>")),l.file("ppt/slideLayouts/_rels/slideLayout".concat(e+1,".xml.rels"),(t=e+1,Mt(s.slideLayouts[t-1],[{target:"../slideMasters/slideMaster1.xml",type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster"}])))}),this.slides.forEach(function(t,e){var n;l.file("ppt/slides/slide".concat(e+1,".xml"),(n=t,'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d)+'<p:sld xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"'+"".concat(null!=n&&n.hidden?' show="0"':"",">")+"".concat(Ot(n))+"<p:clrMapOvr><a:masterClrMapping/></p:clrMapOvr></p:sld>")),l.file("ppt/slides/_rels/slide".concat(e+1,".xml.rels"),Wt(s.slides,s.slideLayouts,e+1)),l.file("ppt/notesSlides/notesSlide".concat(e+1,".xml"),Qt(t)),l.file("ppt/notesSlides/_rels/notesSlide".concat(e+1,".xml.rels"),'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n\t\t<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n\t\t\t<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/notesMaster" Target="../notesMasters/notesMaster1.xml"/>\n\t\t\t<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide" Target="../slides/slide'.concat(e+1,'.xml"/>\n\t\t</Relationships>'))}),l.file("ppt/slideMasters/slideMaster1.xml",(n=this.masterSlide,e=(e=this.slideLayouts).map(function(t,e){return'<p:sldLayoutId id="'.concat(Y+e,'" r:id="rId').concat(n._rels.length+e+1,'"/>')}),r='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+d,(r+='<p:sldMaster xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main">')+Ot(n)+'<p:clrMap bg1="lt1" tx1="dk1" bg2="lt2" tx2="dk2" accent1="accent1" accent2="accent2" accent3="accent3" accent4="accent4" accent5="accent5" accent6="accent6" hlink="hlink" folHlink="folHlink"/><p:sldLayoutIdLst>'+e.join("")+'</p:sldLayoutIdLst><p:hf sldNum="0" hdr="0" ftr="0" dt="0"/><p:txStyles> <p:titleStyle>  <a:lvl1pPr algn="ctr" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="0"/></a:spcBef><a:buNone/><a:defRPr sz="4400" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mj-lt"/><a:ea typeface="+mj-ea"/><a:cs typeface="+mj-cs"/></a:defRPr></a:lvl1pPr> </p:titleStyle> <p:bodyStyle>  <a:lvl1pPr marL="342900" indent="-342900" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="•"/><a:defRPr sz="3200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr>  <a:lvl2pPr marL="742950" indent="-285750" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="–"/><a:defRPr sz="2800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr>  <a:lvl3pPr marL="1143000" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="•"/><a:defRPr sz="2400" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr>  <a:lvl4pPr marL="1600200" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="–"/><a:defRPr sz="2000" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr>  <a:lvl5pPr marL="2057400" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="»"/><a:defRPr sz="2000" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr>  <a:lvl6pPr marL="2514600" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="•"/><a:defRPr sz="2000" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr>  <a:lvl7pPr marL="2971800" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="•"/><a:defRPr sz="2000" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr>  <a:lvl8pPr marL="3429000" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="•"/><a:defRPr sz="2000" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr>  <a:lvl9pPr marL="3886200" indent="-228600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:spcBef><a:spcPct val="20000"/></a:spcBef><a:buFont typeface="Arial" pitchFamily="34" charset="0"/><a:buChar char="•"/><a:defRPr sz="2000" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr> </p:bodyStyle> <p:otherStyle>  <a:defPPr><a:defRPr lang="en-US"/></a:defPPr>  <a:lvl1pPr marL="0" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr>  <a:lvl2pPr marL="457200" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr>  <a:lvl3pPr marL="914400" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr>  <a:lvl4pPr marL="1371600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr>  <a:lvl5pPr marL="1828800" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr>  <a:lvl6pPr marL="2286000" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr>  <a:lvl7pPr marL="2743200" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr>  <a:lvl8pPr marL="3200400" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr>  <a:lvl9pPr marL="3657600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1800" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr> </p:otherStyle></p:txStyles></p:sldMaster>')),l.file("ppt/slideMasters/_rels/slideMaster1.xml.rels",(a=this.masterSlide,(o=(o=this.slideLayouts).map(function(t,e){return{target:"../slideLayouts/slideLayout".concat(e+1,".xml"),type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout"}})).push({target:"../theme/theme1.xml",type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme"}),Mt(a,o))),l.file("ppt/notesMasters/notesMaster1.xml",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<p:notesMaster xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:p="http://schemas.openxmlformats.org/presentationml/2006/main"><p:cSld><p:bg><p:bgRef idx="1001"><a:schemeClr val="bg1"/></p:bgRef></p:bg><p:spTree><p:nvGrpSpPr><p:cNvPr id="1" name=""/><p:cNvGrpSpPr/><p:nvPr/></p:nvGrpSpPr><p:grpSpPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="0" cy="0"/><a:chOff x="0" y="0"/><a:chExt cx="0" cy="0"/></a:xfrm></p:grpSpPr><p:sp><p:nvSpPr><p:cNvPr id="2" name="Header Placeholder 1"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="hdr" sz="quarter"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="0" y="0"/><a:ext cx="2971800" cy="458788"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="3" name="Date Placeholder 2"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="dt" idx="1"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="3884613" y="0"/><a:ext cx="2971800" cy="458788"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"/><a:lstStyle><a:lvl1pPr algn="r"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:fld id="{5282F153-3F37-0F45-9E97-73ACFA13230C}" type="datetimeFigureOut"><a:rPr lang="en-US"/><a:t>7/23/19</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="4" name="Slide Image Placeholder 3"/><p:cNvSpPr><a:spLocks noGrp="1" noRot="1" noChangeAspect="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldImg" idx="2"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="1143000"/><a:ext cx="5486400" cy="3086100"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom><a:noFill/><a:ln w="12700"><a:solidFill><a:prstClr val="black"/></a:solidFill></a:ln></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="ctr"/><a:lstStyle/><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="5" name="Notes Placeholder 4"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="body" sz="quarter" idx="3"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="685800" y="4400550"/><a:ext cx="5486400" cy="3600450"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0"/><a:lstStyle/><a:p><a:pPr lvl="0"/><a:r><a:rPr lang="en-US"/><a:t>Click to edit Master text styles</a:t></a:r></a:p><a:p><a:pPr lvl="1"/><a:r><a:rPr lang="en-US"/><a:t>Second level</a:t></a:r></a:p><a:p><a:pPr lvl="2"/><a:r><a:rPr lang="en-US"/><a:t>Third level</a:t></a:r></a:p><a:p><a:pPr lvl="3"/><a:r><a:rPr lang="en-US"/><a:t>Fourth level</a:t></a:r></a:p><a:p><a:pPr lvl="4"/><a:r><a:rPr lang="en-US"/><a:t>Fifth level</a:t></a:r></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="6" name="Footer Placeholder 5"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="ftr" sz="quarter" idx="4"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="0" y="8685213"/><a:ext cx="2971800" cy="458787"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="b"/><a:lstStyle><a:lvl1pPr algn="l"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp><p:sp><p:nvSpPr><p:cNvPr id="7" name="Slide Number Placeholder 6"/><p:cNvSpPr><a:spLocks noGrp="1"/></p:cNvSpPr><p:nvPr><p:ph type="sldNum" sz="quarter" idx="5"/></p:nvPr></p:nvSpPr><p:spPr><a:xfrm><a:off x="3884613" y="8685213"/><a:ext cx="2971800" cy="458787"/></a:xfrm><a:prstGeom prst="rect"><a:avLst/></a:prstGeom></p:spPr><p:txBody><a:bodyPr vert="horz" lIns="91440" tIns="45720" rIns="91440" bIns="45720" rtlCol="0" anchor="b"/><a:lstStyle><a:lvl1pPr algn="r"><a:defRPr sz="1200"/></a:lvl1pPr></a:lstStyle><a:p><a:fld id="{CE5E9CC1-C706-0F49-92D6-E571CC5EEA8F}" type="slidenum"><a:rPr lang="en-US"/><a:t>‹#›</a:t></a:fld><a:endParaRPr lang="en-US"/></a:p></p:txBody></p:sp></p:spTree><p:extLst><p:ext uri="{BB962C8B-B14F-4D97-AF65-F5344CB8AC3E}"><p14:creationId xmlns:p14="http://schemas.microsoft.com/office/powerpoint/2010/main" val="1024086991"/></p:ext></p:extLst></p:cSld><p:clrMap bg1="lt1" tx1="dk1" bg2="lt2" tx2="dk2" accent1="accent1" accent2="accent2" accent3="accent3" accent4="accent4" accent5="accent5" accent6="accent6" hlink="hlink" folHlink="folHlink"/><p:notesStyle><a:lvl1pPr marL="0" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl1pPr><a:lvl2pPr marL="457200" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl2pPr><a:lvl3pPr marL="914400" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl3pPr><a:lvl4pPr marL="1371600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl4pPr><a:lvl5pPr marL="1828800" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl5pPr><a:lvl6pPr marL="2286000" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl6pPr><a:lvl7pPr marL="2743200" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl7pPr><a:lvl8pPr marL="3200400" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl8pPr><a:lvl9pPr marL="3657600" algn="l" defTabSz="914400" rtl="0" eaLnBrk="1" latinLnBrk="0" hangingPunct="1"><a:defRPr sz="1200" kern="1200"><a:solidFill><a:schemeClr val="tx1"/></a:solidFill><a:latin typeface="+mn-lt"/><a:ea typeface="+mn-ea"/><a:cs typeface="+mn-cs"/></a:defRPr></a:lvl9pPr></p:notesStyle></p:notesMaster>')),l.file("ppt/notesMasters/_rels/notesMaster1.xml.rels",'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'.concat(d,'<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">\n\t\t<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="../theme/theme1.xml"/>\n\t\t</Relationships>')),this.slideLayouts.forEach(function(t){s.createChartMediaRels(t,l,A)}),this.slides.forEach(function(t){s.createChartMediaRels(t,l,A)}),this.createChartMediaRels(this.masterSlide,l,A),[4,Promise.all(A).then(function(){return u(s,void 0,void 0,function(){return p(this,function(t){switch(t.label){case 0:return"STREAM"!==c.outputType?[3,2]:[4,l.generateAsync({type:"nodebuffer",compression:c.compression?"DEFLATE":"STORE"})];case 1:return[2,t.sent()];case 2:return c.outputType?[4,l.generateAsync({type:c.outputType})]:[3,4];case 3:return[2,t.sent()];case 4:return[4,l.generateAsync({type:"blob",compression:c.compression?"DEFLATE":"STORE"})];case 5:return[2,t.sent()]}})})})];case 1:return[2,t.sent()]}var n,e,r,a,o,i})})})];case 1:return[2,t.sent()]}})})};this.LAYOUTS={LAYOUT_4x3:{name:"screen4x3",width:9144e3,height:6858e3},LAYOUT_16x9:{name:"screen16x9",width:9144e3,height:5143500},LAYOUT_16x10:{name:"screen16x10",width:9144e3,height:5715e3},LAYOUT_WIDE:{name:"custom",width:12192e3,height:6858e3}},this._author="PptxGenJS",this._company="PptxGenJS",this._revision="1",this._subject="PptxGenJS Presentation",this._title="PptxGenJS Presentation",this._presLayout={name:this.LAYOUTS[l].name,_sizeW:this.LAYOUTS[l].width,_sizeH:this.LAYOUTS[l].height,width:this.LAYOUTS[l].width,height:this.LAYOUTS[l].height},this._rtlMode=!1,this._slideLayouts=[{_margin:at,_name:nt,_presLayout:this._presLayout,_rels:[],_relsChart:[],_relsMedia:[],_slide:null,_slideNum:1e3,_slideNumberProps:null,_slideObjects:[]}],this._slides=[],this._sections=[],this._masterSlide={addChart:null,addImage:null,addMedia:null,addNotes:null,addShape:null,addTable:null,addText:null,_name:null,_presLayout:this._presLayout,_rId:null,_rels:[],_relsChart:[],_relsMedia:[],_slideId:null,_slideLayout:null,_slideNum:null,_slideNumberProps:null,_slideObjects:[]}}return Object.defineProperty(n.prototype,"layout",{get:function(){return this._layout},set:function(t){var e=this.LAYOUTS[t];if(!e)throw new Error("UNKNOWN-LAYOUT");this._layout=t,this._presLayout=e},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"author",{get:function(){return this._author},set:function(t){this._author=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"company",{get:function(){return this._company},set:function(t){this._company=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"revision",{get:function(){return this._revision},set:function(t){this._revision=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"subject",{get:function(){return this._subject},set:function(t){this._subject=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"theme",{get:function(){return this._theme},set:function(t){this._theme=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"title",{get:function(){return this._title},set:function(t){this._title=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"rtlMode",{get:function(){return this._rtlMode},set:function(t){this._rtlMode=t},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"masterSlide",{get:function(){return this._masterSlide},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"slides",{get:function(){return this._slides},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"sections",{get:function(){return this._sections},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"slideLayouts",{get:function(){return this._slideLayouts},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"AlignH",{get:function(){return this._alignH},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"AlignV",{get:function(){return this._alignV},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"ChartType",{get:function(){return this._chartType},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"OutputType",{get:function(){return this._outputType},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"presLayout",{get:function(){return this._presLayout},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"SchemeColor",{get:function(){return this._schemeColor},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"ShapeType",{get:function(){return this._shapeType},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"charts",{get:function(){return this._charts},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"colors",{get:function(){return this._colors},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"shapes",{get:function(){return this._shapes},enumerable:!1,configurable:!0}),n.prototype.stream=function(e){return u(this,void 0,void 0,function(){return p(this,function(t){switch(t.label){case 0:return[4,this.exportPresentation({compression:null==e?void 0:e.compression,outputType:"STREAM"})];case 1:return[2,t.sent()]}})})},n.prototype.write=function(r){return u(this,void 0,void 0,function(){var e,n;return p(this,function(t){switch(t.label){case 0:return e="object"==typeof r&&null!=r&&r.outputType?r.outputType:r||null,n=!("object"!=typeof r||null==r||!r.compression)&&r.compression,[4,this.exportPresentation({compression:n,outputType:e})];case 1:return[2,t.sent()]}})})},n.prototype.writeFile=function(r){return u(this,void 0,void 0,function(){var a,e,n,o,i=this;return p(this,function(t){switch(t.label){case 0:return a="undefined"!=typeof require&&"undefined"==typeof window?require("fs"):null,"string"==typeof r&&console.log("Warning: `writeFile(filename)` is deprecated - please use `WriteFileProps` argument (v3.5.0)"),e="object"==typeof r&&null!=r&&r.fileName?r.fileName:"string"==typeof r?r:"",n=!("object"!=typeof r||null==r||!r.compression)&&r.compression,o=e?e.toString().toLowerCase().endsWith(".pptx")?e:e+".pptx":"Presentation.pptx",[4,this.exportPresentation({compression:n,outputType:a?"nodebuffer":null}).then(function(r){return u(i,void 0,void 0,function(){return p(this,function(t){switch(t.label){case 0:return a?[4,new Promise(function(e,n){a.writeFile(o,r,function(t){t?n(t):e(o)})})]:[3,2];case 1:return[2,t.sent()];case 2:return[4,this.writeFileToBrowser(o,r)];case 3:return[2,t.sent()]}})})})];case 1:return[2,t.sent()]}})})},n.prototype.addSection=function(t){t?t.title||console.warn("addSection requires a title"):console.warn("addSection requires an argument");var e={_type:"user",_slides:[],title:t.title};t.order?this.sections.splice(t.order,0,e):this._sections.push(e)},n.prototype.addSlide=function(e){var n="string"==typeof e?e:null!=e&&e.masterName?e.masterName:"",t={_name:this.LAYOUTS[l].name,_presLayout:this.presLayout,_rels:[],_relsChart:[],_relsMedia:[],_slideNum:this.slides.length+1},r=(n&&(r=this.slideLayouts.filter(function(t){return t._name===n})[0])&&(t=r),new Lt({addSlide:this.addNewSlide,getSlide:this.getSlide,presLayout:this.presLayout,setSlideNum:this.setSlideNumber,slideId:this.slides.length+256,slideRId:this.slides.length+2,slideNumber:this.slides.length+1,slideLayout:t}));return this._slides.push(r),null!=e&&e.sectionTitle?(t=this.sections.filter(function(t){return t.title===e.sectionTitle})[0])?t._slides.push(r):console.warn('addSlide: unable to find section with title: "'.concat(e.sectionTitle,'"')):this.sections&&0<this.sections.length&&(null==e||!e.sectionTitle)&&("default"===(t=this._sections[this.sections.length-1])._type?t._slides.push(r):this._sections.push({title:"Default-".concat(this.sections.filter(function(t){return"default"===t._type}).length+1),_type:"default",_slides:[r]})),r},n.prototype.defineLayout=function(t){t?t.name?t.width?t.height?"number"!=typeof t.height?console.warn("defineLayout `height` should be a number (inches)"):"number"!=typeof t.width&&console.warn("defineLayout `width` should be a number (inches)"):console.warn("defineLayout requires `height`"):console.warn("defineLayout requires `width`"):console.warn("defineLayout requires `name`"):console.warn("defineLayout requires `{name, width, height}`"),this.LAYOUTS[t.name]={name:t.name,_sizeW:Math.round(Number(t.width)*k),_sizeH:Math.round(Number(t.height)*k),width:Math.round(Number(t.width)*k),height:Math.round(Number(t.height)*k)}},n.prototype.defineSlideMaster=function(t){if(!t.title)throw new Error("defineSlideMaster() object argument requires a `title` value. (https://gitbrent.github.io/PptxGenJS/docs/masters.html)");var e,a,n={_margin:t.margin||at,_name:t.title,_presLayout:this.presLayout,_rels:[],_relsChart:[],_relsMedia:[],_slide:null,_slideNum:1e3+this.slideLayouts.length+1,_slideNumberProps:t.slideNumber||null,_slideObjects:[],background:t.background||null,bkgd:t.bkgd||null};a=n,(e=t).bkgd&&(a.bkgd=e.bkgd),e.objects&&Array.isArray(e.objects)&&0<e.objects.length&&e.objects.forEach(function(t,e){var n=Object.keys(t)[0],r=a;o[n]&&"chart"===n?wt(r,t[n].type,t[n].data,t[n].opts):o[n]&&"image"===n?xt(r,t[n]):o[n]&&"line"===n?Ct(r,A.LINE,t[n]):o[n]&&"rect"===n?Ct(r,A.RECTANGLE,t[n]):o[n]&&"text"===n?Pt(r,[{text:t[n].text}],t[n].options,!1):o[n]&&"placeholder"===n&&(t[n].options.placeholder=t[n].options.name,delete t[n].options.name,t[n].options._placeholderType=t[n].options.type,delete t[n].options.type,t[n].options._placeholderIdx=100+e,Pt(r,[{text:t[n].text}],t[n].options,!0))}),e.slideNumber&&"object"==typeof e.slideNumber&&(a._slideNumberProps=e.slideNumber),this.slideLayouts.push(n),(t.background||t.bkgd)&&St(t.background,n),n._slideNumberProps&&!this.masterSlide._slideNumberProps&&(this.masterSlide._slideNumberProps=n._slideNumberProps)},n.prototype.tableToSlides=function(t,e){var a,r=this,o=t,t=e=void 0===e?{}:e,n=null!=e&&e.masterSlideName?this.slideLayouts.filter(function(t){return t._name===e.masterSlideName})[0]:null,i=(t=void 0===t?{}:t)||{},s=(i.slideMargin=i.slideMargin||0===i.slideMargin?i.slideMargin:.5,i.w||r.presLayout.width,[]),A=[],l=[],c=[],u=[],p=[.5,.5,.5,.5],f=0;if(!document.getElementById(o))throw new Error('tableToSlides: Table ID "'+o+'" does not exist!');null!=n&&n._margin?(Array.isArray(n._margin)?p=n._margin:isNaN(n._margin)||(p=[n._margin,n._margin,n._margin,n._margin]),i.slideMargin=p):null!=i&&i.slideMargin&&(Array.isArray(i.slideMargin)?p=i.slideMargin:isNaN(i.slideMargin)||(p=[i.slideMargin,i.slideMargin,i.slideMargin,i.slideMargin])),a=(i.w?I(i.w):r.presLayout.width)-I(p[1]+p[3]),i.verbose&&(console.log("[[VERBOSE MODE]]"),console.log("|-- `tableToSlides` ----------------------------------------------------|"),console.log("| tableProps.h .................................... = ".concat(i.h)),console.log("| tableProps.w .................................... = ".concat(i.w)),console.log("| pptx.presLayout.width ........................... = ".concat((r.presLayout.width/k).toFixed(1))),console.log("| pptx.presLayout.height .......................... = ".concat((r.presLayout.height/k).toFixed(1))),console.log("| emuSlideTabW .................................... = ".concat((a/k).toFixed(1)))),(t=0===(t=document.querySelectorAll("#".concat(o," tr:first-child th"))).length?document.querySelectorAll("#".concat(o," tr:first-child td")):t).forEach(function(t){if(t.getAttribute("colspan"))for(var e=0;e<Number(t.getAttribute("colspan"));e++)u.push(Math.round(t.offsetWidth/Number(t.getAttribute("colspan"))));else u.push(t.offsetWidth)}),u.forEach(function(t){f+=t}),u.forEach(function(t,e){var t=Number((Number(a)*(t/f*100)/100/k).toFixed(2)),n=0,r=document.querySelector("#".concat(o," thead tr:first-child th:nth-child(").concat(e+1,")")),r=(r&&(n=Number(r.getAttribute("data-pptx-min-width"))),document.querySelector("#".concat(o," thead tr:first-child th:nth-child(").concat(e+1,")")));r&&(n=Number(r.getAttribute("data-pptx-width"))),c.push(t<n?n:t)}),i.verbose&&console.log("| arrColW ......................................... = [".concat(c.join(", "),"]")),["thead","tbody","tfoot"].forEach(function(e){document.querySelectorAll("#".concat(o," ").concat(e," tr")).forEach(function(t){var n=[];switch(Array.from(t.cells).forEach(function(r){var t=window.getComputedStyle(r).getPropertyValue("color").replace(/\s+/gi,"").replace("rgba(","").replace("rgb(","").replace(")","").split(","),e=window.getComputedStyle(r).getPropertyValue("background-color").replace(/\s+/gi,"").replace("rgba(","").replace("rgb(","").replace(")","").split(","),a=("rgba(0, 0, 0, 0)"!==window.getComputedStyle(r).getPropertyValue("background-color")&&!window.getComputedStyle(r).getPropertyValue("transparent")||(e=["255","255","255"]),{align:null,bold:!!("bold"===window.getComputedStyle(r).getPropertyValue("font-weight")||500<=Number(window.getComputedStyle(r).getPropertyValue("font-weight"))),border:null,color:mt(Number(t[0]),Number(t[1]),Number(t[2])),fill:{color:mt(Number(e[0]),Number(e[1]),Number(e[2]))},fontFace:(window.getComputedStyle(r).getPropertyValue("font-family")||"").split(",")[0].replace(/"/g,"").replace("inherit","").replace("initial","")||null,fontSize:Number(window.getComputedStyle(r).getPropertyValue("font-size").replace(/[a-z]/gi,"")),margin:null,colspan:Number(r.getAttribute("colspan"))||null,rowspan:Number(r.getAttribute("rowspan"))||null,valign:null});["left","center","right","start","end"].includes(window.getComputedStyle(r).getPropertyValue("text-align"))&&(t=window.getComputedStyle(r).getPropertyValue("text-align").replace("start","left").replace("end","right"),a.align="center"===t?"center":"left"===t?"left":"right"===t?"right":null),["top","middle","bottom"].includes(window.getComputedStyle(r).getPropertyValue("vertical-align"))&&(e=window.getComputedStyle(r).getPropertyValue("vertical-align"),a.valign="top"===e?"top":"middle"===e?"middle":"bottom"===e?"bottom":null),window.getComputedStyle(r).getPropertyValue("padding-left")&&(a.margin=[0,0,0,0],["padding-top","padding-right","padding-bottom","padding-left"].forEach(function(t,e){a.margin[e]=Math.round(Number(window.getComputedStyle(r).getPropertyValue(t).replace(/\D/gi,"")))})),(window.getComputedStyle(r).getPropertyValue("border-top-width")||window.getComputedStyle(r).getPropertyValue("border-right-width")||window.getComputedStyle(r).getPropertyValue("border-bottom-width")||window.getComputedStyle(r).getPropertyValue("border-left-width"))&&(a.border=[null,null,null,null],["top","right","bottom","left"].forEach(function(t,e){var n=Math.round(Number(window.getComputedStyle(r).getPropertyValue("border-"+t+"-width").replace("px",""))),t=window.getComputedStyle(r).getPropertyValue("border-"+t+"-color").replace(/\s+/gi,"").replace("rgba(","").replace("rgb(","").replace(")","").split(","),t=mt(Number(t[0]),Number(t[1]),Number(t[2]));a.border[e]={pt:n,color:t}})),n.push({_type:_.tablecell,text:r.innerText,options:a})}),e){case"thead":s.push(n);break;case"tbody":A.push(n);break;case"tfoot":l.push(n);break;default:console.log("table parsing: unexpected table part: ".concat(e))}})}),i._arrObjTabHeadRows=s||null,i.colW=c,yt(U(U(U([],s,!0),A,!0),l,!0),i,r.presLayout,n).forEach(function(t,e){var n=r.addSlide({masterName:i.masterSlideName||null});0===e&&(i.y=i.y||p[0]),0<e&&(i.y=i.autoPageSlideStartY||i.newSlideStartY||p[0]),i.verbose&&console.log("| opts.autoPageSlideStartY: ".concat(i.autoPageSlideStartY," / arrInchMargins[0]: ").concat(p[0]," => opts.y = ").concat(i.y)),n.addTable(t.rows,{x:i.x||p[3],y:i.y,w:Number(a)/k,colW:c,autoPage:!1}),i.addImage&&(i.addImage.options=i.addImage.options||{},i.addImage.image&&(i.addImage.image.path||i.addImage.image.data)?n.addImage({path:i.addImage.image.path,data:i.addImage.image.data,x:i.addImage.options.x,y:i.addImage.options.y,w:i.addImage.options.w,h:i.addImage.options.h}):console.warn("Warning: tableToSlides.addImage requires either `path` or `data`")),i.addShape&&n.addShape(i.addShape.shapeName,i.addShape.options||{}),i.addTable&&n.addTable(i.addTable.rows,i.addTable.options||{}),i.addText&&n.addText(i.addText.text,i.addText.options||{})})},n}();
//# sourceMappingURL=pptxgen.bundle.js.map

import { sendEmail } from '../services/emailService.js';
import Meeting from '../models/meeting.js';
import Committee from '../models/committee.js';
import Directory from '../models/directory.js';
import mongoose from 'mongoose';
import { format } from 'date-fns';

// Send meeting invitation emails to selected committee members
export const sendMeetingInvitations = async (req, res) => {
  try {
    const { meetingId, recipientIds, emailSubject, emailContent } = req.body;

    if (!meetingId || !recipientIds || !recipientIds.length || !emailSubject || !emailContent) {
      return res.status(400).json({ 
        error: 'Missing required fields: meetingId, recipientIds, emailSubject, and emailContent are required' 
      });
    }

    // Validate meeting ID
    if (!mongoose.Types.ObjectId.isValid(meetingId)) {
      return res.status(400).json({ error: 'Invalid meeting ID' });
    }

    // Find meeting details
    const meeting = await Meeting.findById(meetingId).populate('committee');
    if (!meeting) {
      return res.status(404).json({ error: 'Meeting not found' });
    }

    // Get committee
    const committee = typeof meeting.committee === 'object' ? 
      meeting.committee : 
      await Committee.findById(meeting.committee);

    if (!committee) {
      return res.status(404).json({ error: 'Committee not found' });
    }

    // Find recipients and their emails
    const recipients = await Directory.find({ 
      _id: { $in: recipientIds }
    });

    // Filter out recipients without emails
    const validRecipients = recipients.filter(recipient => recipient.email);
    
    if (validRecipients.length === 0) {
      return res.status(400).json({ error: 'None of the selected recipients have valid email addresses' });
    }

    // Format meeting date
    const formattedDate = format(new Date(meeting.startDate), 'EEEE, MMMM d, yyyy h:mm a');

    // Prepare email content
    const htmlContent = `
      <html>
        <body>
          <h2>Meeting Invitation: ${meeting.title}</h2>
          <p><strong>Committee:</strong> ${committee.name}</p>
          <p><strong>Date & Time:</strong> ${formattedDate}</p>
          <hr />
          <div>${emailContent}</div>
        </body>
      </html>
    `;

    // Send emails
    const emailResult = await sendEmail({
      to: validRecipients.map(r => r.email),
      subject: emailSubject,
      html: htmlContent
    });

    // Track invitations in the meeting document
    const invitationUpdates = validRecipients.map(recipient => ({
      member: recipient._id,
      status: 'invited',
      invitedAt: new Date()
    }));

    // Update meeting with new invitations
    await Meeting.findByIdAndUpdate(
      meetingId,
      {
        $push: {
          invitations: { 
            $each: invitationUpdates 
          }
        }
      }
    );

    // Return success with info about sent emails
    return res.status(200).json({
      success: true,
      message: `Invitations sent to ${validRecipients.length} recipient(s)`,
      sentTo: validRecipients.map(r => ({ 
        id: r._id,
        name: r.name,
        email: r.email
      })),
      notSent: recipients
        .filter(r => !r.email)
        .map(r => ({ 
          id: r._id, 
          name: r.name,
          reason: 'No email address'
        })),
      emailInfo: {
        messageId: emailResult.messageId,
        previewUrl: emailResult.preview ? emailResult.previewUrl : null
      },
      invitationUpdates: invitationUpdates
    });
    
  } catch (error) {
    console.error('Error sending meeting invitations:', error);
    res.status(500).json({ error: error.message });
  }
};

export default {
  sendMeetingInvitations
};

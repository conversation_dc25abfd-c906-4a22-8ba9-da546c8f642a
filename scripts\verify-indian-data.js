import "dotenv/config";
import mongoose from "mongoose";
import Department from "../src/models/department.js";
import Directory from "../src/models/directory.js";
import Committee from "../src/models/committee.js";

async function verifyIndianData() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB successfully!");

    // Get counts
    const departmentCount = await Department.countDocuments();
    const directoryCount = await Directory.countDocuments();
    const committeeCount = await Committee.countDocuments();

    console.log("\n=== DATABASE VERIFICATION ===");
    console.log(`📁 Departments: ${departmentCount}`);
    console.log(`👤 Directory Members: ${directoryCount}`);
    console.log(`📋 Committees: ${committeeCount}`);

    // Show some sample Indian names
    console.log("\n=== SAMPLE INDIAN DIRECTORY MEMBERS ===");
    const sampleMembers = await Directory.find({})
      .populate('department', 'name')
      .limit(10)
      .select('name designation department phone');

    sampleMembers.forEach(member => {
      console.log(`👤 ${member.name} - ${member.designation} (${member.department?.name || 'No Dept'})`);
      console.log(`   📞 ${member.phone || 'No phone'}`);
    });

    // Show Indian committees
    console.log("\n=== INDIAN COMMITTEES ===");
    const committees = await Committee.find({})
      .populate('department', 'name')
      .populate('assistant', 'name designation')
      .select('name department assistant meetingFrequency');

    committees.forEach(committee => {
      console.log(`📋 ${committee.name}`);
      console.log(`   🏢 ${committee.department?.name || 'No Dept'} (${committee.meetingFrequency})`);
      console.log(`   👨‍💼 Assistant: ${committee.assistant?.name || 'None'} (${committee.assistant?.designation || 'N/A'})`);
    });

    console.log("\n✅ Verification complete!");

  } catch (error) {
    console.error("Error verifying data:", error);
  } finally {
    await mongoose.connection.close();
    console.log("Database connection closed.");
    process.exit(0);
  }
}

verifyIndianData();

{"name": "@editorjs/editorjs", "version": "2.30.8", "description": "Editor.js — open source block-style WYSIWYG editor with JSON output", "main": "dist/editorjs.umd.js", "module": "dist/editorjs.mjs", "types": "./types/index.d.ts", "keywords": ["codex editor", "text editor", "editor", "editor.js", "editorjs", "wysiwyg"], "scripts": {"dev": "vite", "build": "vite build --mode production", "build:test": "vite build --mode test", "lint": "eslint src/ --ext .ts && yarn lint:tests", "lint:errors": "eslint src/ --ext .ts --quiet", "lint:fix": "eslint src/ --ext .ts --fix", "lint:tests": "eslint test/ --ext .ts", "pull_tools": "git submodule update --init --recursive", "_tools:checkout": "git submodule foreach \"git checkout master || git checkout main\"", "_tools:pull": "git submodule foreach git pull", "_tools:yarn": "git submodule foreach yarn", "_tools:build": "git submodule foreach yarn build", "_tools:make": "yarn _tools:yarn && yarn _tools:build", "tools:update": "yarn _tools:checkout && yarn _tools:pull && yarn _tools:make", "test:e2e": "yarn build:test && cypress run", "test:e2e:open": "yarn build:test && cypress open", "devserver:start": "yarn build && node ./devserver.js"}, "author": "CodeX", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/codex-team/editor.js.git"}, "devDependencies": {"@babel/register": "^7.21.0", "@codexteam/icons": "0.3.2", "@codexteam/shortcuts": "^1.1.1", "@cypress/code-coverage": "^3.10.3", "@editorjs/code": "^2.7.0", "@editorjs/delimiter": "^1.2.0", "@editorjs/header": "^2.8.7", "@editorjs/paragraph": "^2.11.6", "@editorjs/simple-image": "^1.4.1", "@types/node": "^18.15.11", "chai-subset": "^1.6.0", "codex-notifier": "^1.1.2", "codex-tooltip": "^1.0.5", "core-js": "3.30.0", "cypress": "^13.13.3", "cypress-intellij-reporter": "^0.0.7", "cypress-plugin-tab": "^1.0.5", "cypress-terminal-report": "^5.3.2", "cypress-vite": "^1.5.0", "eslint": "^8.37.0", "eslint-config-codex": "^1.7.1", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-cypress": "2.12.1", "html-janitor": "^2.0.4", "nanoid": "^4.0.2", "postcss-apply": "^0.12.0", "postcss-nested": "4.1.2", "postcss-preset-env": "^8.3.0", "rollup-plugin-license": "^3.0.1", "stylelint": "^15.4.0", "tslint": "^6.1.1", "typescript": "5.0.3", "vite": "^4.2.1", "vite-plugin-css-injected-by-js": "^3.1.0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/editorjs"}}
import express from "express";
import auth from "../middleware/auth.js";
import {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment
} from "../controllers/departmentController.js";

const router = express.Router();

// Get all departments with pagination
router.get("/", auth, getAllDepartments);

// Get department by ID
router.get("/:id", auth, getDepartmentById);

// Create new department
router.post("/", auth, createDepartment);

// Update department
router.put("/:id", auth, updateDepartment);

// Delete department
router.delete("/:id", auth, deleteDepartment);

export default router;

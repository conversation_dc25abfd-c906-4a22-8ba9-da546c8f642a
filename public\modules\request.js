export const baseURL = window.location.origin;

export async function saveToJson(requestId, content) {
    try {
        const url = new URL(`/editor/api/requests/${requestId}/save-slides`, baseURL);
        const response = await fetch(url.toString(), {
            credentials: "include",
            method: "PATCH",
            body: JSON.stringify(content),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        console.log(response);
    } catch (error) {
        console.error(error);
        throw new Error(error?.message ?? error);
    }
}

export async function getContent(id) {
    const url = new URL(`/editor/api/requests/${id}/slides`, baseURL);
    try {
        const res = await fetch(url, {
            credentials: "include",
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        if (!res.ok && res.status !== 304) {
            throw new Error(`Failed to fetch content: ${res.status} ${res.statusText}`);
        }
        
        // If we got a 304, return the cached data or an empty array as fallback
        if (res.status === 304) {
            console.log("Got 304 Not Modified, using cached data");
            return window._cachedEditorData || [];
        }
        
        const data = await res.json();
        // Cache the data for potential 304 responses later
        window._cachedEditorData = data;
        return data;
    } catch (error) {
        console.error("Error fetching content:", error);
        // Return empty array as fallback to prevent crash
        return [];
    }
}
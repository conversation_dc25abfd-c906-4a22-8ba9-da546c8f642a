import Scheme from "../models/scheme.js";

export const getSchemes = async (req, res) => {
    try{
        const { 
          page = 1, 
          limit = 10, 
          search = '', 
          sortBy = 'createdAt', 
          sortOrder = 'desc' 
        } = req.query;

        const searchFilter = search ? {
          $or: [
              { name: { $regex: search, $options: 'i' } },
              { description: { $regex: search, $options: 'i' } }
            ]
        } : {};

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        const total = await Scheme.countDocuments(searchFilter);

        const data = await Scheme.find(searchFilter)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit));

        res.json({
          data,
          pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
          }
        });
    } catch(error) {
        res.status(400).json({ error: error.message });
    }
}

export const createScheme = async (req, res) => {
  try{
    console.log(req.body);
    const scheme = new Scheme({...req.body, createdBy: req.user._id})
    const saved = await scheme.save();
    res.status(201).json(saved);
  } catch(error){
    res.status(400).json({ error: error.message });
  }
}

export const deleteScheme = async (req, res) => {
  try {
    const schemeId = req.parmas.id;
    await Scheme.findByIdAndDelete(schemeId);
    res.json({success: true});
  } catch (error) {
    res.status(401).json({error: error.message});
  }

}

export const updateScheme = async(req, res) => {
  try {
    const updated = await Scheme.findByIdAndUpdate(req.parmas.id, req.body);
    res.json(updated);
  } catch (error) {
    res.status(401).json({error: error.message});
  }
}
import "dotenv/config";
import mongoose from "mongoose";
import Department from "../src/models/department.js";
import Directory from "../src/models/directory.js";
import User from "../src/models/user.js";
import Committee from "../src/models/committee.js";
import bcrypt from "bcryptjs";

// Sample Indian committees based on common government/organizational structures
const sampleCommittees = [
  {
    name: "Digital India Implementation Committee",
    description: "Oversees the implementation of digital transformation initiatives and technology adoption across all departments",
    meetingFrequency: "monthly",
    departmentCode: "IT",
    memberRoles: ["CTO", "IT Manager", "Senior Developer", "DevOps Engineer", "Operations Director"],
    startDate: new Date("2025-01-15")
  },
  {
    name: "Staff Welfare and Development Committee",
    description: "Addresses employee welfare, professional development, and workplace satisfaction initiatives",
    meetingFrequency: "monthly",
    departmentCode: "HR",
    memberRoles: ["HR Director", "HR Manager", "Finance Manager", "Operations Director", "Legal Counsel"],
    startDate: new Date("2025-01-20")
  },
  {
    name: "Financial Review and Audit Committee",
    description: "Reviews financial performance, budget allocations, and ensures compliance with financial regulations",
    meetingFrequency: "quarterly",
    departmentCode: "FIN",
    memberRoles: ["CFO", "Finance Manager", "Senior Accountant", "QA Director", "General Counsel"],
    startDate: new Date("2025-02-01")
  },
  {
    name: "Public Relations and Communication Committee",
    description: "Manages external communications, public relations strategies, and stakeholder engagement",
    meetingFrequency: "biweekly",
    departmentCode: "MKT",
    memberRoles: ["Marketing Director", "Digital Marketing Manager", "Content Specialist", "Customer Success Director"],
    startDate: new Date("2025-01-25")
  },
  {
    name: "Quality Assurance and Standards Committee",
    description: "Ensures quality standards, monitors compliance, and implements continuous improvement processes",
    meetingFrequency: "monthly",
    departmentCode: "QA",
    memberRoles: ["QA Director", "QA Manager", "QA Analyst", "Operations Director", "R&D Director"],
    startDate: new Date("2025-02-05")
  },
  {
    name: "Research and Innovation Committee",
    description: "Drives research initiatives, innovation projects, and technology advancement strategies",
    meetingFrequency: "monthly",
    departmentCode: "RND",
    memberRoles: ["R&D Director", "Senior Research Scientist", "Product Manager", "Innovation Lead"],
    startDate: new Date("2025-02-10")
  },
  {
    name: "Infrastructure and Operations Committee",
    description: "Oversees infrastructure development, operational efficiency, and resource management",
    meetingFrequency: "monthly",
    departmentCode: "OPS",
    memberRoles: ["Operations Director", "Infrastructure Manager", "Facilities Manager", "IT Manager"],
    startDate: new Date("2025-02-15")
  },
  {
    name: "Legal and Compliance Committee",
    description: "Ensures legal compliance, manages contracts, and oversees regulatory requirements",
    meetingFrequency: "quarterly",
    departmentCode: "LEG",
    memberRoles: ["General Counsel", "Legal Counsel", "Compliance Officer", "HR Director"],
    startDate: new Date("2025-03-01")
  }
];

// Sample Indian directory members with appropriate names and roles
const sampleDirectoryMembers = [
  {
    name: "Dr. Rajesh Kumar",
    designation: "CTO",
    departmentCode: "IT",
    email: "<EMAIL>",
    phone: "+91-9876543210",
    isActive: true
  },
  {
    name: "Ms. Priya Sharma",
    designation: "HR Director",
    departmentCode: "HR",
    email: "<EMAIL>",
    phone: "+91-9876543211",
    isActive: true
  },
  {
    name: "Mr. Vikram Singh",
    designation: "CFO",
    departmentCode: "FIN",
    email: "<EMAIL>",
    phone: "+91-9876543212",
    isActive: true
  },
  {
    name: "Ms. Anita Desai",
    designation: "Marketing Director",
    departmentCode: "MKT",
    email: "<EMAIL>",
    phone: "+91-9876543213",
    isActive: true
  },
  {
    name: "Dr. Suresh Patel",
    designation: "QA Director",
    departmentCode: "QA",
    email: "<EMAIL>",
    phone: "+91-9876543214",
    isActive: true
  },
  {
    name: "Ms. Kavita Reddy",
    designation: "R&D Director",
    departmentCode: "RND",
    email: "<EMAIL>",
    phone: "+91-9876543215",
    isActive: true
  },
  {
    name: "Mr. Arjun Mehta",
    designation: "Operations Director",
    departmentCode: "OPS",
    email: "<EMAIL>",
    phone: "+91-9876543216",
    isActive: true
  },
  {
    name: "Ms. Deepika Gupta",
    designation: "General Counsel",
    departmentCode: "LEG",
    email: "<EMAIL>",
    phone: "+91-9876543217",
    isActive: true
  },
  {
    name: "Mr. Ravi Krishnan",
    designation: "IT Manager",
    departmentCode: "IT",
    email: "<EMAIL>",
    phone: "+91-9876543218",
    isActive: true
  },
  {
    name: "Ms. Shreya Joshi",
    designation: "HR Manager",
    departmentCode: "HR",
    email: "<EMAIL>",
    phone: "+91-9876543219",
    isActive: true
  },
  {
    name: "Mr. Amit Agarwal",
    designation: "Finance Manager",
    departmentCode: "FIN",
    email: "<EMAIL>",
    phone: "+91-9876543220",
    isActive: true
  },
  {
    name: "Ms. Nisha Malhotra",
    designation: "Digital Marketing Manager",
    departmentCode: "MKT",
    email: "<EMAIL>",
    phone: "+91-9876543221",
    isActive: true
  },
  {
    name: "Dr. Manish Verma",
    designation: "QA Manager",
    departmentCode: "QA",
    email: "<EMAIL>",
    phone: "+91-9876543222",
    isActive: true
  },
  {
    name: "Mr. Karthik Nair",
    designation: "Senior Research Scientist",
    departmentCode: "RND",
    email: "<EMAIL>",
    phone: "+91-9876543223",
    isActive: true
  },
  {
    name: "Ms. Pooja Iyer",
    designation: "Infrastructure Manager",
    departmentCode: "OPS",
    email: "<EMAIL>",
    phone: "+91-9876543224",
    isActive: true
  },
  {
    name: "Mr. Rohit Bansal",
    designation: "Legal Counsel",
    departmentCode: "LEG",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Ms. Sunita Rao",
    designation: "Senior Developer",
    departmentCode: "IT",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Mr. Sanjay Khanna",
    designation: "DevOps Engineer",
    departmentCode: "IT",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Ms. Rashmi Tiwari",
    designation: "Senior Accountant",
    departmentCode: "FIN",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Mr. Nikhil Saxena",
    designation: "Content Specialist",
    departmentCode: "MKT",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Ms. Meera Pillai",
    designation: "Customer Success Director",
    departmentCode: "MKT",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Dr. Ashok Mishra",
    designation: "QA Analyst",
    departmentCode: "QA",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Mr. Sachin Yadav",
    designation: "Product Manager",
    departmentCode: "RND",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Ms. Ritu Kapoor",
    designation: "Innovation Lead",
    departmentCode: "RND",
    email: "<EMAIL>",
    phone: "+91-**********",
    isActive: true
  },
  {
    name: "Mr. Ajay Choudhary",
    designation: "Facilities Manager",
    departmentCode: "OPS",
    email: "<EMAIL>",
    phone: "+91-9876543234",
    isActive: true
  },
  {
    name: "Ms. Swati Pandey",
    designation: "Compliance Officer",
    departmentCode: "LEG",
    email: "<EMAIL>",
    phone: "+91-9876543235",
    isActive: true
  }
];

// Use existing users instead of creating additional ones
const existingUsers = [
  {
    email: "<EMAIL>",
    name: "Administrator",
    role: 1 // Admin role
  },
  {
    email: "<EMAIL>",
    name: "Assistant User",
    role: 2 // Assistant role
  }
];

async function seedIndianCommitteesWithRealUsers() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB successfully!");

    // Make sure the two users exist and have proper names and roles
    console.log("Ensuring required users exist...");
    
    for (const userData of existingUsers) {
      let user = await User.findOne({ email: userData.email });
      
      if (!user) {
        // Create the user if it doesn't exist
        console.log(`Creating user: ${userData.name} (${userData.email})`);
        const hashedPassword = await bcrypt.hash("password123", 10);
        user = await User.create({
          ...userData,
          password: hashedPassword
        });
      } else if (!user.name || user.role !== userData.role) {
        // Update user if name is missing or role is incorrect
        user.name = userData.name;
        user.role = userData.role;
        await user.save();
        console.log(`Updated user: ${userData.name} (${userData.email})`);
      }
    }

    // Get only the two specified users
    const realUsers = await User.find({ 
      email: { $in: ["<EMAIL>", "<EMAIL>"] } 
    });
    
    console.log(`Found ${realUsers.length} real users for attribution: ${realUsers.map(u => u.name).join(', ')}`);
    
    if (realUsers.length === 0) {
      console.error("No users found! Please check if users exist.");
      process.exit(1);
    }

    // Clear existing committees and directory members
    console.log("Clearing existing committees and directory data...");
    await Committee.deleteMany({});
    await Directory.deleteMany({});
    console.log("Existing committees and directory data cleared!");

    // Get existing departments
    const departments = await Department.find({});
    console.log(`Found ${departments.length} departments`);

    if (departments.length === 0) {
      console.log("No departments found. Please run the sample data seeding script first.");
      console.log("Run: npm run seed-sample-data");
      process.exit(1);
    }

    // Create department code mapping
    const departmentMap = {};
    departments.forEach(dept => {
      departmentMap[dept.code] = dept._id;
    });

    // Helper function to get a random real user
    const getRandomUser = () => {
      return realUsers[Math.floor(Math.random() * realUsers.length)];
    };

    // Create directory members with real user attribution
    console.log("Creating directory members...");
    const directoryMembers = [];
    
    for (const memberData of sampleDirectoryMembers) {
      if (departmentMap[memberData.departmentCode]) {
        const createdBy = getRandomUser();
        const member = await Directory.create({
          ...memberData,
          department: departmentMap[memberData.departmentCode],
          createdBy: createdBy._id,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
        });
        directoryMembers.push(member);
        console.log(`Created directory member: ${member.name} (Created by: ${createdBy.name})`);
      }
    }

    console.log(`Created ${directoryMembers.length} directory members`);

    // Create committees with real user attribution
    console.log("Creating committees...");
    let committeesCreated = 0;

    for (const committeeData of sampleCommittees) {
      if (departmentMap[committeeData.departmentCode]) {
        // Get members for this committee
        const committeeDepartment = departments.find(d => d.code === committeeData.departmentCode);
        const availableMembers = directoryMembers.filter(
          member => member.department.toString() === departmentMap[committeeData.departmentCode].toString()
        );

        if (availableMembers.length === 0) {
          console.log(`No members available for department ${committeeData.departmentCode}, skipping committee: ${committeeData.name}`);
          continue;
        }

        // Select random members for committee
        const shuffledMembers = [...availableMembers].sort(() => 0.5 - Math.random());
        const selectedMembers = shuffledMembers.slice(0, Math.min(3, shuffledMembers.length));
        
        // Get member secretary (first member)
        const memberSecretary = selectedMembers[0];

        const createdBy = getRandomUser();
        const committee = await Committee.create({
          name: committeeData.name,
          description: committeeData.description,
          department: departmentMap[committeeData.departmentCode],
          memberSecretary: memberSecretary._id,
          members: selectedMembers.map(m => m._id),
          meetingFrequency: committeeData.meetingFrequency,
          status: "active",
          // startDate will be auto-generated based on meeting frequency
          createdBy: createdBy._id,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
        });

        committeesCreated++;
        console.log(`Created committee: ${committee.name} (Department: ${committeeDepartment.name}, Members: ${selectedMembers.length}, Created by: ${createdBy.name})`);
      } else {
        console.log(`Department ${committeeData.departmentCode} not found, skipping committee: ${committeeData.name}`);
      }
    }

    console.log(`\n✅ Seeding completed successfully!`);
    console.log(`📊 Summary:`);
    console.log(`   - Real users available: ${realUsers.length}`);
    console.log(`   - Directory members created: ${directoryMembers.length}`);
    console.log(`   - Committees created: ${committeesCreated}`);
    console.log(`   - All data now has proper user attribution!`);
    
    console.log(`\n🔗 User Attribution:`);
    realUsers.forEach(user => {
      console.log(`   - ${user.name} (${user.email})`);
    });

  } catch (error) {
    console.error("❌ Error seeding Indian committees with real users:", error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log("Database connection closed.");
  }
}

// Run the seeding function
seedIndianCommitteesWithRealUsers();

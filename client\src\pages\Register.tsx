import { useState, FormEvent } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import {
  TextInput,
  PasswordInput,
  Paper,
  Title,

  Button,
  Text,
  Stack,
  MantineTheme,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { IconX, IconCheck } from "@tabler/icons-react";

export default function Register() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { register } = useAuth();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      notifications.show({
        title: "Error",
        message: "Passwords do not match",
        color: "red",
        icon: <IconX />,
      });
      return;
    }

    try {
      setLoading(true);
      await register(email, password);
      notifications.show({
        title: "Success",
        message: "Account created successfully! Welcome aboard!",
        color: "green",
        icon: <IconCheck />,
      });
      navigate("/");
    } catch (error: any) {
      notifications.show({
        title: "Error",
        message: error.response?.data?.error || "Failed to create an account",
        color: "red",
        icon: <IconX />,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: "100vh",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: "40px 20px"
    }}>
      <div style={{
        width: '100%',
        maxWidth: '800px'
      }}>
        <Title
          ta="center"
          style={(theme: MantineTheme) => ({
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          })}
          fw={900}
        >
          Create an account
        </Title>
        <Text c="dimmed" size="sm" ta="center" mt={5}>
          Already have an account?{" "}
          <Link to="/login" style={{ color: "inherit" }}>
            Sign in
          </Link>
        </Text>

        <Paper withBorder shadow="md" p={60} mt={30} radius="md">
          <form onSubmit={handleSubmit}>
            <Stack>
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <PasswordInput
                label="Password"
                placeholder="Your password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <PasswordInput
                label="Confirm Password"
                placeholder="Confirm your password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
              <Button type="submit" loading={loading}>
                Register
              </Button>
            </Stack>
          </form>
        </Paper>
      </div>
    </div>
  );
}

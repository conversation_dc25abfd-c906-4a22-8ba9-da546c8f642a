import { apiClient } from './axios';

// Analysis server configuration - default from .env
let ANALYSIS_SERVER_URL = import.meta.env.VITE_ANALYSIS_SERVER_URL || 'https://analysis-server.example.com';

// Function to load configuration from the server
export const loadServerConfig = async () => {
  try {
    const response = await apiClient.get('/api/config');
    if (response.data && response.data.analysisServerUrl) {
      ANALYSIS_SERVER_URL = response.data.analysisServerUrl;
      console.log('Loaded analysis server URL from server config:', ANALYSIS_SERVER_URL);
    }
  } catch (error) {
    console.error('Failed to load server configuration:', error);
    // Keep using the default from .env
  }
};

export { ANALYSIS_SERVER_URL };

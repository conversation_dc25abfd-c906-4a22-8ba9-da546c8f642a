/**
 * Describes styles API
 */
export interface Styles {
  /**
   * Main Editor`s block styles
   */
  block: string;

  /**
   * Styles for Inline Toolbar button
   */
  inlineToolButton: string;

  /**
   * Styles for active Inline Toolbar button
   */
  inlineToolButtonActive: string;

  /**
   * Styles for inputs
   */
  input: string;

  /**
   * Loader styles
   */
  loader: string;

  /**
   * Styles for Settings box buttons
   */
  settingsButton: string;

  /**
   * Styles for active Settings box buttons
   */
  settingsButtonActive: string;

  /**
   * Styles for buttons
   */
  button: string;
}

<!--
 Use this page for debugging purposes.

 Editor Tools are loaded as git-submodules.
 You can pull modules by running `yarn pull_tools` and start experimenting.
 -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Editor.js 🤩🧦🤨 example</title>
  <link href="https://fonts.googleapis.com/css?family=PT+Mono" rel="stylesheet">
  <link href="assets/demo.css" rel="stylesheet">
  <script src="assets/json-preview.js"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
</head>
<body>
  <div class="ce-example">
    <div class="ce-example__header">
      <a class="ce-example__header-logo" href="https://codex.so/editor">Editor.js 🤩🧦🤨</a>

      <div class="ce-example__header-menu">
        <a href="https://github.com/editor-js" target="_blank">Plugins</a>
        <a href="https://editorjs.io/usage" target="_blank">Usage</a>
        <a href="https://editorjs.io/configuration" target="_blank">Configuration</a>
        <a href="https://editorjs.io/creating-a-block-tool" target="_blank">API</a>
      </div>
    </div>
    <div class="ce-example__content _ce-example__content--small">
      <div id="editorjs"></div>
      <div id="hint" style="text-align: center;">
        No submodules found. Run <code class="inline-code">yarn pull_tools</code>
      </div>
      <div class="ce-example__button" id="saveButton">
        editor.save()
      </div>
    </div>
    <div class="ce-example__output">
      <pre class="ce-example__output-content" id="output"></pre>

      <div class="ce-example__output-footer">
        <a href="https://codex.so" style="font-weight: bold;">Made by CodeX</a>
      </div>
    </div>
  </div>

  <!-- Load Tools -->
  <!--
   You can upload Tools to your project's directory and use as in example below.

   Also you can load each Tool from CDN or use NPM/Yarn packages.

   Read more in Tool's README file. For example:
   https://github.com/editor-js/header#installation
   -->
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest/dist/editor.min.js"></script>
  <script src="../dist/bundle.js" onload="document.getElementById('hint').hidden = true"></script><!-- Header -->
  <script src="./tools/paragraph/dist/bundle.js" onload="document.getElementById('hint').hidden = true"></script><!-- Header -->
  <script src="./tools/delimiter/dist/bundle.js" onload="document.getElementById('hint').hidden = true"></script><!-- Header -->
  <script src="./tools/header/dist/bundle.js" onload="document.getElementById('hint').hidden = true"></script><!-- Header -->
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/list@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/checklist@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/raw"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/embed@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/quote@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/table@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/code@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/editorjs-header-with-anchor@latest"></script>

  <!-- Initialization -->
  <script>
    const saveButton = document.getElementById('saveButton');

    /**
     * To initialize the Editor, create a new instance with configuration object
     * @see docs/installation.md for mode details
     */
    var editor = new EditorJS({
      /**
       * Wrapper of Editor
       */
      holder: 'editorjs',

      /**
       * Tools list
       */
      tools: {
        embed: {
          class: Embed,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        quote: {
          class: Quote,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        table: {
          class: Table,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        code: {
          class: CodeTool,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        raw: {
          class: RawTool,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        checklist: {
          class: Checklist,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        list: {
          class: List,
          inlineToolbar: true,
          tunes: ['alignmentSetting'],
        },
        header: {
          class: Header,
          tunes: ['alignmentSetting'],
        },
        delimiter: Delimiter,
        paragraph: {
          class: Paragraph,
          inlineToolbar: false,
          tunes: ['alignmentSetting'],
          config:{
            css:{
              "btnColor": "btn--gray",
            }
          }
        },
        alignmentSetting: {
          class:AlignmentBlockTune,
          config:{
            default: "center",
            blocks: {
              header: 'center',
              list: 'right'
            }
          },
        }
      },
      // tunes: ['alignmentBlockTune'],
      /**
       * Previously saved data that should be rendered
       */
      // data: {"time":1597151733527,"blocks":[{"type":"paragraph","data":{"text":"right","alignment":"right"}},{"type":"paragraph","data":{"text":"leeeeeeeeeeeeeeft","alignment":"left"}},{"type":"paragraph","data":{"text":"centerrrrrrrrr!","alignment":"center"}},{"type" : "header","data" : {"text" : "header","level" : 2}}],"version":"2.18.0"},
      // data: { "time" : 1617616076673, "blocks" : [ { "type" : "paragraph", "data" : { "text" : "center" }, "tunes" : { "hehe" : { "alignment" : "center" } } }, { "type" : "header", "data" : { "text" : "header", "level" : 2 } } ], "version" : "2.20.0" },
      // data: { "time" : 1617670890577, "blocks" : [ { "type" : "paragraph", "data" : { "text" : "center" }, "tunes" : { "hehe" : { "alignment" : "center" } } }, { "type" : "paragraph", "data" : { "text" : "riiiiiiiiiight" } }, { "type" : "paragraph", "data" : { "text" : "hellooooooooooooooooo" } }, { "type" : "header", "data" : { "text" : "header", "level" : 2 } } ], "version" : "2.20.0" },

      //標準で何も設定していない
      //data:{ "time" : 1617693971188, "blocks" : [ { "type" : "paragraph", "data" : { "text" : "center" }, "tunes" : { "hehe" : { "alignment" : "center" } } }, { "type" : "paragraph", "data" : { "text" : "riiiiiiiiiight" } }, { "type" : "paragraph", "data" : { "text" : "hellooooooooooooooooo" } }, { "type" : "header", "data" : { "text" : "header", "level" : 2 } }, { "type" : "quote", "data" : { "text" : "quote text", "caption" : "caption", "alignment" : "left" } }, { "type" : "table", "data" : { "content" : [ [ "aaaaaaaa", "bbbbbbbbbb", "cccccccc" ], [ "111111111", "2222222222", "ccccccccc" ] ] } }, { "type" : "code", "data" : { "code" : "paragraph: {\n          class: Paragraph,\n          inlineToolbar: false,\n          tunes: ['alignmentSetting'],\n          config:{\n            css:{\n              \"btnColor\": \"btn--gray\",\n            }\n          }\n        }," } }, { "type" : "raw", "data" : { "html" : "paragraph: {\n          class: Paragraph,\n          inlineToolbar: false,\n          tunes: ['alignmentSetting'],\n          config:{\n            css:{\n              \"btnColor\": \"btn--gray\",\n            }\n          }\n        }," } }, { "type" : "paragraph", "data" : { "text" : "paragrapg" } }, { "type" : "checklist", "data" : { "items" : [ { "text" : "check1", "checked" : false }, { "text" : "check2", "checked" : false }, { "text" : "check3", "checked" : false } ] } }, { "type" : "delimiter", "data" : {} }, { "type" : "paragraph", "data" : { "text" : "end" } } ], "version" : "2.20.0" },
      data:{ "time" : 1617715359615, "blocks" : [ { "type" : "checklist", "data" : { "items" : [ { "text" : "check1", "checked" : false }, { "text" : "check2", "checked" : false }, { "text" : "check3", "checked" : false } ] }, "tunes" : { "alignmentSetting" : { "alignment" : "center" } } }, { "type" : "header", "data" : { "text" : "Header", "level" : 2 }, "tunes" : { "alignmentSetting" : { "alignment" : "left" } } } ], "version" : "2.20.0" },
      /**
       * This Tool will be used as default
       */
      // initialBlock: 'paragraph',

      /**
       * Initial Editor data
       */

      onReady: function(){
        saveButton.click();
      },
      onChange: function() {
        console.log('something changed');
      },
    });

    editor.isReady.then(() => {

    })

    /**
     * Saving example
     */
    saveButton.addEventListener('click', function () {
      editor.save().then((savedData) => {
        cPreview.show(savedData, document.getElementById("output"));
      });
    });
  </script>
</body>
</html>

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/user.js';

dotenv.config();

const createDefaultUser = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const defaultUser = {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Administrator',
      role: 1 // Deputy Commissioner
    };

    // Check if default user already exists
    const existingUser = await User.findOne({ email: defaultUser.email });
    if (existingUser) {
      console.log('Default user already exists');
      await mongoose.disconnect();
      return;
    }

    // Create new default user
    const user = new User(defaultUser);
    await user.save();
    
    console.log('Default user created successfully:');
    console.log('Email:', defaultUser.email);
    console.log('Password:', defaultUser.password);

    await mongoose.disconnect();
  } catch (error) {
    console.error('Error creating default user:', error);
  }
};

createDefaultUser();

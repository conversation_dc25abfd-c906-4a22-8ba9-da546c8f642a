export interface User {
  id?: string;
  email: string;
  token?: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => void;
}

export interface TaskResponse {
  _id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  meetingId: string;
  assignedTo?: Array<{ _id: string; name: string }>;
  createdAt?: string;
  updatedAt?: string;
  deadline?: string | null;
}

export interface Task {
  _id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  meeting: string;
  committeeId: string;
  meetingTitle: string;
  committeeName: string;
  assignedTo?: Array<{ _id: string; name: string }>;
  createdAt?: string;
  updatedAt?: string;
  deadline?: string | null;
}

export interface SuggestedTask {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  deadline?: string | null;
}

export interface ImageUploadResponse {
  imageUrl: string;
  extractedText: string;
  suggestedTasks: SuggestedTask[];
}

export interface SuggestedAgendaItem {
  title: string;
  description: string;
  order?: number;
  slideImageIndex?: number;
}

export interface AgendaImageUploadResponse {
  extractedText: string;
  suggestedAgendaItems: SuggestedAgendaItem[];
  slideImages: string[];
}

export interface Directory {
  _id: string;
  name: string;
  designation: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  department?: Department;
  createdBy: string | { _id: string; name: string; email: string; role: number };
  createdAt: string;
}

export interface Department {
  _id: string;
  name: string;
  description?: string;
  code?: string;
  createdBy?: string | { _id: string; name: string; email: string; role: number };
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Committee {
  _id: string;
  name: string;
  description?: string;
  department?: Department | string; // Can be either populated Department or just ID
  members: Array<{ _id: string; name: string }>;
  assistant?: { _id: string; name: string };
  meetingFrequency: string;
  lastMeetingDate?: string; // Optional, can be null if no meetings have occurred
  startDate?: string; // Now optional as it will be auto-generated based on meeting frequency
  createdBy?: { _id: string; name: string; email: string; role: number };
  createdAt: string;
  updatedAt: string;
}

export interface MeetingInvitation {
  _id: string;
  member: string | { _id: string; name: string; email?: string };
  status: 'invited' | 'confirmed' | 'declined';
  invitedAt: string;
  responseAt?: string;
}

export interface Meeting {
  _id: string;
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  status?: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  frequency?: string; // Added frequency property
  location?: string; // Added location property
  committee: string | Committee; // Can be populated or just ID
  committeeId?: string; // For backward compatibility
  tasks?: Task[];
  taskCount?: number;
  attendees: Array<{ _id: string; name: string }>;
  invitations?: MeetingInvitation[];
  documents?: Array<{
    _id: string;
    filename: string;
    originalname: string;
    path: string;
    mimetype: string;
    size: number;
    uploadedAt: string;
    isAttendanceSheet?: boolean;
  }>;
  createdBy?: { _id: string; name: string; email: string; role: number };
  createdAt: string;
  updatedAt: string;
}


export interface AgendaFile {
  _id: string;
  filename: string;
  originalname: string;
  path: string;
  mimetype: string;
  size: number;
  uploadedAt: string;
}

export interface AgendaItem {
  _id: string;
  title: string;
  description?: string;
  meeting: string;
  order: number; // Keeping order for list positioning
  files?: AgendaFile[];
  createdBy?: any;
  createdAt: string;
  updatedAt: string;
  analysis?: {
    token: string | null; // Token provided by external analysis server
    status: number;      // Status code: 0 = not analyzed, 1 = requested, 2 = in progress, 3 = completed, etc.
    time: string | null; // Timestamp of the analysis request or completion
  };
}

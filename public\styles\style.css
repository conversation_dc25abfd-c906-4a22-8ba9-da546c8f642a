html {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Basic layout */
body {
  margin: 0;
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--sl-color-neutral-200);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
}

/* Main container */
.app-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 250px;
  border-right: 1px solid var(--sl-color-neutral-200);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
}

.page-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

/* Editor container */
#editor-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Page styling */
.page {
  background: white;
  border: 1px solid var(--sl-color-neutral-200);
  border-radius: 0.25rem;
  padding: 2rem;
  margin: 0 auto;
  max-width: 800px;
}

/* Toast container */
#toast-container {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 1000;
}

/* Fix for EditorJS toolbar */
.ce-toolbar__actions--opened {
  z-index: 2;
}
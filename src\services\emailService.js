import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

// Create a transporter using environment variables or default test account
let transporter;

const createTransporter = async () => {
  // Check if we have email settings in environment variables
  if (process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
    return nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT || 587,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
  }
  
  // If no email settings, create a test account using Ethereal
  console.log('No email configuration found, creating test account...');
  const testAccount = await nodemailer.createTestAccount();
  console.log('Test email account created:', testAccount.user);
  
  return nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false,
    auth: {
      user: testAccount.user,
      pass: testAccount.pass,
    },
  });
};

// Initialize transporter
export const initEmailService = async () => {
  transporter = await createTransporter();
  console.log('Email service initialized');
  return transporter;
};

// Send an email
export const sendEmail = async ({ to, subject, text, html }) => {
  if (!transporter) {
    transporter = await createTransporter();
  }

  const mailOptions = {
    from: process.env.EMAIL_FROM || '"Meeting Scheduler" <<EMAIL>>',
    to: Array.isArray(to) ? to.join(', ') : to,
    subject,
    text,
    html: html || text,
  };

  const info = await transporter.sendMail(mailOptions);
  console.log('Message sent: %s', info.messageId);
  
  // If using Ethereal, show the preview URL
  if (info.preview) {
    console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
  }
  
  return info;
};

export default {
  initEmailService,
  sendEmail
};

# Task Scheduler with AI-Powered Handwriting Recognition

A modern task management application that converts handwritten tasks into digital format using AI and provides intelligent scheduling features.

## Features

- User authentication and authorization
- Upload handwritten task lists via images
- AI-powered handwriting recognition and task extraction (Google Gemini Vision API)
- Intelligent task scheduling and organization
- Priority and category detection
- Real-time task management dashboard
- Responsive design for all devices

## Tech Stack

### Frontend
- React with TypeScript
- Vite for build tooling
- Mantine UI component library
- React Router for navigation
- Axios for API communication

### Backend
- Node.js with Express
- MongoDB with Mongoose
- JWT for authentication
- Google Gemini Vision API integration

## Project Structure

```
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── contexts/      # React contexts (auth, etc.)
│   │   ├── pages/        # Main application pages
│   │   └── types/        # TypeScript type definitions
└── src/                   # Backend Node.js application
    ├── controllers/       # Route controllers
    ├── middleware/        # Express middlewares
    ├── models/           # Mongoose models
    ├── routes/           # API route definitions
    └── scripts/          # Utility scripts
```

## Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   # Install backend dependencies
   npm install

   # Install frontend dependencies
   cd client
   npm install
   ```

3. Create `.env` file in root directory:
   ```
   MONGODB_URI=your_mongodb_uri
   JWT_SECRET=your_jwt_secret
   GEMINI_API_KEY=your_google_gemini_api_key
   ```

4. Create `.env` file in client directory:
   ```
   VITE_API_URL=http://localhost:3000
   ```

## Running the Application

1. Start the backend server:
   ```bash
   npm run dev
   ```

2. Start the frontend development server:
   ```bash
   cd client
   npm run dev
   ```

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3000

## Gemini Vision API Setup

- Get your API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
- Add `GEMINI_API_KEY=your_google_gemini_api_key` to your `.env` file in the backend root
- The backend will use Gemini Vision API to extract tasks from uploaded handwritten images

## Upcoming Features

- Natural language processing for task parsing
- Intelligent scheduling suggestions
- Task dependency detection
- Automated priority assignment
- Learning from user corrections

## Development Status

Currently implementing:
- Basic task management functionality
- Image upload and processing pipeline
- AI service integration (Gemini Vision)
- Enhanced scheduling features
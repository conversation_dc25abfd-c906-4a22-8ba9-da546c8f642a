// main.js
import { EditorManager } from "../modules/editor-manager.js";
import ElementsBuilder from "./elements-builder.js";
import { getContent } from "./request.js";

const { div: Div, p: Paragraph } = ElementsBuilder.multiple("div", "p");

// Show loading indicator
const loadingDiv = new Div({
  appendChild: new Paragraph({
    textContent: "Loading presentation...",
    $styles: { margin: "0px" }
  }),
  $styles: {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    padding: "20px",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
    borderRadius: "4px",
    zIndex: "9999"
  }
});

document.body.appendChild(loadingDiv);

// Initialize the editor manager
const params = new URLSearchParams(window.location.search);
const id = params.get("id");
const isPreview = params.has("preview");

// Initialize with readonly option if needed
const editorManager = new EditorManager(id);

try {
  // Load data after initialization
  const data = await getContent(id);
  
  // Remove loading indicator
  document.body.removeChild(loadingDiv);
  
  // If data was retrieved successfully, load it
  if (data && Array.isArray(data)) {
    editorManager.loadSampleData(data);
    
    // Call setReadonly to ensure all UI elements are updated properly
    setTimeout(() => {
      editorManager.setReadonly(isPreview);
    }, 100);
  } else {
    console.error("No valid data received from server");
    alert("Failed to load presentation data. Please try again later.");
  }
} catch (error) {
  // Remove loading indicator
  document.body.removeChild(loadingDiv);
  
  console.error("Error loading presentation:", error);
  alert("Failed to load presentation. Please try again later.");
}
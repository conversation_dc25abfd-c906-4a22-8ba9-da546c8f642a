{"name": "editorjs-text-alignment-blocktune", "version": "1.0.3", "main": "./dist/bundle.js", "license": "MIT", "keywords": ["codex editor", "paragraph", "alignment", "tool", "editor.js", "editorjs"], "repository": {"type": "git", "url": "https://github.com/kaaaaaaaaaaai/editorjs-alignment-blocktune.git"}, "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development --watch", "pull_tools": "git submodule update --init --recursive"}, "devDependencies": {"@babel/core": "^7.13.14", "@babel/preset-env": "^7.13.12", "babel-loader": "^8.2.2", "css-loader": "^5.2.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "webpack": "^5.30.0", "webpack-cli": "^4.6.0"}, "bugs": {"url": "https://github.com/kaaaaaaaaaaai/paragraph-with-alignment/issues"}, "homepage": "https://github.com/kaaaaaaaaaaai/paragraph-with-alignment#readme", "directories": {"example": "example"}, "dependencies": {}, "author": "kaaaaaaaaaaai <<EMAIL>>", "description": "text alignment block tune tool for Editor.js"}
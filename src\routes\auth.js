import express from "express";
import { register, login, getProfile } from "../controllers/authController.js";
import auth from "../middleware/auth.js";

const router = express.Router();

// Add logging middleware
router.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`);
  console.log('Request body:', req.body);
  next();
});

// Register new user
router.post("/register", register);

// Login user
router.post("/login", login);

// Get user profile (protected route)
router.get("/profile", auth, getProfile);

export default router;

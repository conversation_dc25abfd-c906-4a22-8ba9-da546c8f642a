{"name": "tasks-scheduler", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node --max-old-space-size=4096 src/index.js", "dev": "node --max-old-space-size=4096 src/index.js", "start:high-memory": "node --max-old-space-size=8192 src/index.js", "client": "cd client && npm run dev", "client:build": "cd client && npm run build", "server": "node src/index.js", "create-default-user": "node src/scripts/createDefaultUser.js", "seed-sample-data": "node scripts/seed-sample-data.js", "seed-quick-data": "node scripts/seed-quick-data.js", "seed-indian-committees": "node scripts/seed-indian-committees.js", "seed-committees-real-users": "node scripts/seed-committees-with-real-users.js", "seed-meetings-tasks": "node scripts/seed-sample-meetings-tasks.js", "verify-indian-data": "node scripts/verify-indian-data.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/genai": "^0.6.0", "agenda": "^5.0.0", "archiver": "^5.3.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.9", "uuid": "^9.0.1"}, "description": ""}
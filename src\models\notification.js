import mongoose from "mongoose";

const notification = new mongoose.Schema({
    committeeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Committee",
        required: true,
    },
    periodStart: {
        type: Date,
        required: true,
    },
    showPeriod: {
        type: Date,
        required: true,
    },
    periodEnd: {
        type: Date,
        required: true,
    },
    cronId: {
        type: String,
        required: false,
    },
}, { timestamps: true, });

notification.index({ committeeId: 1, periodStart: 1, periodEnd: 1 }, { background: true, unique: true });
export default mongoose.model("Notification", notification);

import mongoose from "mongoose";

const schemeSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    required: true,
    trim: true
  },
  startDate: { 
    type: Date, 
    required: true 
  },
  aimAndObjective: { 
    type: String,
    trim: true
  },
  eligibilityCriteria: { 
    type: String,
    trim: true
  },
  documentLink: { 
    type: String,
    trim: true
  },
  benefits: { 
    type: String,
    trim: true
  },
  sourceOfFund: { 
    type: String,
    enum: ["Central", "State", "Sponsored", "50:50"],
    required: true
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User", 
    required: true 
  },
  updatedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User"
  }
}, { 
  timestamps: true 
});

export default mongoose.model("Scheme", schemeSchema);
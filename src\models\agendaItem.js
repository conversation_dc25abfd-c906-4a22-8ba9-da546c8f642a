import mongoose from "mongoose";

const agendaItemSchema = new mongoose.Schema({
  title: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String 
  },
  meeting: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Meeting", 
    required: true 
  },
  order: { 
    type: Number, 
    default: 0 
  },
  files: [{
    _id: { 
      type: mongoose.Schema.Types.ObjectId, 
      auto: true 
    },
    filename: { 
      type: String 
    },
    originalname: { 
      type: String 
    },
    path: { 
      type: String 
    },
    mimetype: { 
      type: String 
    },
    size: { 
      type: Number 
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  analysis: {
    token: { 
      type: String,
      default: null
    },
    status: { 
      type: Number, 
      default: 0  // 0: not analyzed, other values can be defined for different states
    },
    time: { 
      type: Date,
      default: null
    }
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "User" 
  }
}, { timestamps: true });

export default mongoose.model("AgendaItem", agendaItemSchema);

// @ts-check

import * as _fabric from "fabric";
import ElementsBuilder from "./elements-builder.js";

const { div: Div } = ElementsBuilder.multiple("div");

export const Fabric = _fabric.fabric ?? _fabric;

/**
 * @typedef {{ element: HTMLElement, action(editor: FabricEditor): void, onSelect?(editor: FabricEditor): void, onDeselect?(editor: FabricEditor): void }} FabricTool
 */

/**
 * @typedef {{ tools?: FabricTool[]; canvas?: _fabric.fabric.ICanvasOptions; initialZoom?: number; initialPan?: { x: number, y: number } }} FabricOptions
 */

export default class FabricEditor extends Fabric.Canvas {

  /** @type {FabricTool[]} */
  #tools = [];
  #toolbar;

  /**
   * Create a new FabricEditor instance
   * @param {HTMLCanvasElement} canvasElement - The canvas element to use
   * @param {FabricOptions} [options] - Configuration options
   */
  constructor(canvasElement, options) {
    super(canvasElement, {
      width: 1920,
      height: 1080,
      ...(options?.canvas ?? {})
    });
    this.#tools = options?.tools ?? [];
    this.#toolbar = this.#createToolbar(this.#tools);
    const zoomValue = options?.initialZoom ?? 1;
    const panPoint = new Fabric.Point(options?.initialPan?.x ?? 0, options?.initialPan?.y ?? 0);
    this.setZoom(zoomValue);
    this.absolutePan(panPoint);
    this.renderCanvas(this.getContext(), this.getObjects());

    this.#registerListeners();
  }

  get toolbar() {
    return this.#toolbar;
  }

  addList() {}

  addTable() {}

  resize() {
    const parentElement = this.getElement().parentElement?.parentElement;
    const { width, height } = parentElement?.getBoundingClientRect() ?? { width: 1920, height: 1080 };
    const zoom = width / 1920;
    console.log(zoom);
    this.setDimensions({ width, height: width / 1.777 });
    this.setZoom(zoom);
  }

  #registerListeners() {

    window.addEventListener("load", () => this.resize(), { once: true });
    window.addEventListener("resize", () => this.resize());

    // create text box on canvas click
    this.on("mouse:down", (event) => {
      if (event.target) return;
      const text = new Fabric.IText("Double-click to edit", {
        left: event.pointer?.x ?? 0,
        top: event.pointer?.y ?? 0,
        fontFamily: "Arial",
        fontSize: 24,
        fill: "#333333",
        originX: "center",
        originY: "center"
      });
      this.add(text);
      this.setActiveObject(text);
    });

  }

  /** @param {FabricTool[]} tools */
  #createToolbar(tools) {
    const element = new Div({
      append: tools.map(tool => {
        tool.element.addEventListener("click", () => tool.action(this));
        return tool.element;
      })
    });
    return { element };
  }

};
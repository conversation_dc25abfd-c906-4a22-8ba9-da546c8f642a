import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import {
  TextInput,
  PasswordInput,
  Paper,
  Text,
  Button,
  Stack,
  Center,
  Image,
} from "@mantine/core";
import { IconX, IconCheck } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [emailError, setEmailError] = useState("");
  const navigate = useNavigate();
  const { login } = useAuth();

  const validateEmail = (email: string): boolean => {
    if (!email) {
      setEmailError("Email is required");
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address");
      return false;
    }
    setEmailError("");
    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      return;
    }

    if (!password) {
      notifications.show({
        title: "Validation Error",
        message: "Password is required",
        color: "red",
        icon: <IconX />,
      });
      return;
    }

    setLoading(true);
    try {
      await login(email, password);
      notifications.show({
        title: "Welcome back",
        message: "You've successfully logged in.",
        color: "green",
        icon: <IconCheck />,
      });
      navigate("/");
    } catch (error: any) {
      notifications.show({
        title: "Login failed",
        message: error.response?.data?.error || "Invalid credentials",
        color: "red",
        icon: <IconX />,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Center mih="100vh" bg="gray.0">
      <Paper withBorder shadow="md" radius="md" p="xl" w={360}>
        <Stack gap="sm" align="center">
        <Image
          src="/assets/logo.svg"
          alt="Logo"
          width={40}
          height={40}
          fit="contain"
        />
        <Stack gap={2} align="center">
          <Text size="md" fw={500} c="dimmed" ta="center">
            District Committee Meeting
          </Text>
          <Text size="md" fw={500} c="dimmed" ta="center">
            Automation & Monitoring Platform
          </Text>
        </Stack>
      </Stack>

        <br />

        <form onSubmit={handleSubmit}>
          <Stack gap="md" mt="md">
            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              required
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (emailError) validateEmail(e.target.value);
              }}
              error={emailError}
              disabled={loading}
            />
            <PasswordInput
              label="Password"
              placeholder="Your password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
            <Button
              type="submit"
              loading={loading}
              fullWidth
              size="md"
            >
              {loading ? "Logging in..." : "Log in"}
            </Button>
          </Stack>
        </form>

        <Text ta="center" size="xs" mt="xl" c="dimmed">
          Powered by <Text span fw={500}>CodeQuotient</Text>
        </Text>
      </Paper>
    </Center>
  );
}

import { useState, useCallback, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Group,
  Paper,
  Card,
  SimpleGrid,
  Stack,
  Loader,
  Button,
  Tooltip,
  Avatar,
  Divider,
  Modal,
  TextInput,
  Textarea,
  Checkbox,
  ActionIcon,
  ScrollArea,
  Notification,
} from '@mantine/core';
import { 
  IconCalendar, 
  IconPlus,
  IconBuilding,
  IconUserCheck,
  IconChecks,
  IconMail,
  IconX,
  IconCheck
} from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Meeting, Committee, Directory } from '../types';
import { PaginationResponse, usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { useDisclosure } from '@mantine/hooks';

export default function MeetingsList() {
  const navigate = useNavigate();
  const [statusFilter, _setStatusFilter] = useState<string | null>(null);
  const [committeeFilter, _setCommitteeFilter] = useState<string | null>(null);
  const [dateRangeFilter, _setDateRangeFilter] = useState<string | null>(null);
  const [committees, setCommittees] = useState<Committee[]>([]);
  
  // Invite Modal State
  const [inviteModalOpened, { open: openInviteModal, close: closeInviteModal }] = useDisclosure(false);
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [committeeMembers, setCommitteeMembers] = useState<Directory[]>([]);
  const [selectedMemberIds, setSelectedMemberIds] = useState<string[]>([]);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [sendingEmail, setSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);

  // Fetch function for pagination with filters
  const fetchMeetings = useCallback(async (params: any) => {
    const filters: Record<string, string> = {};
    if (statusFilter) filters.status = statusFilter;
    if (committeeFilter) filters.committeeId = committeeFilter;
    if (dateRangeFilter) filters.dateRange = dateRangeFilter;

    const response = await apiClient.get<PaginationResponse<Meeting>>('/api/meetings', { 
      params: { 
        ...params, 
        ...filters,
        populate: 'committee,attendees,invitations.member', 
      }
    });
    return response.data;
  }, [statusFilter, committeeFilter, dateRangeFilter]);

  const {
    data: meetings,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
  } = usePagination<Meeting>(fetchMeetings, {
    sortBy: 'startDate',
    sortOrder: 'desc'
  });

  console.log(meetings)

  // Fetch committees for filter
  useEffect(() => {
    const fetchCommitteeOptions = async () => {
      try {
        const response = await apiClient.get('/api/committee', { params: { limit: 100 } });
        setCommittees(response.data.data || []);
      } catch (error) {
        console.error('Failed to fetch committees:', error);
      }
    };

    fetchCommitteeOptions();
  }, []);

  const sortOptions = [
    { value: 'startDate', label: 'Meeting Date' },
    { value: 'status', label: 'Status' },
    { value: 'title', label: 'Title' },
    { value: 'createdAt', label: 'Date Created' },
  ];

  const handleMeetingClick = (meeting: Meeting) => {
    const committeeId = typeof meeting.committee === 'string' ? meeting.committee : meeting.committee._id;
    navigate(`/committee/${committeeId}/meetings/${meeting._id}`);
  };

  const handleCreateMeeting = () => {
    // This would likely open a modal or navigate to a create meeting page
    // For now, let's just navigate to the committees page where they can select a committee
    navigate('/committees');
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'EEE, MMM d, yyyy h:mm a');
  };

  const getCommitteeName = (meeting: Meeting) => {
    if (typeof meeting.committee === 'object' && meeting.committee) {
      return meeting.committee.name;
    }
    
    // Try to find the committee name from our state
    if (typeof meeting.committee === 'string') {
      const foundCommittee = committees.find(c => c._id === meeting.committee);
      if (foundCommittee) {
        return foundCommittee.name;
      }
    }
    
    return '<Committee unavailable>';
  };

  // Fetch committee members for a meeting
  const fetchCommitteeMembers = useCallback(async (meeting: Meeting) => {
    try {
      // Get committee ID from the meeting
      const committeeId = typeof meeting.committee === 'string' ? meeting.committee : meeting.committee._id;
      
      // Fetch committee details with members
      const response = await apiClient.get(`/api/committee/${committeeId}`);
      const committee = response.data;
      
      if (committee && committee.members && committee.members.length > 0) {
        setCommitteeMembers(committee.members);
        // Set the email subject with meeting title and date
        const meetingDate = format(new Date(meeting.startDate), 'EEE, MMM d, yyyy');
        setEmailSubject(`Invitation: ${meeting.title} - ${meetingDate}`);
        
        // Set default email content
        setEmailContent(`
You are invited to attend the following meeting:

Meeting: ${meeting.title}
Date: ${format(new Date(meeting.startDate), 'EEEE, MMMM d, yyyy')}
Time: ${format(new Date(meeting.startDate), 'h:mm a')}

${meeting.description ? `\nDetails:\n${meeting.description}` : ''}

Please confirm your attendance.
        `.trim());
      }
    } catch (error) {
      console.error('Failed to fetch committee members:', error);
    }
  }, []);

  // Handle opening the invite modal
  const handleOpenInviteModal = (meeting: Meeting) => {
    setSelectedMeeting(meeting);
    
    // Pre-select members who have been invited before
    if (meeting.invitations && meeting.invitations.length > 0) {
      const invitedMemberIds = meeting.invitations.map(inv => 
        typeof inv.member === 'object' ? inv.member._id : inv.member
      );
      setSelectedMemberIds(invitedMemberIds);
      
      // Set email error to show invitation status
      const invitationInfo = `${meeting.invitations.length} member(s) have already been invited to this meeting.`;
      setEmailError(invitationInfo);
      setTimeout(() => setEmailError(null), 5000);
    } else {
      setSelectedMemberIds([]);
    }
    
    setEmailSent(false);
    fetchCommitteeMembers(meeting);
    openInviteModal();
  };

  // Toggle member selection
  const toggleMemberSelection = (memberId: string) => {
    setSelectedMemberIds(current => 
      current.includes(memberId) 
        ? current.filter(id => id !== memberId)
        : [...current, memberId]
    );
  };

  // Select or deselect all members
  const handleSelectAllMembers = (select: boolean) => {
    if (select) {
      setSelectedMemberIds(committeeMembers.map(member => member._id));
    } else {
      setSelectedMemberIds([]);
    }
  };

  // Send invitations to selected members
  const handleSendInvitations = async () => {
    if (!selectedMeeting || selectedMemberIds.length === 0 || !emailSubject || !emailContent) {
      setEmailError('Please select at least one member and provide email subject and content');
      return;
    }

    try {
      setSendingEmail(true);
      setEmailError(null);
      
      const response = await apiClient.post('/api/meeting-invitations', {
        meetingId: selectedMeeting._id,
        recipientIds: selectedMemberIds,
        emailSubject,
        emailContent
      });
      
      setEmailSent(true);
      console.log('Invitations sent successfully:', response.data);
      
      // Close modal after 3 seconds on success
      setTimeout(() => {
        closeInviteModal();
        // Reset form
        setSelectedMemberIds([]);
        setEmailSubject('');
        setEmailContent('');
      }, 3000);
      
    } catch (error) {
      console.error('Failed to send invitations:', error);
      setEmailError(
        error && typeof error === 'object' && 'response' in error && 
        error.response && typeof error.response === 'object' && 'data' in error.response && 
        error.response.data && typeof error.response.data === 'object' && 'error' in error.response.data ? 
        error.response.data.error as string : 
        'Failed to send invitations. Please try again.'
      );
    } finally {
      setSendingEmail(false);
    }
  };

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>Meetings</Title>
          <Text c="dimmed">View and manage all committee meetings</Text>
        </div>
        <Button 
          leftSection={<IconPlus size={16} />}
          onClick={handleCreateMeeting}
        >
          Create Meeting
        </Button>
      </Group>

      {/* Filters Section */}
      {/* <Paper withBorder p="md" mb="lg">
        <Group align="flex-start">
          <Stack gap="xs">
            <Group gap="xs">
              <IconFilter size={16} />
              <Text fw={500} size="sm">Filters</Text>
            </Group>
            
            <Group wrap="wrap">
              {/ <Select
                label="Status"
                placeholder="Filter by status"
                value={statusFilter}
                onChange={setStatusFilter}
                data={[
                  { value: '', label: 'All Statuses' },
                  { value: 'scheduled', label: 'Scheduled' },
                  { value: 'in-progress', label: 'In Progress' },
                  { value: 'completed', label: 'Completed' },
                  { value: 'cancelled', label: 'Cancelled' },
                ]}
                style={{ width: 150 }}
                clearable
              /> /}
              
              <Select
                label="Committee"
                placeholder="Filter by committee"
                value={committeeFilter}
                onChange={setCommitteeFilter}
                data={[
                  { value: '', label: 'All Committees' }, 
                  ...committees.map(committee => ({ 
                    value: committee._id, 
                    label: committee.name 
                  }))
                ]}
                style={{ width: 200 }}
                searchable
                clearable
              />
            </Group>
          </Stack>
        </Group>
      </Paper> */}

      {/* Search and Pagination Controls */}
      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>

      {/* Meetings List */}
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '3rem 0' }}>
          <Loader />
        </div>
      ) : meetings.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No meetings found matching your filters</Text>
            <Button
              variant="light"
              onClick={handleCreateMeeting}
            >
              Create a meeting
            </Button>
          </Stack>
        </Paper>
      ) : (
        <SimpleGrid cols={{ base: 1, md: 2, xl: 3 }} spacing="md">
          {meetings.map((meeting) => (
            <Card 
              key={meeting._id} 
              withBorder 
              shadow="sm" 
              padding="lg" 
              radius="md"
              style={{ cursor: meeting.committee === null ? 'initial' : 'pointer' }}
              onClick={() => meeting.committee === null ? undefined : handleMeetingClick(meeting)}
            >
              <Card.Section withBorder inheritPadding py="xs">
                <Group justify="space-between">
                  <Text fw={500} truncate>{meeting.title}</Text>
                </Group>
              </Card.Section>

              <Stack gap="md" mt="md">
                <Group gap="xs" wrap="nowrap">
                  <IconBuilding size={16} stroke={1.5} style={{ flexShrink: 0 }} />
                  <Text size="sm" lineClamp={1}>
                    {getCommitteeName(meeting)}
                  </Text>
                </Group>

                <Group gap="xs" wrap="nowrap">
                  <IconCalendar size={16} stroke={1.5} style={{ flexShrink: 0 }} />
                  <Stack gap={0}>
                    <Text size="sm">{formatDate(meeting.startDate)}</Text>
                    {/* <Text size="xs" c="dimmed">
                      to {format(new Date(meeting.endDate ?? Date.now()), 'h:mm a')}
                    </Text> */}
                  </Stack>
                </Group>

                {(meeting.attendees && meeting.attendees.length > 0) || (meeting.invitations && meeting.invitations.length > 0) ? (
                  <Group gap="xs" wrap="nowrap">
                    <IconUserCheck size={16} stroke={1.5} style={{ flexShrink: 0, marginTop: 2 }} />
                    <Tooltip.Group openDelay={300} closeDelay={100}>
                      <Avatar.Group spacing="sm">
                        {/* Display attendees first */}
                        {meeting.attendees?.slice(0, 2).map(attendee => (
                          <Tooltip 
                            key={attendee._id} 
                            label={`${attendee.name} (Present)`}
                          >
                            <Avatar 
                              radius="xl" 
                              size="sm"
                              color="green"
                            >
                              {attendee.name?.charAt(0)}
                            </Avatar>
                          </Tooltip>
                        ))}
                        
                        {/* Show invited members */}
                        {meeting.invitations?.filter(inv => !meeting.attendees?.some(att => 
                          typeof inv.member === 'object' ? 
                          inv.member._id === att._id : 
                          inv.member === att._id
                        ))
                        .slice(0, meeting.attendees?.length ? 1 : 2)
                        .map((invitation, index) => {
                          const memberName = typeof invitation.member === 'object' ? 
                            invitation.member.name : 
                            invitation.member;
                          const firstChar = typeof memberName === 'string' ? 
                            memberName.charAt(0) : '?';
                            
                          return (
                            <Tooltip 
                              key={`inv-${index}`}
                              label={`${memberName} (Invited)`}
                            >
                              <Avatar 
                                radius="xl" 
                                size="sm"
                                color="blue"
                                styles={{ 
                                  root: { 
                                    border: '1px dashed #228be6',
                                    backgroundColor: 'rgba(34, 139, 230, 0.2)'
                                  } 
                                }}
                              >
                                {firstChar}
                              </Avatar>
                            </Tooltip>
                          );
                        })}
                        
                        {/* Show counter for remaining */}
                        {((meeting.attendees?.length || 0) + (meeting.invitations?.length || 0) > 3) && (
                          <Avatar radius="xl" size="sm">
                            +{(meeting.attendees?.length || 0) + (meeting.invitations?.length || 0) - 
                              Math.min((meeting.attendees?.length || 0), 2) - 
                              Math.min((meeting.invitations?.filter(inv => !meeting.attendees?.some(att => 
                                typeof inv.member === 'object' ? 
                                inv.member._id === att._id : 
                                inv.member === att._id
                              )).length || 0), meeting.attendees?.length ? 1 : 2)
                            }
                          </Avatar>
                        )}
                      </Avatar.Group>
                    </Tooltip.Group>
                  </Group>
                ) : null}

                {meeting.description && (
                  <Text lineClamp={2} size="sm" c="dimmed">
                    {meeting.description}
                  </Text>
                )}

                <Divider />
                
                <Group justify="space-between" mt="xs">
                  <Group gap="xs">
                    <IconChecks size={16} stroke={1.5} />
                    <Text size="sm">
                      {meeting.taskCount || 0} {meeting.taskCount === 1 ? 'Task' : 'Tasks'}
                    </Text>
                  </Group>
                  <Tooltip 
                    label={`${meeting.invitations?.length || 0} member(s) invited`}
                    disabled={!meeting.invitations?.length}
                  >
                    <Button
                      size="xs"
                      variant="filled"
                      color="blue"
                      leftSection={<IconMail size={16} />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOpenInviteModal(meeting);
                      }}
                    >
                      Invite Members
                      {meeting.invitations?.length ? ` (${meeting.invitations.length})` : ''}
                    </Button>
                  </Tooltip>
                </Group>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>
      )}

      {/* Meeting Invitation Modal */}
      <Modal
        opened={inviteModalOpened}
        onClose={closeInviteModal}
        title={<Text fw={600}>Invite Members to Meeting</Text>}
        size="lg"
      >
        {emailSent ? (
          <Notification
            title="Invitations Sent!"
            color="green"
            icon={<IconCheck size={20} />}
            onClose={() => {}}
          >
            Email invitations have been sent successfully.
          </Notification>
        ) : (
          <Stack>
            {emailError && (
              <Notification
                title="Error"
                color="red"
                icon={<IconX size={20} />}
                onClose={() => setEmailError(null)}
              >
                {emailError}
              </Notification>
            )}
            
            <TextInput
              label="Subject"
              placeholder="Meeting invitation subject"
              value={emailSubject}
              onChange={(e) => setEmailSubject(e.target.value)}
              leftSection={<IconMail size={16} />}
              required
            />
            
            <Textarea
              label="Email Content"
              placeholder="Write the invitation message here..."
              minRows={5}
              value={emailContent}
              onChange={(e) => setEmailContent(e.target.value)}
              required
            />
            
            <Paper withBorder p="md">
              <Group justify="space-between" mb="xs">
                <Text fw={500}>Select Recipients</Text>
                <Group>
                  <Button 
                    variant="subtle" 
                    size="xs" 
                    onClick={() => handleSelectAllMembers(true)}
                  >
                    Select All
                  </Button>
                  <Button 
                    variant="subtle" 
                    size="xs" 
                    color="gray" 
                    onClick={() => handleSelectAllMembers(false)}
                  >
                    Clear
                  </Button>
                </Group>
              </Group>
              
              <ScrollArea h={200}>
                {committeeMembers.length === 0 ? (
                  <Text c="dimmed" ta="center" py="md">No members found for this committee</Text>
                ) : (
                  committeeMembers.map((member) => (
                    <Group key={member._id} py="xs" justify="space-between">
                      <Group>
                        <Avatar 
                          radius="xl" 
                          size="sm"
                          color={['blue', 'cyan', 'green', 'teal', 'violet'][Math.floor(Math.random() * 5)]}
                        >
                          {member.name?.charAt(0)}
                        </Avatar>
                        <div>
                          <Text size="sm">{member.name}</Text>
                          <Text size="xs" c="dimmed">{member.email || 'No email available'}</Text>
                        </div>
                      </Group>
                      <Checkbox
                        checked={selectedMemberIds.includes(member._id)}
                        onChange={() => toggleMemberSelection(member._id)}
                        disabled={!member.email}
                      />
                    </Group>
                  ))
                )}
              </ScrollArea>
            </Paper>
            
            <Group justify="flex-end">
              <Button 
                onClick={handleSendInvitations}
                loading={sendingEmail}
                disabled={selectedMemberIds.length === 0 || !emailSubject || !emailContent}
                leftSection={<IconCheck size={16} />}
              >
                Send Invitations
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Container>
  );
}

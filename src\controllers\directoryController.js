import Directory from "../models/directory.js";
import Committee from "../models/committee.js";

export const getDirectories = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      sortBy = 'createdAt', 
      sortOrder = 'desc',
      departmentId
    } = req.query;

    // Build search filter
    const searchFilter = {
      ...(search && {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { designation: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ]
      }),
      ...(departmentId && { department: departmentId })
    };

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Get total count for pagination
    const total = await Directory.countDocuments(searchFilter);

    // Get paginated results
    const data = await Directory.find(searchFilter)
      .populate("createdBy")
      .populate("department")
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    res.json({
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getDirectory = async (req, res) => {
  try {
    const data = await Directory.findById(req.params.id)
      .populate("createdBy")
      .populate("department");
    res.json(data);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const createDirectory = async (req, res) => {
  try {
    // If department is empty string or null, don't include it
    if (req.body.department === '' || req.body.department === null) {
      delete req.body.department;
    }
    
    const directory = new Directory({ ...req.body, createdBy: req.user._id });
    const saved = await directory.save();
    const populated = await Directory.findById(saved._id).populate('department');
    res.status(201).json(populated);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const updateDirectory = async (req, res) => {
  try {
    // If department is empty string or null, remove it
    if (req.body.department === '' || req.body.department === null) {
      req.body.department = undefined; // Use undefined to remove the field
    }
    
    const updated = await Directory.findByIdAndUpdate(req.params.id, req.body, { 
      new: true 
    }).populate('department');
    
    res.json(updated);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const getDirectoryUsage = async (req, res) => {
  try {
    const directoryId = req.params.id;

    // Find committees where this person is a member
    const committeesAsMember = await Committee.find({
      'members': directoryId
    }).select('name');

    // Find committees where this person is an assistant
    const committeesAsAssistant = await Committee.find({
      'assistant': directoryId
    }).select('name');

    res.json({
      committees: committeesAsMember.map(c => c.name),
      isAssistant: committeesAsAssistant.map(c => c.name)
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const deleteDirectory = async (req, res) => {
  try {
    const directoryId = req.params.id;

    // Remove from all committees as member
    await Committee.updateMany(
      { members: directoryId },
      { $pull: { members: directoryId } }
    );

    // Remove as assistant from committees
    await Committee.updateMany(
      { assistant: directoryId },
      { $unset: { assistant: "" } }
    );

    // Delete the directory entry
    await Directory.findByIdAndDelete(directoryId);
    res.json({ success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

import AgendaItem from "../models/agendaItem.js";
import Meeting from "../models/meeting.js";
import mongoose from "mongoose";
import { extractAgendaFromPresentationWithGemini } from "../services/gemini.js";
import fs from 'fs';
import path from 'path';
import axios from 'axios';

/** @enum {typeof AnalysisStatus[keyof typeof AnalysisStatus]} */
const AnalysisStatus = Object.freeze({
  NONE: 0,
  QUEUED: 1,
  PROCESSING: 16,
  ANALYSED: 32,
  ERROR: -1
});

// Get all agenda items for a meeting
export const getAgendaItems = async (req, res) => {
  try {
    const { meetingId } = req.params;

    const agendaItems = await AgendaItem.find({ meeting: meetingId })
      .sort({ order: 1 })
      .populate('files');

    res.status(200).json(agendaItems);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: error.message });
  }
};

// Create a new agenda item
export const createAgendaItem = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { title, description, order } = req.body;

    const meeting = await Meeting.findById(meetingId);
    if (!meeting) {
      return res.status(404).json({ message: "Meeting not found" });
    }

    const agendaItem = new AgendaItem({
      title,
      description,
      meeting: meetingId,
      order: typeof order === 'number' ? order : await AgendaItem.countDocuments({ meeting: meetingId }),
      createdBy: req.user ? req.user.id : null,
    });

    await agendaItem.save();
    res.status(201).json(agendaItem);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: error.message });
  }
};

// Update an agenda item
export const updateAgendaItem = async (req, res) => {
  try {
    const { meetingId, agendaItemId } = req.params;
    const { title, description, order } = req.body;

    const agendaItem = await AgendaItem.findOne({
      _id: agendaItemId,
      meeting: meetingId
    });

    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }

    if (title) agendaItem.title = title;
    if (description !== undefined) agendaItem.description = description;
    if (order !== undefined) agendaItem.order = order;

    await agendaItem.save();
    res.status(200).json(agendaItem);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: error.message });
  }
};

// Delete an agenda item
export const deleteAgendaItem = async (req, res) => {
  try {
    const { meetingId, agendaItemId } = req.params;

    const agendaItem = await AgendaItem.findOneAndDelete({
      _id: agendaItemId,
      meeting: meetingId
    });

    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }

    res.status(200).json({ message: "Agenda item deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: error.message });
  }
};

// Reorder agenda items
export const reorderAgendaItems = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { items } = req.body;

    if (!Array.isArray(items)) {
      return res.status(400).json({ message: "Items must be an array" });
    }

    const bulkOperations = items.map(({ id, order }) => ({
      updateOne: {
        filter: { _id: id, meeting: meetingId },
        update: { $set: { order } }
      }
    }));

    await AgendaItem.bulkWrite(bulkOperations);

    const updatedItems = await AgendaItem.find({ meeting: meetingId }).sort({ order: 1 });
    
    res.status(200).json(updatedItems);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: error.message });
  }
};

// Upload presentation for agenda extraction
export const uploadPresentationForAgenda = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const skipAnalysis = req.query.skipAnalysis === 'true';

    // Validate meetingId
    if (!meetingId || meetingId === 'undefined') {
      return res.status(400).json({ message: "Invalid meeting ID" });
    }
    
    // Validate if it's a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(meetingId)) {
      return res.status(400).json({ message: "Invalid meeting ID format" });
    }

    if (!req.file) {
      return res.status(400).json({ message: "No file provided" });
    }

    console.log("File uploaded:", req.file);
    const filePath = req.file.path;
    const fileUrl = `/uploads/${req.file.filename}`;

    if (skipAnalysis) {
      // If skipAnalysis is true, just create a simple file entry without AI extraction
      const fileData = {
        filename: req.file.filename,
        originalname: req.file.originalname,
        path: fileUrl,
        mimetype: req.file.mimetype,
        size: req.file.size
      };
      
      // Create an agenda item with the file attached
      const agendaItem = new AgendaItem({
        title: `Document: ${req.file.originalname}`,
        description: '',
        meeting: meetingId,
        files: [fileData],
        order: await AgendaItem.countDocuments({ meeting: meetingId }),
        createdBy: req.user ? req.user.id : null
      });
      
      await agendaItem.save();
      
      return res.status(201).json({
        message: "Document uploaded successfully",
        agendaItem
      });
    }

    // If not skipAnalysis, continue with the original flow
    console.log("Processing presentation at:", filePath);
    // Use Gemini Vision API to extract agenda items and slides
    const { extractedText, suggestedAgendaItems, slideImages } = await extractAgendaFromPresentationWithGemini(filePath);
    
    console.log("Extraction complete. Agenda items:", suggestedAgendaItems.length);
    console.log("Slide images:", slideImages);

    res.status(200).json({
      extractedText,
      suggestedAgendaItems,
      slideImages, // URLs to the extracted slide images
      meetingId
    });
  } catch (error) {
    console.error("Error in uploadPresentationForAgenda:", error);
    
    // More detailed error message for easier debugging
    let errorMessage = "Error processing file";
    let errorDetails = error.message;
    
    if (error.message && error.message.includes("400 Bad Request")) {
      errorMessage = "Gemini API rejected the image";
      errorDetails = "The image format may be unsupported or corrupted. Try uploading a different image.";
    }
    
    res.status(500).json({ 
      message: errorMessage, 
      error: errorDetails,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Create agenda items from presentation
export const createAgendaItemsFromPresentation = async (req, res) => {
  try {
    const { meetingId } = req.params;
    const { agendaItems, slideImages, originalText } = req.body;

    if (!agendaItems || !Array.isArray(agendaItems) || agendaItems.length === 0) {
      return res.status(400).json({ message: "No agenda items provided" });
    }

    const meeting = await Meeting.findById(meetingId);
    if (!meeting) {
      return res.status(404).json({ message: "Meeting not found" });
    }

    // Get current count for ordering new items at the end
    const currentCount = await AgendaItem.countDocuments({ meeting: meetingId });
    
    const createdItems = [];

    for (let i = 0; i < agendaItems.length; i++) {
      const itemData = agendaItems[i];
      
      // Create the agenda item
      const agendaItem = new AgendaItem({
        title: itemData.title,
        description: itemData.description || '',
        meeting: meetingId,
        order: currentCount + i,
        createdBy: req.user ? req.user.id : null
      });
      
      await agendaItem.save();
        // If there's a slide image for this agenda item, attach it as a file
      if (slideImages && slideImages[i]) {
        // The slideImage has already been saved during the extraction process
        // Here we just need to add a reference to the file in our database
        const slideUrl = slideImages[i];
        const filename = path.basename(slideUrl);
        
        console.log(`Adding file reference for agenda item ${i}: ${slideUrl}`);        // Create a file entry for this slide
        // Determine the file type
        const isSvg = slideUrl.endsWith('.svg');
        
        const file = {
          filename,
          originalname: `Slide for ${itemData.title}${isSvg ? '.svg' : '.jpg'}`,
          path: slideUrl,
          mimetype: isSvg ? 'image/svg+xml' : 'image/jpeg',          size: fs.existsSync(path.join(process.cwd(), 'uploads', filename)) 
            ? fs.statSync(path.join(process.cwd(), 'uploads', filename)).size 
            : 1024 // Default size if file not found
        };
        
        // Add the file to the agenda item's files array
        agendaItem.files = agendaItem.files || [];
        agendaItem.files.push(file);
        
        await agendaItem.save();
      }
      
      createdItems.push(agendaItem);
    }

    res.status(201).json(createdItems);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: "Error creating agenda items", error: error.message });
  }
};

// Analyze agenda item with AI
export const analyzeAgendaItem = async (req, res) => {
  try {
    const { meetingId, id: agendaItemId } = req.params; // Changed from 'agendaItemId' to 'id' to match route parameter

    // Find the agenda item with its files
    const agendaItem = await AgendaItem.findOne({
      _id: agendaItemId,
      meeting: meetingId
    }).populate('meeting');

    console.log("Analyzing agenda item:", agendaItemId, "for meeting:", meetingId);

    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }

    // Get analysis server URL from environment variable
    const ANALYSIS_SERVER_URL = process.env.ANALYSIS_SERVER_URL;
    
    try {
      const payload = {
        title: agendaItem.title,
        description: agendaItem.description,
        files: agendaItem.files ? agendaItem.files.map(f => {
          // Get just the filename from the path
          const filename = f.path.includes('/') ? f.path.split('/').pop() :
                          f.path.includes('\\') ? f.path.split('\\').pop() :
                          f.path;
          
          return { 
            name: f.originalname, 
            url: `${process.env.BASE_URL}/uploads/${filename}`,
            mimeType: f.mimetype
          };
        }) : []
      };
      console.log("Payload for analysis:", `${ANALYSIS_SERVER_URL}/api/requests`, payload);
      // Make the actual request to the external analysis server
      const response = await axios.post(`${ANALYSIS_SERVER_URL}/api/requests`, payload, {
        headers: {
          "x-internal-req": "testtokentesttoken"
        }
      });
      console.log("Analysis server response:", response.data);
      // Extract token from the response - the API returns { id: "token" }
      const token = response.data.id;
      
      // Update the agenda item with the analysis token and status
      agendaItem.analysis = {
        token: token,
        status: 1, // 1 indicates "analysis requested"
        time: new Date()
      };
      
      await agendaItem.save();
      
      // Return the updated analysis structure in the format the client expects
      res.status(200).json({
        analysis: {
          token: token,
          status: 1,
          time: new Date()
        }
      });
    } catch (error) {
      console.error("Error connecting to analysis server:", error.message);
      res.status(500).json({ 
        message: "Error connecting to analysis server", 
        error: error.message 
      });
    }
  } catch (error) {
    console.error("Error in analyzeAgendaItem:", error);
    
    let errorMessage = "Error analyzing agenda item";
    let errorDetails = error.message;
    
    if (error.message && error.message.includes("400 Bad Request")) {
      errorMessage = "AI analysis failed";
      errorDetails = "There was an issue processing the agenda item or its attachments.";
    }
    
    res.status(500).json({ 
      message: errorMessage, 
      error: errorDetails
    });
  }
};

// Check analysis status from external server
export const checkAnalysisStatus = async (req, res) => {
  try {
    // Use 'id' parameter name to match route definition
    const { meetingId, id } = req.params;
    console.log("Checking analysis status for:", { meetingId, id });

    // Find the agenda item
    const agendaItem = await AgendaItem.findOne({
      _id: id,
      meeting: meetingId
    });

    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }

    // Rest of function remains the same
    if (!agendaItem.analysis || !agendaItem.analysis.token) {
      return res.status(400).json({ message: "No analysis has been requested for this item" });
    }

    // Get analysis server URL from environment variable
    const ANALYSIS_SERVER_URL = process.env.ANALYSIS_SERVER_URL;
    
    // Make an API request to the analysis server
    try {
      const axios = await import('axios');
      console.log(`Checking analysis status at: ${ANALYSIS_SERVER_URL}/api/requests/${agendaItem.analysis.token}`);
      /** @type {import("axios").AxiosResponse<{ processStatus: AnalysisStatus; title: string; description: string }, never>} */
      const result = await axios.default.get(`${ANALYSIS_SERVER_URL}/api/requests/${agendaItem.analysis.token}`, {
        headers: {
          "x-internal-req": "testtokentesttoken"
        }
      });
      console.log("Analysis server response:", result.data);
      const processStatus = result.data.processStatus;
      
      // Always update our local record with the latest status from the server
      agendaItem.analysis.status = processStatus;
      await agendaItem.save();
      
      // If status is 32 (completed), include the analysis URL in the response
      if (processStatus === AnalysisStatus.ANALYSED) {
        // Generate the URL to view the analysis results - use editor format for direct opening
        const analysisUrl = `/editor?id=${agendaItem.analysis.token}`;
        
        return res.json({
          status: processStatus,
          message: "Analysis complete",
          analysisUrl
        });
      }
      
      // Otherwise return the current status
      res.json({
        status: processStatus,
        message: getStatusMessage(processStatus)
      });
    } catch (error) {
      console.error("Error connecting to analysis server:", error.message);
      res.status(500).json({ 
        message: "Error connecting to analysis server", 
        error: error.message
      });
    }
  } catch (error) {
    console.error("Error checking analysis status:", error);
    res.status(500).json({ message: "Error checking analysis status" });
  }
};

// Request analysis for an agenda item
export const requestAnalysis = async (req, res) => {
  try {
    const { meetingId, agendaItemId } = req.params;

    // Find the agenda item and meeting
    const agendaItem = await AgendaItem.findOne({
      _id: agendaItemId,
      meeting: meetingId
    }).populate('meeting');

    if (!agendaItem) {
      return res.status(404).json({ message: "Agenda item not found" });
    }

    // Check if there's already an analysis in progress
    if (agendaItem.analysis && agendaItem.analysis.status === 16) { // Processing
      return res.status(400).json({ message: "Analysis is already in progress" });
    }

    const meeting = agendaItem.meeting;
    
    // Get analysis server URL from environment variable
    const ANALYSIS_SERVER_URL = process.env.ANALYSIS_SERVER_URL || "https://analysis-server.example.com";

    try {
      const axios = await import('axios');
      
      const payload = {
        title: agendaItem.title,
        description: agendaItem.description,
        files: agendaItem.files ? agendaItem.files.map(f => {
          // Get just the filename from the path
          const filename = f.path.includes('/') ? f.path.split('/').pop() :
                          f.path.includes('\\') ? f.path.split('\\').pop() :
                          f.path;
          
          return { 
            name: f.originalname, 
            url: `${process.env.BASE_URL}/uploads/${filename}`,
            mimeType: f.mimetype
          };
        }) : []
      };
      console.log("Payload:", payload);
      // Make the actual request to the analysis server
      const result = await axios.default.post(`${ANALYSIS_SERVER_URL}/api/requests`, payload);

      // Extract the token from response - the API returns { id: "token" }
      const token = result.data.id;

      // Update the agenda item with the token and status
      agendaItem.analysis = {
        token,
        status: 1, // Queued
        time: new Date()
      };
      
      await agendaItem.save();

      res.status(200).json({
        message: "Analysis requested successfully",
        token
      });
    } catch (error) {
      console.error("Error requesting analysis from server:", error.message);
      res.status(500).json({ 
        message: "Error requesting analysis from server", 
        error: error.message 
      });
    }
  } catch (error) {
    console.error("Error requesting analysis:", error);
    res.status(500).json({ message: "Error requesting analysis" });
  }
};

// Helper function to get status message
const getStatusMessage = (status) => {
  console.log("STATUS:", status);
  switch (status) {
    case AnalysisStatus.NONE:
      return "Not analyzed";
    case AnalysisStatus.QUEUED:
      return "Analysis queued";
    case AnalysisStatus.PROCESSING:
      return "Analysis in progress";
    case AnalysisStatus.ANALYSED:
      return "Analysis complete";
    case AnalysisStatus.ERROR:
      return "Analysis failed";
    default:
      return "Unknown status";
  }
};

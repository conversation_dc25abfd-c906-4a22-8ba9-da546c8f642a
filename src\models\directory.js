import mongoose from "mongoose";

const directorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  designation: { type: String, required: true },
  description: { type: String },
  email: { type: String },
  phone: { type: String },
  address: { type: String },
  department: { type: mongoose.Schema.Types.ObjectId, ref: "Department" },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  createdAt: { type: Date, default: Date.now }
});

export default mongoose.model("Directory", directorySchema);

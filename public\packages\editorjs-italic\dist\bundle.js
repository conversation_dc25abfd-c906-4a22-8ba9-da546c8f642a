/**
 * Italic Tool for Editor.js
 */
class Italic {
  static get isInline() {
    return true;
  }

  constructor({api}) {
    this.api = api;
    this.button = null;
    this._state = false;
    this.tag = 'I';
    this.class = 'cdx-italic';
  }

  render() {
    this.button = document.createElement('button');
    this.button.type = 'button';
    this.button.innerHTML = '<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.67494 3.50017C5.67494 3.25164 5.87641 3.05017 6.12494 3.05017H10.6249C10.8735 3.05017 11.0749 3.25164 11.0749 3.50017C11.0749 3.7487 10.8735 3.95017 10.6249 3.95017H9.00587L7.2309 11.05H8.87493C9.12345 11.05 9.32493 11.2515 9.32493 11.5C9.32493 11.7486 9.12345 11.95 8.87493 11.95H4.37493C4.1264 11.95 3.92493 11.7486 3.92493 11.5C3.92493 11.2515 4.1264 11.05 4.37493 11.05H5.99397L7.76894 3.95017H6.12494C5.87641 3.95017 5.67494 3.7487 5.67494 3.50017Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>';
    this.button.classList.add(this.api.styles.inlineToolButton);
    
    return this.button;
  }

  surround(range) {
    if (this.state) {
      this.unwrap(range);
      return;
    }
    
    this.wrap(range);
  }

  wrap(range) {
    const selectedText = range.extractContents();
    const mark = document.createElement(this.tag);
    
    mark.classList.add(this.class);
    mark.appendChild(selectedText);
    range.insertNode(mark);
    
    this.api.selection.expandToTag(mark);
  }

  unwrap(range) {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    const text = range.extractContents();
    
    mark.remove();
    
    range.insertNode(text);
  }

  checkState() {
    const mark = this.api.selection.findParentTag(this.tag, this.class);
    
    this.state = !!mark;
    
    if (this.state) {
      this.button.classList.add(this.api.styles.inlineToolButtonActive);
    } else {
      this.button.classList.remove(this.api.styles.inlineToolButtonActive);
    }
  }

  get state() {
    return this._state;
  }

  set state(state) {
    this._state = state;
  }

  static get sanitize() {
    return {
      i: {
        class: 'cdx-italic'
      }
    };
  }
}

export default Italic;

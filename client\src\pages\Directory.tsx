import { useState, useCallback, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Paper,
  TextInput,
  Textarea,
  Modal,
  Stack,
  Card,
  ActionIcon,
  Grid,
  Loader,
  List,
  Alert,
  Select,
  Badge,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconEdit, IconTrash, IconMail, IconPhone, IconMapPin } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Directory, Department } from '../types';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';
import ErrorBoundary from '../components/ErrorBoundary';
import { useParams } from 'react-router-dom';

// Move DirectoryForm outside to prevent recreation on every render
const DirectoryForm = ({
  formData,
  setFormData,
  departments,
  onSubmit,
  onCancel,
  title,
  departmentId
}: {
  formData: any;
  setFormData: (data: any) => void;
  departments: { value: string; label: string; }[];
  onSubmit: () => void;
  onCancel: () => void;
  title: string;
  departmentId?: string;
}) => (
  <Stack>
    <TextInput
      label="Designation"
      placeholder="Enter designation"
      value={formData.designation}
      onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
      required
    />

    <TextInput
      label="Name"
      placeholder="Enter full name"
      value={formData.name}
      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      required
    />

    <Select
      label="Department"
      placeholder="Select department"
      value={departmentId ? departmentId : formData.department}
      onChange={(value) => setFormData({ ...formData, department: value || '' })}
      data={departments}
      clearable
      // disabled={!!departmentId}
    />

    <Textarea
      label="Description"
      placeholder="Enter description"
      value={formData.description}
      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
      minRows={2}
      resize="vertical"
    />

    <TextInput
      label="Email"
      placeholder="Enter email address"
      value={formData.email}
      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
    />

    <TextInput
      label="Phone"
      placeholder="Enter phone number"
      value={formData.phone}
      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
    />

    <Textarea
      label="Address"
      placeholder="Enter address"
      value={formData.address}
      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
      minRows={2}
      resize="vertical"
    />

    <Group justify="flex-end" mt="md">
      <Button variant="light" onClick={onCancel}>
        Cancel
      </Button>
      <Button onClick={onSubmit}>{title}</Button>
    </Group>
  </Stack>
);

function DirectoryPage() {
  const { departmentId } = useParams();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState<Directory | null>(null);
  const [deletingDirectory, setDeletingDirectory] = useState<Directory | null>(null);
  const [directoryUsage, setDirectoryUsage] = useState<{
    committees: string[];
    isAssistant: string[];
  } | null>(null);
  const [departments, setDepartments] = useState<{ value: string; label: string; }[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    designation: '',
    description: '',
    email: '',
    phone: '',
    address: '',
    department: departmentId || '',
  });
  
  // Fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const response = await apiClient.get('/api/departments');
        const deptOptions = response.data.data.map((dept: Department) => ({
          value: dept._id,
          label: dept.name
        }));
        setDepartments(deptOptions);
      } catch (error) {
        console.error('Error fetching departments:', error);
      }
    };
    
    fetchDepartments();
  }, []);
  // Fetch function for pagination
  const fetchDirectories = useCallback(async (params: any) => {
    const response = await apiClient.get('/api/directory', { 
      params: { ...params, populate: 'createdBy,department', departmentId }
    });
    return response.data;  }, [departmentId]);

  const {
    data: directories,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Directory>(fetchDirectories, {    sortBy: 'name',
    sortOrder: 'asc'
  });

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'designation', label: 'Designation' },
    { value: 'email', label: 'Email' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  const validateDirectory = () => {
    if (!formData.designation) {
      notifications.show({
        title: 'Validation Error',
        message: 'Member designation is required',
        color: 'red',
      });
      return false;
    }
    if (!formData.name) {
      notifications.show({
        title: 'Validation Error',
        message: 'Member name is required',
        color: 'red',
      });
      return false;
    }
    // if (!formData.email) {
    //   notifications.show({
    //     title: 'Validation Error',
    //     message: 'Member email is required',
    //     color: 'red',
    //   });
    //   return false;
    // }
    // if (!formData.phone) {
    //   notifications.show({
    //     title: 'Validation Error',
    //     message: 'Phone number is required',
    //     color: 'red',
    //   });
    //   return false;
    // }
    return true;
  };

  const handleCreateDirectory = async () => {
    if (!validateDirectory()) return;

    try {
      await apiClient.post('/api/directory', formData);
      notifications.show({
        title: 'Success',
        message: 'Directory entry created successfully',
        color: 'green',
      });
      setIsCreateModalOpen(false);
      refresh();
      resetForm();
    } catch (error) {
      console.error('Failed to create directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to create directory entry',
        color: 'red',
      });
    }
  };

  const handleEditDirectory = async () => {
    if (!validateDirectory() || !currentDirectory) return;

    try {
      await apiClient.patch(`/api/directory/${currentDirectory._id}`, formData);
      notifications.show({
        title: 'Success',
        message: 'Directory entry updated successfully',
        color: 'green',
      });
      setIsEditModalOpen(false);
      refresh();
      resetForm();
    } catch (error) {
      console.error('Failed to update directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update directory entry',
        color: 'red',
      });
    }
  };

  const handleDeleteDirectory = async (directory: Directory) => {
    try {
      const response = await apiClient.get(`/api/directory/${directory._id}/usage`);
      setDirectoryUsage(response.data);
      setDeletingDirectory(directory);
      setIsDeleteModalOpen(true);
    } catch (error) {
      console.error('Failed to fetch directory usage:', error);
    }
  };

  const confirmDeleteDirectory = async () => {
    if (!deletingDirectory) return;

    try {
      await apiClient.delete(`/api/directory/${deletingDirectory._id}`);
      notifications.show({
        title: 'Success',
        message: 'Directory entry deleted successfully',
        color: 'green',
      });
      setIsDeleteModalOpen(false);
      setDeletingDirectory(null);
      setDirectoryUsage(null);
      refresh();
    } catch (error) {
      console.error('Failed to delete directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete directory entry',
        color: 'red',
      });
    }
  };
  const resetForm = () => {
    setFormData({
      name: '',
      designation: '',
      description: '',
      email: '',
      phone: '',
      address: '',
      department: '',
    });
    setCurrentDirectory(null);
  };
  const handleEditClick = (directory: Directory) => {
    setFormData({
      name: directory.name,
      designation: directory.designation,
      description: directory.description || '',
      email: directory.email || '',
      phone: directory.phone || '',
      address: directory.address || '',
      department: directory.department ? directory.department._id : '',
    });
    setCurrentDirectory(directory);
    setIsEditModalOpen(true);
  };

  
  function titleCasePreserveCaps(str: string) {
    const stopWords = new Set(['or', 'and', 'in', 'the', 'of', 
      'for', 'to', 'a', 'an', 'is', 'at', 'on', 'by', 'with', 'from', 'as', 'but', 'into', 'over', 'under', 'between', 'through', 'during', 'without', 'before', 'after', 'since', 'until', 'while', 'till', 'among', 'within', 'upon', 'against', 'amongst', 'beyond', 'behind', 'below', 'above', 'across', 'along', 'around', 'beside', 'between', 'beneath', 'besides', 'during', 'except', 'inside', 'near', 'outside', 'since', 'through', 'toward', 'under', 'underneath', 'until', 'upon', 'versus', 'via', 'with', 'without', 'yet', 'nor', 'yet', 'yet'
    ]);
    const words = str.split(' ');
    return words.map((word: string, idx: number) => {
      // Leave fully capitalized words (like SUOF) as-is
      if (word === word.toUpperCase()) return word;

      // Check for stop word (lowercased match) and skip if not first or last
      const lowerWord = word.toLowerCase();
      if (stopWords.has(lowerWord) && idx !== 0 && idx !== words.length - 1) {
        return lowerWord;
      }

      // Capitalize first letter, keep rest as-is
      return word.charAt(0).toUpperCase() + word.slice(1);
    }).join(' ');
  }

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title>Directory</Title>
          <Text c="dimmed">Manage committee members and contacts</Text>
        </div>
        <Button
          leftSection={<IconPlus size={14} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Create Member
        </Button>
      </Group>

      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>      {loading ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Loader />
            <Text>Loading directory...</Text>
          </Stack>
        </Paper>
      ) : directories?.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No directory entries found</Text>
            <Button
              variant="light"
              onClick={() => setIsCreateModalOpen(true)}
            >
              Add your first member
            </Button>
          </Stack>
        </Paper>
      ) : (
        <Grid>
          {directories?.map((directory: Directory) => (
            <Grid.Col key={directory._id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Stack gap="1">
                    <Text size="sm" c="dimmed">
                      {titleCasePreserveCaps(directory.designation)}
                    </Text>
                    <Text fw={500} size="lg">
                      {directory.name}
                    </Text>
                  </Stack>
                  <Group gap="xs" wrap="nowrap">
                    <ActionIcon
                      variant="light"
                      color="blue"
                      onClick={() => handleEditClick(directory)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="light"
                      color="red"
                      onClick={() => handleDeleteDirectory(directory)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>

                {directory.description && (
                  <Text size="sm" mb="md">
                    {directory.description}
                  </Text>
                )}                <Stack gap="xs">
                  {directory.department && (
                    <Badge color="blue" size="sm" mb={5}>
                      {directory.department.name}
                    </Badge>
                  )}
                  {directory.email && (
                    <Group gap="xs" align="flex-start" wrap="nowrap">
                      <IconMail size={16} style={{ flexShrink: 0, marginTop: 2 }} />
                      <Text size="sm">{directory.email}</Text>
                    </Group>
                  )}
                  {directory.phone && (
                    <Group gap="xs" align="flex-start" wrap="nowrap">
                      <IconPhone size={16} style={{ flexShrink: 0, marginTop: 2 }} />
                      <Text size="sm">{directory.phone}</Text>
                    </Group>
                  )}
                  {directory.address && (
                    <Group gap="xs" align="flex-start" wrap="nowrap">
                      <IconMapPin size={16} style={{ flexShrink: 0, marginTop: 2 }} />
                      <Text size="sm">{directory.address}</Text>
                    </Group>
                  )}
                  
                  <Text size="xs" c="dimmed">
                    Created by {typeof directory.createdBy === 'object' ? directory.createdBy.name : 'Unknown'} on {new Date(directory.createdAt).toLocaleDateString()}
                  </Text>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      )}

      {/* Create Directory Modal */}
      <Modal
        opened={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title="Add New Member"
        size="lg"
      >        <DirectoryForm
          formData={formData}
          setFormData={setFormData}
          departments={departments}
          onSubmit={handleCreateDirectory}
          onCancel={() => {
            setIsCreateModalOpen(false);
            resetForm();
          }}
          title="Create Member"
          departmentId={departmentId}
        />
      </Modal>

      {/* Edit Directory Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          resetForm();
        }}
        title="Edit Contact"
        size="lg"
      >        <DirectoryForm
          formData={formData}
          setFormData={setFormData}
          departments={departments}
          onSubmit={handleEditDirectory}
          onCancel={() => {
            setIsEditModalOpen(false);
            resetForm();
          }}
          title="Save Changes"
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeletingDirectory(null);
          setDirectoryUsage(null);
        }}
        title="Delete Directory Entry"
        size="md"
      >
        <Stack>
          <Alert
            color={directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? "orange" : "red"}
            title={directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? "Warning: Person is in use" : "Confirm deletion"}
          >
            You are about to delete <strong>"{deletingDirectory?.name}"</strong> from the directory.
          </Alert>

          {directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? (
            <Stack>
              <Text size="sm" fw={500} c="orange">
                This person is currently being used in the following places:
              </Text>

              {directoryUsage.committees.length > 0 && (
                <div>
                  <Text size="sm" fw={500} mb="xs">Committee Member in:</Text>
                  <List size="sm" spacing="xs">
                    {directoryUsage.committees.map((committee, index) => (
                      <List.Item key={index}>{committee}</List.Item>
                    ))}
                  </List>
                </div>
              )}

              {directoryUsage.isAssistant.length > 0 && (
                <div>
                  <Text size="sm" fw={500} mb="xs">Committee Assistant for:</Text>
                  <List size="sm" spacing="xs">
                    {directoryUsage.isAssistant.map((committee, index) => (
                      <List.Item key={index}>{committee}</List.Item>
                    ))}
                  </List>
                </div>
              )}
              <Alert color="orange">
                <Text size="sm">
                  <strong>If you proceed:</strong>
                </Text>
                <List size="sm" spacing="xs" mt="xs">
                  <List.Item>This person will be removed from all committees</List.Item>
                  <List.Item>Any committee where they are the assistant will have no assistant</List.Item>
                  <List.Item>Their task assignments may be affected</List.Item>
                  <List.Item>This action cannot be undone</List.Item>
                </List>
              </Alert>
            </Stack>
          ) : (
            <Text size="sm" c="dimmed">
              This person is not currently assigned to any committees. They can be safely deleted.
            </Text>
          )}

          <Group justify="flex-end" mt="lg">
            <Button
              variant="light"
              onClick={() => {
                setIsDeleteModalOpen(false);
                setDeletingDirectory(null);
                setDirectoryUsage(null);
              }}
            >
              Cancel
            </Button>
            <Button
              color={directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? "orange" : "red"}
              onClick={confirmDeleteDirectory}
            >
              {directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0)
                ? "Delete Anyway"
                : "Delete Entry"
              }
            </Button>
          </Group>
        </Stack>
      </Modal>    </Container>
  );
}

// Wrap the component with ErrorBoundary
export default function DirectoryPageWithErrorBoundary() {
  return (
    <ErrorBoundary>
      <DirectoryPage />
    </ErrorBoundary>
  );
}

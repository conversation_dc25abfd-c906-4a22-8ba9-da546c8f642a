import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Committee from '../src/models/committee.js';
import Meeting from '../src/models/meeting.js';
import Task from '../src/models/task.js';
import Directory from '../src/models/directory.js';
import User from '../src/models/user.js';
import { faker } from '@faker-js/faker';
import colors from 'colors';

dotenv.config();

// Constants
const NUM_MEETINGS_PER_COMMITTEE = 3;  // Number of meetings to generate per committee
const NUM_TASKS_PER_MEETING = 5;       // Number of tasks to generate per meeting

// Priority distribution: 20% high, 50% medium, 30% low
const PRIORITY_DISTRIBUTION = [
  { value: 'high', weight: 20 },
  { value: 'medium', weight: 50 },
  { value: 'low', weight: 30 }
];

// Status distribution: 60% pending, 40% completed
const STATUS_DISTRIBUTION = [
  { value: 'pending', weight: 60 },
  { value: 'completed', weight: 40 }
];

// Meeting status distribution
const MEETING_STATUS_DISTRIBUTION = [
  { value: 'scheduled', weight: 30 },
  { value: 'in-progress', weight: 20 },
  { value: 'completed', weight: 40 },
  { value: 'cancelled', weight: 10 }
];

// Helper functions
function getWeightedRandomItem(items) {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of items) {
    random -= item.weight;
    if (random < 0) {
      return item.value;
    }
  }
  
  return items[0].value; // Fallback
}

function getRandomDate(startDate, endDate) {
  return new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));
}

function getRandomStartAndEndDate(startDate, endDate, durationHours = 1) {
  const randomStartDate = getRandomDate(startDate, endDate);
  const randomEndDate = new Date(randomStartDate.getTime() + durationHours * 60 * 60 * 1000);
  return { startDate: randomStartDate, endDate: randomEndDate };
}

function getRandomFutureDate(minDays = 0, maxDays = 30) {
  const now = new Date();
  const minDate = new Date(now);
  minDate.setDate(now.getDate() + minDays);
  
  const maxDate = new Date(now);
  maxDate.setDate(now.getDate() + maxDays);
  
  return getRandomDate(minDate, maxDate);
}

function getRandomPastDate(minDays = 1, maxDays = 30) {
  const now = new Date();
  const minDate = new Date(now);
  minDate.setDate(now.getDate() - maxDays);
  
  const maxDate = new Date(now);
  maxDate.setDate(now.getDate() - minDays);
  
  return getRandomDate(minDate, maxDate);
}

function getRandomDeadline(meetingStartDate, completed) {
  // If task is completed, deadline is in the past
  // If task is pending, deadline is in the future
  const now = new Date();
  
  if (completed) {
    // For completed tasks, set deadline to sometime between meeting date and now
    return getRandomDate(meetingStartDate, now);
  } else {
    // For pending tasks, set deadline to near future from now
    return getRandomFutureDate(1, 30);
  }
}

function getTaskTitle() {
  const actions = [
    'Create', 'Develop', 'Prepare', 'Publish', 'Review', 'Update',
    'Analyze', 'Implement', 'Test', 'Deploy', 'Document', 'Present',
    'Research', 'Evaluate', 'Coordinate', 'Finalize', 'Schedule'
  ];
  
  const subjects = [
    'project report', 'budget proposal', 'monthly summary', 'action plan',
    'training program', 'quarterly review', 'strategic plan', 'user flow',
    'UI/UX design', 'database schema', 'API documentation', 'security policy',
    'marketing campaign', 'revenue forecast', 'content strategy', 'press release'
  ];
  
  return `${faker.helpers.arrayElement(actions)} ${faker.helpers.arrayElement(subjects)}`;
}

function getMeetingTitle(committeeType) {
  const meetingTypes = {
    'finance': [
      'Budget Approval', 'Quarterly Financial Review', 'Investment Strategy',
      'Expense Analysis', 'Revenue Projection', 'Financial Policy Update',
      'Annual Budget Planning', 'Audit Committee'
    ],
    'academic': [
      'Curriculum Review', 'Faculty Assessment', 'Student Performance Analysis',
      'Educational Policy Update', 'Teaching Standards', 'Academic Calendar Planning',
      'Degree Program Review', 'Research Funding Discussion'
    ],
    'general': [
      'Strategic Planning', 'Annual Review', 'Policy Update', 
      'Quarterly Review', 'Monthly Planning', 'Progress Assessment',
      'Team Alignment', 'Status Update'
    ]
  };
  
  let titles;
  if (committeeType && meetingTypes[committeeType.toLowerCase()]) {
    titles = meetingTypes[committeeType.toLowerCase()];
  } else {
    titles = meetingTypes.general;
  }
  
  return faker.helpers.arrayElement(titles) + ' Meeting';
}

function determineCommitteeType(committeeName) {
  const name = committeeName.toLowerCase();
  
  if (name.includes('finance') || name.includes('budget') || name.includes('audit')) {
    return 'finance';
  } else if (name.includes('academic') || name.includes('education') || name.includes('curriculum')) {
    return 'academic';
  } else {
    return 'general';
  }
}

// Main seeding function
async function seedSampleMeetingsAndTasks() {
  console.log(colors.yellow('Starting to seed sample meetings and tasks...'));
  
  try {
    // Connect to the database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log(colors.green('Connected to MongoDB'));
    
    // First, clean up existing data
    console.log(colors.yellow('Cleaning up old meetings and tasks...'));
    
    const meetings = await Meeting.find({});
    console.log(`Found ${meetings.length} existing meetings`);
    
    // Clean up tasks first (they reference meetings)
    const deletedTasks = await Task.deleteMany({ 
      meeting: { $in: meetings.map(m => m._id) } 
    });
    console.log(`Deleted ${deletedTasks.deletedCount} existing tasks`);
    
    // Clean up meetings
    const deletedMeetings = await Meeting.deleteMany({});
    console.log(`Deleted ${deletedMeetings.deletedCount} existing meetings`);
    
    // Fetch all committees
    const committees = await Committee.find({});
    console.log(`Found ${committees.length} committees to create meetings for`);
    
    if (committees.length === 0) {
      console.log(colors.red('No committees found! Please run seed-committees script first.'));
      return;
    }
    
    // Fetch all users for createdBy field
    const users = await User.find({});
    if (users.length === 0) {
      console.log(colors.red('No users found! Please create at least one user first.'));
      return;
    }
    const defaultUser = users[0];
    
    // For each committee, create meetings and tasks
    let totalMeetingsCreated = 0;
    let totalTasksCreated = 0;
    
    for (const committee of committees) {
      console.log(colors.cyan(`Creating meetings for committee: ${committee.name}`));
      
      // Determine committee type for contextually appropriate meeting titles
      const committeeType = determineCommitteeType(committee.name);
      
      // Get committee members to assign as meeting attendees and task assignees
      const members = committee.members;
      
      for (let i = 0; i < NUM_MEETINGS_PER_COMMITTEE; i++) {
        // Generate meeting dates based on position (past, present, future)
        let meetingDates;
        let meetingStatus;
        
        if (i === 0) {
          // Past meeting (completed)
          meetingDates = {
            startDate: getRandomPastDate(30, 60),
            endDate: getRandomPastDate(15, 29)
          };
          meetingStatus = 'completed';
        } else if (i === 1) {
          // Recent/current meeting (in-progress or recently completed)
          meetingDates = {
            startDate: getRandomPastDate(7, 14),
            endDate: getRandomFutureDate(0, 7)
          };
          meetingStatus = new Date() > meetingDates.endDate ? 'completed' : 'in-progress';
        } else {
          // Future meeting (scheduled)
          meetingDates = {
            startDate: getRandomFutureDate(14, 60),
            endDate: getRandomFutureDate(61, 90)
          };
          meetingStatus = 'scheduled';
        }
        
        // Create meeting
        const meeting = new Meeting({
          title: getMeetingTitle(committeeType),
          description: faker.lorem.paragraph(),
          committee: committee._id,
          startDate: meetingDates.startDate,
          endDate: meetingDates.endDate,
          status: meetingStatus,
          attendees: members,
          createdBy: defaultUser._id,
        });
        
        await meeting.save();
        totalMeetingsCreated++;
        
        // Create tasks for this meeting
        for (let j = 0; j < NUM_TASKS_PER_MEETING; j++) {
          // Determine status
          const taskStatus = getWeightedRandomItem(STATUS_DISTRIBUTION);
          const isCompleted = taskStatus === 'completed';
          
          // Assign to random committee members (1 to 3)
          const numAssignees = Math.floor(Math.random() * 3) + 1;
          const assignedTo = members.length > 0 
            ? faker.helpers.arrayElements(members, Math.min(numAssignees, members.length))
            : [];
            
          // Create task
          const task = new Task({
            title: getTaskTitle(),
            description: faker.lorem.paragraph(),
            priority: getWeightedRandomItem(PRIORITY_DISTRIBUTION),
            status: taskStatus,
            meeting: meeting._id,
            assignedTo: assignedTo.map(m => m._id),
            deadline: getRandomDeadline(meetingDates.startDate, isCompleted)
          });
          
          await task.save();
          totalTasksCreated++;
          
          // Add the task to the meeting's tasks array
          meeting.tasks = meeting.tasks || [];
          meeting.tasks.push(task._id);
        }
        
        // Save the meeting again with the tasks
        await meeting.save();
        
        console.log(`  Created meeting: ${meeting.title} (${meeting.status}) with ${NUM_TASKS_PER_MEETING} tasks`);
      }
    }
    
    console.log(colors.green(`Successfully created ${totalMeetingsCreated} meetings with ${totalTasksCreated} tasks`));
    
  } catch (error) {
    console.error(colors.red('Error seeding sample meetings and tasks:'), error);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log(colors.yellow('Database connection closed'));
  }
}

// Run the seeding function
seedSampleMeetingsAndTasks();

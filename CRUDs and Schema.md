## CRUDs and Schema
# Directory (authority):
  - name: text
  - designation: text
  - description: text
  - email: text
  - phone: text
  - address: text
  - createdBy: User
  - createdAt: date

# Committee:
  - name: text
  - description: text
  - members: Directory[]
  - meetingFrequency: time_period
  - startDate: date
  - assistant: Committee.member[*]
  - createdBy: User
  - createdAt: date

# Task:
  schema already defined

# Meeting:
  - title: text
  - committee: Comittee
  - agenda: text
  - startDate: date

# User:
  - name: text
  - role: 1 | 2 (1 = Deputy Comissioner, 2 = DC Assistant)

## Flow
  - `User` will create a comittee and add members (along with an assistant from members)
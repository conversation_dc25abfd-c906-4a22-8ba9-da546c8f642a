import Committee from "../models/committee.js";
import ExcelJS from "exceljs";
import mongoose from "mongoose";
import multer from "multer";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";
import Meeting from "../models/meeting.js";
import { analyzeAttendanceSheetWithAI, generateSimulatedAttendanceResults } from "../services/gemini.js";

// Generate attendance sheet for a committee
export const generateAttendanceSheet = async (req, res) => {
  try {
    const { committeeId } = req.params;
    
    // Validate committeeId
    if (!mongoose.Types.ObjectId.isValid(committeeId)) {
      return res.status(400).json({ message: "Invalid committee ID format" });
    }
    
    // Get committee with members - use lean() to reduce memory usage
    const committee = await Committee.findById(committeeId)
      .populate({
        path: "members",
        select: "name designation email phone"
      })
      .lean();
    
    if (!committee) {
      return res.status(404).json({ message: "Committee not found" });
    }
    
    // Create a new Excel workbook
    const workbook = new ExcelJS.Workbook();
    
    // Add metadata
    workbook.creator = "Tasks Scheduler";
    workbook.lastModifiedBy = "Tasks Scheduler";
    workbook.created = new Date();
    workbook.modified = new Date();
    
    // Add a worksheet
    const worksheet = workbook.addWorksheet("Attendance Sheet");
    
    // Add header with committee information
    worksheet.mergeCells('A1:E1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = `${committee.name} - Attendance Sheet`;
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };
    
    // Add column headers - define columns first without headers
    worksheet.columns = [
      { key: 'srNo', width: 8 },
      { key: 'designation', width: 25 },
      { key: 'contactInfo', width: 30 }, // Wider column for combined info
      { key: 'signature', width: 20 },
      { key: 'correspondent', width: 20 }
    ];
    
    // Add header row separately for better control
    const headerRow = worksheet.addRow([
      'Sr.No.', 
      'Designation', 
      'Member Information', 
      'Signature (Self)',
      'Signature (Correspondent)'
    ]);
    
    // Style the header row
    headerRow.font = { bold: true };
    headerRow.alignment = { horizontal: 'center' };
    
    // Add data rows - process members in batches if needed
    const members = committee.members || [];
    members.forEach((member, index) => {
      // Create combined contact info with name, email and phone
      const contactInfo = `${member.name}\n${member.email || 'N/A'}\n${member.phone || 'N/A'}`;
      
      worksheet.addRow([
        index + 1,
        member.designation || 'N/A',
        contactInfo,
        '',  // Empty cell for signature
        ''   // Empty cell for correspondent
      ]);
    });
    
    // Style all cells - but be more selective about styling to reduce memory usage
    // Start from row 3 since row 1 is title and row 2 is now the headers
    worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        if (rowNumber > 2) {
          cell.alignment = { vertical: 'middle', horizontal: 'left' };
        }
      });
    });
    
    // Make rows taller for all data rows to accommodate contact info and signature/correspondent
    const lastRow = Math.min(worksheet.rowCount + 1, 1000); // Avoid excessive iterations
    // Start from row 3 (data rows after header)
    for (let i = 3; i <= lastRow; i++) {
      worksheet.getRow(i).height = 60; // Taller to fit 3 lines of text
      
      // Format contact info cell for multi-line display
      const contactInfoCell = worksheet.getCell(`C${i}`);
      contactInfoCell.alignment = { vertical: 'middle', wrapText: true };
      
      // Make the signature column cells bigger for writing
      const signatureCell = worksheet.getCell(`D${i}`);
      signatureCell.alignment = { vertical: 'middle', horizontal: 'center' };
      
      // Make the correspondent column cells bigger for writing
      const correspondentCell = worksheet.getCell(`E${i}`);
      correspondentCell.alignment = { vertical: 'middle', horizontal: 'center' };
    }
    
    // Set the content type and headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="attendance_sheet_${committee.name.replace(/[^a-zA-Z0-9]/g, '_')}.xlsx"`);
    
    // Write to response stream with proper error handling
    try {
      await workbook.xlsx.write(res);
      res.end();
    } catch (writeError) {
      console.error('Error writing Excel file to response:', writeError);
      // Only send error if headers haven't been sent yet
      if (!res.headersSent) {
        res.status(500).json({ message: "Error generating Excel file" });
      } else {
        res.end();
      }
    }
  } catch (error) {
    console.error('Error generating attendance sheet:', error);
    if (!res.headersSent) {
      res.status(500).json({ message: error.message });
    } else {
      res.end();
    }
  }
};

// Configure storage for attendance sheet uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(process.cwd(), "uploads/"));
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// File filter to allow images and pdfs
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image/") || file.mimetype === "application/pdf") {
    cb(null, true);
  } else {
    cb(new Error("Only image and PDF files are allowed"), false);
  }
};

export const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1 // Only allow 1 file at a time
  },
  fileFilter
});

// Analyze attendance sheet wrapper function that handles committee data fetching
const analyzeAttendanceSheet = async (filePath, mimetype, committeeId) => {
  try {
    // Get committee members for reference
    const committee = await Committee.findById(committeeId)
      .populate('members', 'name designation')
      .lean();
    
    if (!committee || !committee.members) {
      return { success: false, message: "Could not retrieve committee members for comparison" };
    }
    
    console.log(`Analyzing attendance sheet: ${filePath} (${mimetype})`);
    
    try {
      // Call the AI service function from gemini.js
      return await analyzeAttendanceSheetWithAI(filePath, mimetype, committee.members);
    } catch (aiError) {
      console.error('AI analysis error:', aiError);
      
      // Fallback to simulated results if AI analysis fails
      return generateSimulatedAttendanceResults(committee.members);
    }
    
  } catch (error) {
    console.error('Error analyzing attendance sheet:', error);
    return {
      success: false,
      message: `Attendance analysis failed: ${error.message}`
    };
  }
};

// Upload attendance sheet for a meeting
export const uploadAttendanceSheet = async (req, res) => {
  try {
    const { meetingId } = req.params;
    
    // Validate meetingId
    if (!mongoose.Types.ObjectId.isValid(meetingId)) {
      return res.status(400).json({ message: "Invalid meeting ID format" });
    }
    
    if (!req.file) {
      return res.status(400).json({ message: "No file provided" });
    }
    
    // Find the meeting with committee info for AI analysis
    const meeting = await Meeting.findById(meetingId).lean();
    if (!meeting) {
      // Delete the uploaded file if meeting doesn't exist
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ message: "Meeting not found" });
    }
    
    // Create file metadata
    const fileData = {
      filename: req.file.filename,
      originalname: req.file.originalname,
      path: req.file.path,
      mimetype: req.file.mimetype,
      size: req.file.size,
      uploadedAt: new Date(),
      isAttendanceSheet: true, // Mark as attendance sheet
      analyzed: false,
      analysisResults: null
    };
    
    // Perform AI analysis on the uploaded attendance sheet
    const analysisResults = await analyzeAttendanceSheet(
      req.file.path,
      req.file.mimetype,
      meeting.committee // Pass the committee ID for member reference
    );
    
    // Add analysis results to file data
    if (analysisResults.success) {
      fileData.analyzed = true;
      fileData.analysisResults = analysisResults.attendanceResults;
      fileData.aiAnalysis = {
        usedFallback: !!analysisResults.fallback,
        aiError: analysisResults.aiError || null,
        message: analysisResults.message
      };
      
      // Update meeting's attendees based on analysis
      // Map the directory IDs from committee members to meeting attendees
      const presentMemberIds = analysisResults.attendanceResults
        .filter(result => result.present)
        .map(result => result.memberId);
      
      // Update attendance in the meeting document
      await Meeting.findByIdAndUpdate(
        meetingId,
        { 
          $push: { documents: fileData },
          $set: { attendees: presentMemberIds }
        }
      );
      
      // Return response with analysis results
      return res.status(200).json({
        message: analysisResults.fallback 
          ? "Attendance sheet uploaded, but AI analysis fell back to simulation" 
          : "Attendance sheet uploaded and analyzed successfully with AI",
        file: {
          filename: fileData.filename,
          originalname: fileData.originalname,
          uploadedAt: fileData.uploadedAt
        },
        analysis: {
          analyzed: true,
          usedAI: true,
          usedFallback: !!analysisResults.fallback,
          aiError: analysisResults.aiError || null,
          attendanceCount: presentMemberIds.length,
          totalMembers: analysisResults.attendanceResults.length,
          attendanceResults: analysisResults.attendanceResults
        }
      });
    } else {
      // If analysis failed completely, still save the document but mark as not analyzed
      await Meeting.updateOne(
        { _id: meetingId },
        { $push: { documents: fileData } }
      );
      
      return res.status(200).json({
        message: "Attendance sheet uploaded but analysis failed",
        file: {
          filename: fileData.filename,
          originalname: fileData.originalname,
          uploadedAt: fileData.uploadedAt
        },
        analysis: {
          analyzed: false,
          error: analysisResults.message
        }
      });
    }
    
  } catch (error) {
    console.error('Error uploading attendance sheet:', error);
    // Clean up the file if there was an error
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkError) {
        console.error('Error deleting file:', unlinkError);
      }
    }
    res.status(500).json({ message: error.message });
  }
};

// Get attendance sheet for a meeting
export const getAttendanceSheet = async (req, res) => {
  try {
    const { meetingId } = req.params;
    
    // Validate meetingId
    if (!mongoose.Types.ObjectId.isValid(meetingId)) {
      return res.status(400).json({ message: "Invalid meeting ID format" });
    }
    
    // Use projection to only retrieve the documents and attendees arrays
    const meeting = await Meeting.findById(meetingId, { documents: 1, attendees: 1 })
      .populate('attendees', 'name')
      .lean();
    
    if (!meeting) {
      return res.status(404).json({ message: "Meeting not found" });
    }
    
    // Find attendance sheet document
    const attendanceSheet = meeting.documents?.find(doc => doc.isAttendanceSheet);
    
    if (!attendanceSheet) {
      return res.status(404).json({ message: "Attendance sheet not found for this meeting" });
    }
    
    // Include attendance information with the response
    const response = {
      ...attendanceSheet,
      attendees: meeting.attendees || [],
      attendeeCount: meeting.attendees?.length || 0
    };
    
    res.json(response);
    
  } catch (error) {
    console.error('Error getting attendance sheet:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get attendance analysis for a meeting
export const getAttendanceAnalysis = async (req, res) => {
  try {
    const { meetingId } = req.params;
    
    // Validate meetingId
    if (!mongoose.Types.ObjectId.isValid(meetingId)) {
      return res.status(400).json({ message: "Invalid meeting ID format" });
    }
    
    // Get meeting with committee information
    const meeting = await Meeting.findById(meetingId)
      .populate({
        path: 'committee',
        populate: {
          path: 'members',
          select: 'name designation'
        }
      })
      .populate('attendees', 'name designation')
      .lean();
    
    if (!meeting) {
      return res.status(404).json({ message: "Meeting not found" });
    }
    
    // Find attendance sheet document
    const attendanceSheet = meeting.documents?.find(doc => doc.isAttendanceSheet);
    
    if (!attendanceSheet) {
      return res.status(404).json({ message: "Attendance sheet not found for this meeting" });
    }
    
    // Get committee members and current attendees
    const committeeMembers = meeting.committee?.members || [];
    const attendees = meeting.attendees || [];
    
    // Create a comprehensive attendance report
    const attendanceReport = committeeMembers.map(member => {
      const isAttending = attendees.some(attendee => 
        attendee._id.toString() === member._id.toString()
      );
      
      // Find analysis result if available
      let signatureInfo = null;
      if (attendanceSheet.analysisResults) {
        const analysis = attendanceSheet.analysisResults.find(result => 
          result.memberId.toString() === member._id.toString()
        );
        
        if (analysis) {
          signatureInfo = {
            present: analysis.present,
            signatureType: analysis.signatureType,
            confidence: analysis.confidence
          };
        }
      }
      
      return {
        memberId: member._id,
        name: member.name,
        designation: member.designation,
        present: isAttending,
        signatureInfo: signatureInfo
      };
    });
    
    // Return attendance report with metadata
    res.json({
      meetingId: meeting._id,
      meetingTitle: meeting.title,
      committee: meeting.committee?.name,
      attendanceCount: attendees.length,
      totalMembers: committeeMembers.length,
      attendancePercentage: committeeMembers.length > 0 
        ? Math.round((attendees.length / committeeMembers.length) * 100) 
        : 0,
      attendanceSheet: {
        filename: attendanceSheet.filename,
        originalname: attendanceSheet.originalname,
        uploadedAt: attendanceSheet.uploadedAt,
        analyzed: attendanceSheet.analyzed || false,
        aiAnalysis: attendanceSheet.aiAnalysis || {
          usedFallback: false,
          aiError: null,
          message: "No AI analysis data available"
        }
      },
      attendanceReport
    });
    
  } catch (error) {
    console.error('Error getting attendance analysis:', error);
    res.status(500).json({ message: error.message });
  }
};

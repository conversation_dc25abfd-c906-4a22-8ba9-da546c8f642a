import mongoose from "mongoose";

const committeeSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  department: { type: mongoose.Schema.Types.ObjectId, ref: "Department" },
  members: [{ type: mongoose.Schema.Types.ObjectId, ref: "Directory" }],
  meetingFrequency: { type: String, required: true },
  lastMeetingDate: { type: Date },
  startDate: { type: Date }, // No longer required, will be generated based on meeting frequency
  assistant: { type: mongoose.Schema.Types.ObjectId, ref: "Directory" },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  createdAt: { type: Date, default: Date.now }
});

export default mongoose.model("Committee", committeeSchema);

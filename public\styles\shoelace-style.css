/* Minimal styles using Shoelace variables */
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--sl-font-sans);
  color: var(--sl-color-neutral-900);
  background-color: var(--sl-color-neutral-0);
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.app-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: #fff;
}

.page {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: var(--sl-border-radius-medium);
  padding: var(--sl-spacing-large);
  display: flex;
  flex-direction: column;
  margin-bottom: 60px;
  /* Space for footer */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Footer styles */
#slide-footer {
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

/* Modern sidebar styles */
aside {
  background-color: #f8fafc;
  box-shadow: 1px 0 5px rgba(0, 0, 0, 0.05);
}

aside h2 {
  color: #333;
  font-weight: 600;
}

/* Muuri grid styles */
.muuri-grid {
  position: relative;
  height: auto;
  /* Allow grid to expand with content */
  box-sizing: border-box;
}

.muuri-item {
  display: block;
  position: absolute;
  width: 100% !important;
  z-index: 1;
  box-sizing: border-box;
}

.muuri-item.muuri-item-dragging {
  z-index: 3;
}

.muuri-item.muuri-item-releasing {
  z-index: 2;
}

.muuri-item.muuri-item-hidden {
  z-index: 0;
}

.muuri-item-content {
  position: relative;
  width: 100% !important;
  cursor: move;
  transition: all 0.2s ease;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.muuri-item-content:hover {
  background-color: #f0f4f8;
}

.muuri-item-selected .muuri-item-content {
  background-color: #e6f0ff;
}

.muuri-item-content::before {
  content: "";
  position: absolute;
  top: 0%;
  right: 0px;
  width: 0px;
  height: 100%;
  background-color: var(--sl-color-primary-500);
  /* border-radius: 0px 0.5rem 0.5rem 0px; */
}

.muuri-item-selected .muuri-item-content::before {
  width: 0.15rem;
}

.slide-number {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin: 0px 0.25rem;
  width: 24px;
  font-size: 13px;
  font-weight: 500;
  color: var(--sl-color-neutral-400);
  text-align: center;
}

.muuri-item-selected .slide-number {
  color: #1677ff;
  font-weight: 600;
}

.preview-container {
  flex: 1;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  margin-left: 4px;
  height: 80px;
  /* Fixed height */
  width: 106px;
  /* Fixed width based on 16:9 aspect ratio */
  position: relative;
}

.muuri-item-selected .preview-container {
  border-color: var(--sl-color-primary-500);
}

.slide-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 8px;
}

.muuri-item-content:hover .slide-actions {
  opacity: 1;
}

.ce-block__content,
.ce-toolbar__content {
  max-width: calc(100% - 160px) !important;
}

.cdx-block {
  max-width: 100% !important;
}

/* Fix for EditorJS toolbar - this is needed for functionality */
.ce-toolbar__actions--opened {
  z-index: 2;
}

/* Improve EditorJS toolbar styling */
.ce-toolbar__plus,
.ce-toolbar__settings-btn {
  color: #1677ff;
}

.ce-inline-toolbar {
  border-radius: 6px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
  overflow: visible !important;
}

.ce-inline-toolbar__dropdown {
  overflow: visible !important;
}

.ce-inline-tool-container {
  overflow: visible !important;
}

.ce-inline-tool {
  border-radius: 4px;
  margin: 0 2px;
}

.ce-inline-tool--active {
  background-color: #e6f0ff;
  color: #1677ff;
}

.ce-conversion-toolbar {
  border-radius: 6px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.ce-conversion-tool {
  border-radius: 4px;
}

.ce-conversion-tool--active {
  background-color: #e6f0ff;
  color: #1677ff;
}

.ce-conversion-tool__icon {
  margin-right: 8px;
}

/* Marker styling */
.cdx-marker {
  background: rgba(245, 235, 111, 0.29);
  padding: 3px 0;
}

/* Custom inline tools styling */
.cdx-bold {
  font-weight: bold;
}

.cdx-italic {
  font-style: italic;
}

/* Simple image tool styles */
.simple-image {
  margin: 20px 0;
}

.simple-image__image {
  max-width: 100%;
  display: block;
  margin: 0 auto;
}

.simple-image__caption {
  margin-top: 10px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.simple-image--withBorder .simple-image__image {
  border: 1px solid #e8e8eb;
  border-radius: 3px;
}

.simple-image--withBackground .simple-image__image {
  background: #f7f9fb;
  padding: 10px;
  border-radius: 3px;
}

.simple-image--stretched .simple-image__image {
  width: 100%;
}

/* Override EditorJS popover styles */
.ce-popover__items {
  overflow: visible !important;
  -ms-scroll-chaining: none;
  overscroll-behavior: contain;
}

.ce-popover__container {
  min-width: var(--width);
  width: var(--width);
  max-height: var(--max-height);
  border-radius: var(--border-radius);
  overflow: visible !important;
}

/* Shoelace toast stack styling */
.sl-toast-stack {
  right: 20px;
  top: 20px;
}

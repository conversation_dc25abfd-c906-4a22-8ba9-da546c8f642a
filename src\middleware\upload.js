import multer from "multer";
import path from "path";
import { fileURLToPath } from "url";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define uploads path
const UPLOADS_PATH = path.join(__dirname, "../../uploads");

// Create uploads directory if it doesn't exist
if (!fs.existsSync(UPLOADS_PATH)) {
  fs.mkdirSync(UPLOADS_PATH, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, UPLOADS_PATH);
  },
  filename: (req, file, cb) => {
    // Generate a unique filename with uuid + original extension
    const fileExtension = path.extname(file.originalname);
    const uniqueFilename = `${uuidv4()}${fileExtension}`;
    cb(null, uniqueFilename);
  }
});

// File filter to allow specific file types
const fileFilter = (req, file, cb) => {
  // Add more mime types as needed
  const allowedMimeTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
    "image/jpeg",
    "image/png",
    "image/gif"
  ];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error("Unsupported file type"), false);
  }
};

// Create upload middleware with file size limit of 10MB
const upload = multer({
  storage,
  fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB
});

// Debug middleware to log file uploads
const debugUpload = (fieldname) => {
  return (req, res, next) => {
    console.log(`Starting file upload middleware for field: ${fieldname}`);
    console.log(`Request headers:`, req.headers);
    
    // Use multer directly instead of through the wrapper
    const originalUpload = multer({ 
      storage, 
      fileFilter,
      limits: { fileSize: 10 * 1024 * 1024 } // 10MB 
    }).array(fieldname, 10);
    
    originalUpload(req, res, (err) => {
      if (err) {
        console.error("File upload error:", err);
        return res.status(400).json({ message: err.message });
      }
      
      if (!req.files || req.files.length === 0) {
        console.log("No files were received in the request");
      } else {
        console.log(`${req.files.length} files received:`, req.files?.map(f => ({
          fieldname: f.fieldname,
          originalname: f.originalname,
          mimetype: f.mimetype,
          filename: f.filename,
          size: f.size,
          path: f.path
        })));
      }
      
      next();
    });
  };
};

// Export both the raw multer middleware and the debug wrapper
export { debugUpload };
export default upload;

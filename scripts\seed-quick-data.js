import "dotenv/config";
import mongoose from "mongoose";
import Department from "../src/models/department.js";
import Directory from "../src/models/directory.js";
import User from "../src/models/user.js";
import bcrypt from "bcryptjs";

// Quick test data - just a few departments and members
const quickDepartments = [
  {
    name: "Information Technology",
    description: "Technology and software development",
    code: "IT",
    head: null,
    isActive: true
  },
  {
    name: "Human Resources",
    description: "Employee relations and recruitment",
    code: "HR",
    head: null,
    isActive: true
  },
  {
    name: "Finance",
    description: "Financial management and accounting",
    code: "FIN",
    head: null,
    isActive: true
  }
];

const quickMembers = [
  {
    name: "<PERSON>",
    designation: "IT Director",
    email: "<EMAIL>",
    phone: "******-0001",
    department: "IT",
    description: "Technology leader and software architect"
  },
  {
    name: "<PERSON>",
    designation: "Software Developer",
    email: "<EMAIL>",
    phone: "******-0002",
    department: "IT",
    description: "Full-stack developer"
  },
  {
    name: "<PERSON>",
    designation: "HR Manager",
    email: "<EMAIL>",
    phone: "******-0003",
    department: "HR",
    description: "Human resources specialist"
  },
  {
    name: "<PERSON>",
    designation: "Finance Manager",
    email: "<EMAIL>",
    phone: "******-0004",
    department: "FIN",
    description: "Financial analyst and budget manager"
  }
];

async function seedQuickData() {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected!");

    // Create or get system user for createdBy field
    console.log("Creating/finding system user...");
    let systemUser = await User.findOne({ email: "<EMAIL>" });
    
    if (!systemUser) {
      const hashedPassword = await bcrypt.hash("systemPassword123", 10);
      systemUser = await User.create({
        email: "<EMAIL>",
        password: hashedPassword,
        name: "System User",
        role: 1 // Deputy Commissioner role
      });
      console.log("Created system user!");
    } else {
      console.log("System user already exists!");
    }

    // Clear existing data
    await Department.deleteMany({});
    await Directory.deleteMany({});
    console.log("Cleared existing data");

    // Create departments
    const departments = await Department.insertMany(quickDepartments);
    console.log(`Created ${departments.length} departments`);

    // Create department map
    const deptMap = {};
    departments.forEach(dept => {
      deptMap[dept.code] = dept._id;
    });

    // Create members
    const membersWithDepts = quickMembers.map(member => ({
      ...member,
      department: deptMap[member.department],
      createdBy: systemUser._id // Use the system user's ObjectId
    }));

    const members = await Directory.insertMany(membersWithDepts);
    console.log(`Created ${members.length} directory members`);

    // Assign department heads
    for (const dept of departments) {
      const head = members.find(m => 
        m.department.toString() === dept._id.toString() && 
        m.designation.toLowerCase().includes('director')
      );
      
      if (head) {
        await Department.findByIdAndUpdate(dept._id, { head: head._id });
      }
    }

    console.log("Quick sample data created successfully!");

  } catch (error) {
    console.error("Error:", error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

seedQuickData();
